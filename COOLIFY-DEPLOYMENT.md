# 🚀 TarmacTrack Deployment on Coolify (Testing Guide)

This simplified guide will help you deploy TarmacTrack on Coolify for testing purposes using TimescaleDB Cloud and Coolify's free generated domains.

## 📋 Prerequisites

- Coolify instance running on your VPS
- TimescaleDB Cloud account (free tier available)
- Git repository with TarmacTrack code

## 🗄️ Step 1: Set Up TimescaleDB Cloud

1. **Create TimescaleDB Account:**
   - Go to [TimescaleDB Cloud](https://cloud.timescale.com)
   - Sign up for a free account
   - Create a new service (free tier is fine for testing)

2. **Get Connection String:**
   - Once your service is ready, go to the "Overview" tab
   - Copy the connection string that looks like:
   ```
   *****************************************************************************/tsdb?ssl=require
   ```

3. **Enable PostGIS (Required):**
   - Click "Connect" in your TimescaleDB dashboard
   - Run this SQL command:
   ```sql
   CREATE EXTENSION IF NOT EXISTS postgis;
   ```

## 🔧 Step 2: Update Your Configuration

1. **Edit docker-compose.prod.yml:**
   - Replace the DATABASE_URL with your actual TimescaleDB connection string
   - Change the SECRET_KEY to something secure
   - **Important**: You'll need to update the API URL in TWO places for the webapp
   
   ```yaml
   api:
     environment:
       - DATABASE_URL=postgresql+asyncpg://tsdbadmin:your_actual_password@your_actual_host.tsdb.cloud.timescale.com:35750/tsdb?ssl=require
       - SECRET_KEY=your-secure-secret-key-here

   webapp:
     build:
       args:
         - NEXT_PUBLIC_API_URL=http://your-api-domain.sslip.io
     environment:
       - NEXT_PUBLIC_API_URL=http://your-api-domain.sslip.io
   ```

2. **Why Two Places?**
   - `build.args`: Next.js needs this at build time to embed the API URL
   - `environment`: Runtime environment variable (backup)

## 🌐 Step 3: Deploy in Coolify

### 3.1 Initial Deployment

1. **Create New Project:**
   - In Coolify Dashboard: "New Project" → `tarmactrack` → "Docker Compose"
   - Repository URL: `https://github.com/yourusername/tarmactrack.git`
   - Branch: `main`
   - Docker Compose file: `docker-compose.prod.yml`

2. **First Deploy:**
   - Click "Deploy" (this will fail initially - that's expected!)
   - Wait for Coolify to generate domain names for both services

3. **Get Generated Domains:**
   - After deployment, note the generated domains:
     - API: `http://random-id.your-ip.sslip.io`
     - Webapp: `http://different-id.your-ip.sslip.io`

### 3.2 Update Configuration with Real Domains

1. **Update docker-compose.prod.yml:**
   ```yaml
   webapp:
     build:
       args:
         - NEXT_PUBLIC_API_URL=http://your-actual-api-domain.sslip.io
     environment:
       - NEXT_PUBLIC_API_URL=http://your-actual-api-domain.sslip.io
   ```

2. **Commit and Push:**
   ```bash
   git add docker-compose.prod.yml
   git commit -m "Update API URLs with Coolify domains"
   git push origin main
   ```

3. **Coolify Auto-Redeploys:**
   - Coolify will automatically redeploy
   - Both containers should become healthy
   - Domains should work properly

## 🧪 Step 4: Test Your Deployment

1. **Test API:**
   - Visit: `https://your-api-domain.sslip.io/health`
   - Should return: `{"status": "healthy"}`
   - Visit: `https://your-api-domain.sslip.io/docs`
   - Should show the API documentation

2. **Test Webapp:**
   - Visit: `https://your-webapp-domain.sslip.io`
   - Should show the TarmacTrack application
   - Should redirect to `/KORD` and display the map
   - Check browser network tab - API calls should go to your Coolify API domain

## 🔧 Step 5: Troubleshooting

### Webapp Still Calls localhost:8001:
1. Verify both `build.args` and `environment` have the correct API URL
2. Redeploy to trigger a fresh build
3. Check browser dev tools to see what URL the webapp is actually calling

### Containers Show as Unhealthy:
1. Check that `curl` is installed in both Dockerfiles
2. Verify health check endpoints are responding
3. Look at container logs for errors

### Database Connection Fails:
1. Ensure connection string uses `postgresql+asyncpg://` (not `postgres://`)
2. Use `ssl=require` (not `sslmode=require`)
3. Verify PostGIS extension is installed

## 🎉 Success!

Your TarmacTrack application should now be running on Coolify with:
- ✅ TimescaleDB Cloud database
- ✅ Coolify-generated free domains
- ✅ Proper Next.js build-time configuration
- ✅ Automated SSL certificates
- ✅ Container health monitoring
- ✅ Auto-deployment from Git

## 💡 Key Learnings

- **Next.js Environment Variables**: Need to be available at build time, not just runtime
- **Docker Build Args**: Required for Next.js public environment variables
- **Coolify Domains**: Generated after first deployment, require configuration update
- **Health Checks**: Both containers need `curl` installed for proper monitoring

## 🔄 Making Changes

To update your deployment:
1. Make changes to your code
2. Commit and push to git
3. Coolify will automatically redeploy

## 💡 Tips

- **Monitor**: Check Coolify logs if something isn't working
- **Scaling**: You can scale the webapp service if needed
- **Custom Domains**: Add your own domain later in Coolify settings
- **Environment**: This setup is perfect for testing - for production, move environment variables to Coolify's environment settings 