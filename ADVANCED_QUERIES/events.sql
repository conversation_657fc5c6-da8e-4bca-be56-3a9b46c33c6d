WITH

-- Step 1: Define the input airport ICAO code.

-- Replace 'KJFK' with the ICAO code of the airport you want to analyze.

airport_input AS (

SELECT

'KJFK' AS icao

),

-- Step 2: Extract the start and end points of each runway for the airport.

-- We use ST_StartPoint and ST_EndPoint on the centerline geometry.

-- UNION ALL is used to combine the start and end points into a single set.

runway_endpoints AS (

SELECT

ST_StartPoint(r.centerline::geometry) AS point

FROM

public.runways AS r

WHERE

r.airport_icao = (

SELECT

icao

FROM

airport_input

)

UNION ALL

SELECT

ST_EndPoint(r.centerline::geometry) AS point

FROM

public.runways AS r

WHERE

r.airport_icao = (

SELECT

icao

FROM

airport_input

)

),

-- Step 3: Convert each endpoint geometry directly to a resolution 8 H3 cell.

-- The h3_geo_to_h3 function works directly with PostGIS point geometries.

initial_h3_cells AS (

SELECT DISTINCT

h3_lat_lng_to_cell(ep.point, 8) AS h3_cell

FROM

runway_endpoints AS ep

),

-- Step 4: Expand the set of cells to include neighbors within a distance of 3.

-- h3_grid_disk (or h3_k_ring) gets the cell and its neighbors up to k distance away.

-- This creates a buffer around the runway endpoints.

expanded_h3_cells AS (

SELECT DISTINCT

neighbor_cell

FROM

initial_h3_cells,

LATERAL h3_grid_disk(h3_cell, 2) AS neighbor_cell

)

-- Step 5: Find the unique parent cells at a coarser resolution (resolution 4).

-- The h3_cell_to_parent function finds the ancestor cell at the specified resolution.

-- This defines the final, broad search area.

SELECT DISTINCT

h3_cell_to_parent(neighbor_cell, 4) AS search_area_h3_cell

FROM

expanded_h3_cells;