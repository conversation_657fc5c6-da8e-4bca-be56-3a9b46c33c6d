-- This query efficiently finds pairs of aircraft that were close to each other
-- within a specific H3 resolution 4 area and timeframe. It returns the minimum
-- H3 grid distance observed for each unique pair.

-- Use a Common Table Expression (CTE) to pre-filter the data.
-- This is the most critical step for performance, as it dramatically reduces
-- the number of rows that need to be processed in the main query.
WITH relevant_positions AS (
    SELECT
        "time",
        hex,
        h3_index
    FROM
        public.aircraft_positions
    WHERE
        -- Filter 1: Restrict to the last 2 hours. This uses the 'time' part of the index.
        "time" >= NOW() - INTERVAL '2 hours'
        -- Filter 2: Restrict to the h3_res4 search area. This uses the 'h3_res4' part of the index.
        -- The database can efficiently seek to the relevant data blocks using the
        -- aircraft_positions_time_h3_res4_idx index.
        AND h3_res4 = '842a103ffffffff'::h3index
        -- Filter 3: Exclude positions where alt_baro indicates an invalid altitude.
        AND alt_baro != -10000
)
-- Main query to find proximity between aircraft
SELECT
    a1.hex AS hex1,
    a2.hex AS hex2,
    -- Calculate the minimum H3 grid distance for each pair.
    -- We use MIN() because a pair of aircraft might be close at multiple timestamps
    -- within the 2-hour window, and we only want their closest encounter.
    MIN(h3_grid_distance(a1.h3_index, a2.h3_index)) AS min_h3_distance
FROM
    -- Join the filtered set of positions with itself.
    relevant_positions a1
JOIN
    relevant_positions a2 ON a1."time" = a2."time" -- Must be at the exact same time
    -- This condition is crucial:
    -- 1. It prevents an aircraft from being compared with itself (hex1 != hex2).
    -- 2. It ensures we only get one result per pair (e.g., we get (A, B) but not (B, A)),
    --    halving the number of comparisons.
    AND a1.hex < a2.hex
WHERE
    -- After joining, apply the more computationally expensive grid distance calculation.
    -- This function is now running only on the small, pre-filtered dataset.
    h3_grid_distance(a1.h3_index, a2.h3_index) <= 3
GROUP BY
    -- Group by the unique pair of aircraft.
    a1.hex, a2.hex
ORDER BY
    min_h3_distance,
    hex1,
    hex2;


    