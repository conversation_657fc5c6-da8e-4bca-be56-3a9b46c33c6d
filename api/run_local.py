#!/usr/bin/env python3
"""
Local development server for TarmacTrack API.
Runs the API on localhost:8000 with auto-reload enabled.
"""

import uvicorn
import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

if __name__ == "__main__":
    print("🚀 Starting TarmacTrack API on http://localhost:8000")
    print("📚 API Documentation available at: http://localhost:8000/docs")
    print("🔍 Health check available at: http://localhost:8000/health")
    print("🔄 Auto-reload enabled for development")
    print("-" * 60)
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=["app"],
        log_level="info"
    )
