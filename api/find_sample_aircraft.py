#!/usr/bin/env python3
"""
Script to find sample aircraft hex values from the database for testing.
"""

import asyncio
import asyncpg
from datetime import datetime, timezone, timedelta

async def find_sample_aircraft():
    """Find some sample aircraft hex values from the database."""
    
    # Database connection string (from your config)
    DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?ssl=require"
    
    try:
        # Connect to the database
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Get some recent aircraft positions
        query = """
        SELECT DISTINCT hex, COUNT(*) as position_count
        FROM aircraft_positions 
        WHERE time >= NOW() - INTERVAL '7 days'
        GROUP BY hex
        ORDER BY position_count DESC
        LIMIT 10;
        """
        
        print("🔍 Finding sample aircraft with recent activity...")
        print("-" * 60)
        
        rows = await conn.fetch(query)
        
        if rows:
            print("✅ Found aircraft with recent activity:")
            for i, row in enumerate(rows, 1):
                print(f"   {i}. {row['hex']} - {row['position_count']} positions")
            
            # Get the most active aircraft for testing
            most_active_hex = rows[0]['hex']
            
            # Get time range for this aircraft
            time_query = """
            SELECT MIN(time) as first_seen, MAX(time) as last_seen, COUNT(*) as total_positions
            FROM aircraft_positions 
            WHERE hex = $1 AND time >= NOW() - INTERVAL '7 days';
            """
            
            time_info = await conn.fetchrow(time_query, most_active_hex)
            
            print(f"\n📊 Details for most active aircraft ({most_active_hex}):")
            print(f"   First seen: {time_info['first_seen']}")
            print(f"   Last seen: {time_info['last_seen']}")
            print(f"   Total positions: {time_info['total_positions']}")
            
            print(f"\n💡 Use this hex for testing: {most_active_hex}")
            
        else:
            print("❌ No recent aircraft activity found in the database")
            
        await conn.close()
        
    except Exception as e:
        print(f"❌ Error connecting to database: {e}")

if __name__ == "__main__":
    asyncio.run(find_sample_aircraft())
