FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    DATABASE_URL=postgresql+asyncpg://tsdbadmin:<EMAIL>:34087/tsdb?ssl=require \
    B2_KEY_ID=0056deeab75af970000000003 \
    B2_APPLICATION_KEY=K005cayC+sNRkiHzX8y2gTmpuNWAdIQ \
    B2_BUCKET_NAME=skytraces \
    R2_ACCESS_KEY_ID=c53df2e89e72fe035e7db5a899c9b4de \
    R2_SECRET_ACCESS_KEY=e08273037508b98ce57dc581b58db3b2c0bb1ddbbe1d32d28211cb1c83552ef6 \
    R2_ENDPOINT_URL=https://a80f24e552c9303eea94ad9ba226353d.r2.cloudflarestorage.com \
    R2_BUCKET_NAME=recordings \
    R2_REGION=auto

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        gcc \
        postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
# Note: requirements.txt specifies h3==4.3.0 for consistent polygon API
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"] 