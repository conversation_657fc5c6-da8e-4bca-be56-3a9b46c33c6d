#!/usr/bin/env python3
"""
Test script for the new aircraft trace endpoint.
This script tests the /{hex}/trace endpoint to ensure it works correctly.
"""

import requests
from datetime import datetime, timezone, timedelta

def test_trace_endpoint():
    """Test the aircraft trace endpoint via HTTP requests."""

    base_url = "http://localhost:8000"
    
    # Test with a common aircraft hex (you may need to adjust this based on your data)
    test_hex = "a1b2c3"  # Replace with an actual hex from your database
    
    # Set time range for last 24 hours
    end_time = datetime.now(timezone.utc)
    start_time = end_time - timedelta(hours=24)

    print(f"🧪 Testing aircraft trace endpoint for hex: {test_hex}")
    print(f"📅 Time range: {start_time.isoformat()} to {end_time.isoformat()}")
    print("-" * 60)

    try:
        # Test health endpoint first
        print("🔍 Testing health endpoint...")
        health_response = requests.get(f"{base_url}/health")
        if health_response.status_code == 200:
            print("✅ Health check passed")
        else:
            print(f"❌ Health check failed: {health_response.status_code}")
            return

        # Test trace endpoint with default parameters (last 24 hours)
        print(f"\n📊 Testing trace endpoint with defaults...")
        trace_url = f"{base_url}/api/v1/aircraft/{test_hex}/trace"
        trace_response = requests.get(trace_url)

        if trace_response.status_code == 200:
            trace_data = trace_response.json()
            print(f"✅ Trace endpoint (defaults): {trace_data['query_info']['results_count']} positions")
            print(f"   Time range: {trace_data['query_info']['time_range_hours']} hours")
        else:
            print(f"❌ Trace endpoint (defaults) failed: {trace_response.status_code}")
            print(f"Response: {trace_response.text}")

        # Test trace endpoint with specific time range
        print(f"\n🔍 Testing trace endpoint with specific time range...")
        params = {
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat()
        }
        trace_response_params = requests.get(trace_url, params=params)

        if trace_response_params.status_code == 200:
            trace_data_params = trace_response_params.json()
            print(f"✅ Trace endpoint (with params): {trace_data_params['query_info']['results_count']} positions")
            print(f"   Time range: {trace_data_params['query_info']['time_range_hours']} hours")
            
            # Show sample positions
            if trace_data_params['positions']:
                print(f"\n📋 Sample positions (first 5):")
                for i, position in enumerate(trace_data_params['positions'][:5]):
                    alt = position.get('alt_baro', 'N/A')
                    gs = position.get('gs', 'N/A')
                    track = position.get('track', 'N/A')
                    print(f"   {i+1}. {position['time']} - ALT:{alt}ft - GS:{gs}kt - TRK:{track}°")
        else:
            print(f"❌ Trace endpoint (with params) failed: {trace_response_params.status_code}")
            print(f"Response: {trace_response_params.text}")

        # Test with invalid hex (empty string)
        print(f"\n🚫 Testing with invalid hex...")
        invalid_trace_url = f"{base_url}/api/v1/aircraft/ /trace"  # space as hex
        invalid_response = requests.get(invalid_trace_url)

        if invalid_response.status_code == 400:
            print("✅ Invalid hex properly rejected with 400 status")
        else:
            print(f"❌ Invalid hex test failed: expected 400, got {invalid_response.status_code}")

        # Test with invalid time range
        print(f"\n🚫 Testing with invalid time range...")
        invalid_params = {
            'start_time': end_time.isoformat(),  # start after end
            'end_time': start_time.isoformat()
        }
        invalid_time_response = requests.get(trace_url, params=invalid_params)
        
        if invalid_time_response.status_code == 400:
            print("✅ Invalid time range properly rejected with 400 status")
        else:
            print(f"❌ Invalid time range test failed: expected 400, got {invalid_time_response.status_code}")

        print(f"\n✅ All tests completed!")
        print(f"🌐 View API docs at: {base_url}/docs")
        print(f"📖 Test the endpoint manually at: {trace_url}")

    except requests.exceptions.ConnectionError:
        print(f"❌ Could not connect to API at {base_url}")
        print("💡 Make sure the API is running with: python run_local.py")
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_trace_endpoint()
