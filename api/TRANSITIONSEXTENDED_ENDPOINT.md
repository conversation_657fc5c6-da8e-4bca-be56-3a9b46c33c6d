# TransitionsExtended Endpoint Documentation

## Overview

The `/airports/{icao}/transitionsextended` endpoint extends the functionality of the regular `/transitions` endpoint by including additional context events for each aircraft involved in takeoff and landing operations.

## Endpoint Details

- **URL**: `GET /api/v1/airports/{icao}/transitionsextended`
- **Method**: GET
- **Response**: Same as `/transitions` endpoint (`AirportTransitionsResponse`)

## Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `icao` | string | Yes | 4-character ICAO airport code |
| `start_time` | datetime | No | Start time for query (ISO format). Defaults to 24 hours ago |
| `end_time` | datetime | No | End time for query (ISO format). Defaults to now |

## Functionality

### Regular Transitions Endpoint
The regular `/transitions` endpoint returns only takeoff and landing events that occur at the specified airport within the given time range.

### Extended Transitions Endpoint
The `/transitionsextended` endpoint returns:

1. **All takeoff and landing events** at the specified airport (same as regular endpoint)
2. **Previous events** for each aircraft involved (the event immediately before each takeoff/landing)
3. **Next events** for each aircraft involved (the event immediately after each takeoff/landing)

**Note**: Previous and next events can be any flight phase (startup, appear, disappear, shutdown, airborne, ground) and may have `null` values for `runway_name` and `airport_icao` since they don't necessarily occur at a specific runway or airport.

### Key Features

- **Single Database Query**: All data is fetched in one optimized database call using CTEs (Common Table Expressions)
- **Unique Results**: Duplicate events are automatically removed
- **Extended Time Window**: The query looks beyond the specified time range to find adjacent events
- **Ordered Results**: Events are returned ordered by time (most recent first)

## Use Cases

This endpoint is useful for:

- **Flight Path Analysis**: Understanding aircraft behavior before and after airport operations
- **Pattern Recognition**: Identifying common flight patterns and sequences
- **Operational Analysis**: Analyzing the complete context of airport operations
- **Data Visualization**: Creating more comprehensive flight tracking displays

## Example Usage

### Request
```http
GET /api/v1/airports/KJFK/transitionsextended?start_time=2024-01-01T12:00:00Z&end_time=2024-01-01T18:00:00Z
```

### Response
```json
{
  "airport_icao": "KJFK",
  "start_time": "2024-01-01T12:00:00Z",
  "end_time": "2024-01-01T18:00:00Z",
  "total_transitions": 150,
  "transitions": [
    {
      "time": "2024-01-01T17:45:00Z",
      "hex": "a12345",
      "flight_phase": "landing",
      "runway_name": "04L",
      "flight": "UAL123",
      "alt_baro": 100,
      "gs": 140,
      ...
    },
    {
      "time": "2024-01-01T17:44:30Z",
      "hex": "a12345",
      "flight_phase": "airborne",
      "runway_name": null,
      "flight": "UAL123",
      "alt_baro": 500,
      "gs": 160,
      ...
    },
    {
      "time": "2024-01-01T17:46:15Z",
      "hex": "a12345",
      "flight_phase": "disappear",
      "runway_name": null,
      "flight": "UAL123",
      "alt_baro": null,
      "gs": null,
      ...
    }
  ]
}
```

## Performance Considerations

- The endpoint uses an optimized SQL query with CTEs to minimize database load
- Extended time windows (±2 hours) are used to capture adjacent events
- Results are deduplicated to prevent unnecessary data transfer
- Indexes on `time`, `hex`, and `flight_phase` columns optimize query performance

## Implementation Details

The endpoint is implemented in:
- **Service**: `app/services/airport_service.py` - `get_airport_transitions_extended()`
- **Router**: `app/routers/airports.py` - `/transitionsextended` endpoint
- **Frontend**: `landing/src/lib/api.ts` - `getAirportTransitionsExtended()`
