"""
Airport and Runway models for TarmacTrack API.
Matches the database schema exactly.
"""

from sqlalchemy import <PERSON>um<PERSON>, Integer, DateTime, ForeignKey, Enum as SQLEnum, SmallInteger
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import VARCHAR
from geoalchemy2 import Geography

from app.db.base_class import Base
from app.shared.enums import RunwaySurface


class Airport(Base):
    """Airport model - matches database table exactly."""
    __tablename__ = "airports"

    icao = Column(VARCHAR(4), primary_key=True, index=True)
    iata = Column(VARCHAR(3), nullable=True)
    name = Column(VARCHAR(255), nullable=True)
    location = Column(Geography('POINT', srid=4326), nullable=True)
    elevation_ft = Column(Integer, nullable=True)
    timezone = Column(VARCHAR(50), nullable=True)
    image_url = Column(VARCHAR(500), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=True)

    # Relationships
    runways = relationship("Runway", back_populates="airport", cascade="all, delete-orphan")
    aircraft_events = relationship("AircraftEvent", back_populates="airport", overlaps="runway")


class Runway(Base):
    """Runway model - matches database table exactly."""
    __tablename__ = "runways"

    airport_icao = Column(VARCHAR(4), ForeignKey("airports.icao", ondelete="CASCADE", onupdate="CASCADE"), primary_key=True)
    name = Column(VARCHAR(10), primary_key=True)
    length_ft = Column(Integer, nullable=True)
    width_ft = Column(Integer, nullable=True)
    surface = Column(SQLEnum(RunwaySurface, name='runway_surface_enum'), nullable=False)
    centerline = Column(Geography('LINESTRING', srid=4326), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    heading_deg = Column(SmallInteger, nullable=True)

    # Relationships
    airport = relationship("Airport", back_populates="runways")
    aircraft_events = relationship("AircraftEvent", back_populates="runway", overlaps="aircraft_events,airport")
