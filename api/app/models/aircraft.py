"""
Aircraft models for TarmacTrack API.
Matches the database schema exactly.
"""

from sqlalchemy import <PERSON>umn, Integer, SmallInteger, ForeignKey, Enum as SQLEnum, Text, ForeignKeyConstraint, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import TIMESTAMP, VARCHAR

from app.db.base_class import Base
from app.shared.enums import AircraftCategory, FlightPhase, WakeTurbulenceCategory
from app.shared.types import H3Index


class Aircraft(Base):
    """Aircraft model - matches database table exactly."""
    __tablename__ = "aircraft"

    # Primary fields
    hex = Column(Text, primary_key=True, index=True)
    category = Column(SQLEnum(AircraftCategory, name='aircraft_category_enum'), nullable=True)
    registration = Column(Text, nullable=True, unique=True)

    # TAR1090 enhanced fields
    icao_type_designator = Column(VARCHAR(4), nullable=True, index=True)
    type_description = Column(VARCHAR(10), nullable=True)
    wake_turbulence_category = Column(SQLEnum(WakeTurbulenceCategory, name='wake_turbulence_category_enum'), nullable=True)
    aircraft_description = Column(Text, nullable=True)
    operator = Column(Text, nullable=True, index=True)
    year_built = Column(Integer, nullable=True)

    # Flag fields
    is_military = Column(Boolean, default=False, nullable=False, index=True)
    is_interesting = Column(Boolean, default=False, nullable=False)
    is_pia = Column(Boolean, default=False, nullable=False)  # Privacy ICAO Address
    is_ladd = Column(Boolean, default=False, nullable=False)  # Limiting Aircraft Data Displayed

    # Metadata fields
    country_code = Column(VARCHAR(2), nullable=True, index=True)
    database_source = Column(VARCHAR(50), nullable=True)
    last_updated = Column(TIMESTAMP(timezone=True), nullable=True, index=True)
    created_at = Column(TIMESTAMP(timezone=True), nullable=True)

    # Relationships
    positions = relationship("AircraftPosition", back_populates="aircraft")
    events = relationship("AircraftEvent", back_populates="aircraft")


class AircraftPosition(Base):
    """Aircraft position model - matches database table exactly."""
    __tablename__ = "aircraft_positions"

    time = Column(TIMESTAMP(timezone=True), primary_key=True, nullable=False)
    hex = Column(Text, ForeignKey("aircraft.hex"), primary_key=True, nullable=False)
    alt_baro = Column(Integer, nullable=True)
    alt_geom = Column(Integer, nullable=True)
    gs = Column(SmallInteger, nullable=True)
    track = Column(SmallInteger, nullable=True)
    baro_rate = Column(SmallInteger, nullable=True)
    squawk = Column(SmallInteger, nullable=True)
    flight = Column(VARCHAR(8), nullable=True)
    h3_index = Column(H3Index, nullable=True)
    # h3_res4 is a generated column in the database
    h3_res4 = Column(H3Index, nullable=True)

    # Relationship to aircraft
    aircraft = relationship("Aircraft", back_populates="positions")

    __table_args__ = (
        {"postgresql_partition_by": "RANGE (time)"},  # TimescaleDB partitioning hint
    )


class AircraftEvent(Base):
    """Aircraft event model - matches database table exactly."""
    __tablename__ = "aircraft_events"

    time = Column(TIMESTAMP(timezone=True), primary_key=True, nullable=False)
    hex = Column(Text, ForeignKey("aircraft.hex"), primary_key=True, nullable=False)
    alt_baro = Column(Integer, nullable=True)
    alt_geom = Column(Integer, nullable=True)
    gs = Column(SmallInteger, nullable=True)
    track = Column(SmallInteger, nullable=True)
    baro_rate = Column(SmallInteger, nullable=True)
    squawk = Column(SmallInteger, nullable=True)
    flight = Column(VARCHAR(8), nullable=True)
    h3_index = Column(H3Index, nullable=True)
    h3_res4 = Column(H3Index, nullable=True)
    flight_phase = Column(SQLEnum(FlightPhase, name='flight_phase_enum'), nullable=False)
    airport_icao = Column(VARCHAR(4), ForeignKey("airports.icao"), nullable=True)
    runway_name = Column(VARCHAR(10), nullable=True)

    # Relationships
    aircraft = relationship("Aircraft", back_populates="events")
    airport = relationship("Airport", back_populates="aircraft_events", overlaps="runway")
    runway = relationship("Runway", back_populates="aircraft_events", overlaps="airport")

    __table_args__ = (
        ForeignKeyConstraint(
            ['airport_icao', 'runway_name'],
            ['runways.airport_icao', 'runways.name'],
            ondelete='SET NULL'
        ),
    )