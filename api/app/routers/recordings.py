import re
from fastapi import APIRouter, HTTPException, status
from fastapi.responses import StreamingResponse

from app.schemas.recordings import RecordingsListResponse, ErrorResponse
from app.services.recordings_service import recordings_service


router = APIRouter(
    prefix="/recordings",
    tags=["recordings"],
    responses={
        400: {"description": "Bad request", "model": ErrorResponse},
        404: {"description": "Not found", "model": ErrorResponse},
        500: {"description": "Internal server error", "model": ErrorResponse}
    },
)


def validate_date_format(date: str) -> bool:
    """Validate date is in YYYYMMDD format."""
    return bool(re.match(r'^\d{8}$', date))


def validate_station_name(station: str) -> bool:
    """Validate station name format."""
    # Allow alphanumeric characters, underscores, and hyphens
    return bool(re.match(r'^[a-zA-Z0-9_-]+$', station))


def validate_filename(filename: str) -> bool:
    """Validate recording filename format."""
    # Must match the expected pattern: YYYYMMDD_HHMMSS_mmm_dDDDDDD.webm
    return bool(re.match(r'^\d{8}_\d{6}_\d{3}_d\d+\.webm$', filename))





@router.get(
    "/{station}/{date}",
    response_model=RecordingsListResponse,
    summary="List recordings for date and station",
    description="""
    List all audio recordings for a specific station and date.

    **Parameters:**
    - `station`: Station identifier (e.g., "4FL5_122900")
    - `date`: Date in YYYYMMDD format (e.g., "20250723")

    **Returns:**
    - Complete list of recordings with metadata including filename, start time, duration, and direct download URL
    - Summary statistics: total recordings, total duration, total size

    **Example:**
    ```
    GET /api/v1/recordings/4FL5_122900/20250723
    ```

    **Note:** This endpoint accesses Cloudflare R2 storage and does not require database access.
    Download URLs point directly to R2 storage for efficient file access.
    """
)
async def list_recordings(station: str, date: str):
    """List all recordings for a given station and date."""

    # Validate input parameters
    if not validate_station_name(station):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid station name format"
        )

    if not validate_date_format(date):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid date format. Use YYYYMMDD format (e.g., 20250723)"
        )
    
    try:
        # Get recordings from service
        recordings = await recordings_service.list_recordings(station, date)
        
        # Calculate summary statistics
        total_recordings = len(recordings)
        total_duration_ms = sum(r.duration_ms for r in recordings)
        total_size_bytes = sum(r.size_bytes for r in recordings)

        # Return response
        return RecordingsListResponse(
            station=station,
            date=date,
            total_recordings=total_recordings,
            total_duration_ms=total_duration_ms,
            total_size_bytes=total_size_bytes,
            recordings=recordings
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions from service
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred while fetching recordings: {str(e)}"
        )


@router.get(
    "/{station}/{date}/{filename}",
    summary="Stream/download specific recording",
    description="""
    Stream or download a specific audio recording file.

    **Parameters:**
    - `station`: Station identifier (e.g., "4FL5_122900")
    - `date`: Date in YYYYMMDD format (e.g., "20250723")
    - `filename`: Recording filename (e.g., "20250723_031946_192_d002950.webm")

    **Returns:**
    - Binary audio file in WebM format (optimized for Apple device compatibility)
    - Appropriate headers for streaming/download
    - CORS headers for web audio playback

    **Example:**
    ```
    GET /api/v1/recordings/4FL5_122900/20250723/20250723_031946_192_d002950.webm
    ```

    **Note:** This endpoint streams the file from Cloudflare R2 storage through our API
    to handle authentication and CORS properly for web audio playback.
    """
)
async def stream_recording(
    station: str,
    date: str,
    filename: str
):
    """Stream a specific recording file for audio playback."""

    # Validate input parameters
    if not validate_station_name(station):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid station name format"
        )

    if not validate_date_format(date):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid date format. Use YYYYMMDD format (e.g., 20250723)"
        )

    if not validate_filename(filename):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid filename format"
        )

    try:
        # Get recording stream from service
        s3_response = await recordings_service.get_recording_stream(station, date, filename)

        # Create streaming response with proper headers for audio playback
        def generate():
            try:
                for chunk in s3_response['Body'].iter_chunks(chunk_size=8192):
                    yield chunk
            finally:
                s3_response['Body'].close()

        # Return streaming response with CORS and audio headers
        return StreamingResponse(
            generate(),
            media_type="video/webm",  # WebM format for better Apple device compatibility
            headers={
                "Content-Disposition": f"inline; filename={filename}",  # inline for streaming
                "Content-Length": str(s3_response['ContentLength']),
                "Accept-Ranges": "bytes",
                "Access-Control-Allow-Origin": "*",  # Allow CORS for audio playback
                "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
                "Access-Control-Allow-Headers": "Range, Content-Range",
                "Cache-Control": "public, max-age=3600"  # Cache for 1 hour
            }
        )

    except HTTPException:
        # Re-raise HTTP exceptions from service
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred while streaming recording: {str(e)}"
        )



