from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.session import get_db
from app.services.airport_service import (
    get_airport_by_icao,
    get_airport_transitions,
    get_airport_transitions_extended,
    update_airport_image_url,
)
from app.services.storage_service import get_storage_service, StorageService
from app.schemas.airport import Airport, AirportTransitionsResponse
from app.schemas.image import ImageUploadResponse, ImageDeleteResponse, AirportImageInfo

router = APIRouter(
    prefix="/airports",
    tags=["airports"],
    responses={404: {"description": "Airport not found"}, 500: {"description": "Internal server error"}},
)


@router.get(
    "/{icao}",
    response_model=Airport,
    summary="Get airport by ICAO code",
    description="Retrieve detailed information about an airport using its ICAO code, including runways and geometry data.",
)
async def get_airport(icao: str, db: AsyncSession = Depends(get_db)) -> Airport:
    """Get airport information by ICAO code."""
    airport = await get_airport_by_icao(db, icao)

    if not airport:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Airport with ICAO code {icao} not found")

    return airport


@router.get(
    "/{icao}/transitions",
    response_model=AirportTransitionsResponse,
    summary="Get airport transitions",
    description="Retrieve takeoff and landing events for a specific airport within a time range. "
    "If no end_time is provided, defaults to now. If no start_time is provided, defaults to 24 hours ago.",
)
async def get_airport_transitions_endpoint(
    icao: str,
    start_time: Optional[datetime] = Query(None, description="Start time for transitions query (ISO format)"),
    end_time: Optional[datetime] = Query(None, description="End time for transitions query (ISO format)"),
    db: AsyncSession = Depends(get_db),
) -> AirportTransitionsResponse:
    """Get airport transitions (takeoffs and landings) for a specific airport."""
    # Validate ICAO code format
    if len(icao) != 4:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="ICAO code must be exactly 4 characters"
        )

    # Validate time range if both are provided
    if start_time and end_time and start_time >= end_time:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="Start time must be before end time"
        )

    try:
        transitions = await get_airport_transitions(db, icao, start_time, end_time)
        return transitions
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e))
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while fetching transitions",
        )


@router.get(
    "/{icao}/transitionsextended",
    response_model=AirportTransitionsResponse,
    summary="Get extended airport transitions",
    description="Retrieve takeoff and landing events for a specific airport within a time range, "
    "including the previous and next events for each aircraft involved. "
    "If no end_time is provided, defaults to now. If no start_time is provided, defaults to 24 hours ago.",
)
async def get_airport_transitions_extended_endpoint(
    icao: str,
    start_time: Optional[datetime] = Query(None, description="Start time for transitions query (ISO format)"),
    end_time: Optional[datetime] = Query(None, description="End time for transitions query (ISO format)"),
    db: AsyncSession = Depends(get_db),
) -> AirportTransitionsResponse:
    """Get extended airport transitions (takeoffs and landings) including previous and next events for each aircraft."""
    # Validate ICAO code format
    if len(icao) != 4:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="ICAO code must be exactly 4 characters"
        )

    # Validate time range if both are provided
    if start_time and end_time and start_time >= end_time:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="Start time must be before end time"
        )

    try:
        transitions = await get_airport_transitions_extended(db, icao, start_time, end_time)
        return transitions
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e))
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while fetching extended transitions",
        )


@router.post(
    "/{icao}/image",
    response_model=ImageUploadResponse,
    summary="Upload airport image",
    description="Upload an image for a specific airport. The image will be processed, "
    "optimized, and stored in cloud storage.",
)
async def upload_airport_image(
    icao: str,
    file: UploadFile = File(..., description="Image file to upload"),
    db: AsyncSession = Depends(get_db),
    storage: StorageService = Depends(get_storage_service),
) -> ImageUploadResponse:
    """Upload an image for an airport."""
    # Validate ICAO code format
    if len(icao) != 4:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="ICAO code must be exactly 4 characters"
        )

    # Check if airport exists
    airport = await get_airport_by_icao(db, icao)
    if not airport:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Airport with ICAO code {icao} not found")

    # Validate file type
    if not file.content_type or not file.content_type.startswith("image/"):
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="File must be an image")

    # Validate file size (10MB limit)
    max_size = 10 * 1024 * 1024  # 10MB
    file_content = await file.read()
    if len(file_content) > max_size:
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="File size must be less than 10MB")

    try:
        # Upload to storage
        image_url = await storage.upload_airport_image(icao, file_content, file.filename or "image")

        # Update database
        success = await update_airport_image_url(db, icao, image_url)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail=f"Airport with ICAO code {icao} not found"
            )

        return ImageUploadResponse(
            success=True, message="Image uploaded successfully", image_url=image_url, icao=icao.upper()
        )

    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e))
    except Exception as e:
        # Log the actual error for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Upload failed for {icao}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to upload image: {str(e)}"
        )


@router.delete(
    "/{icao}/image",
    response_model=ImageDeleteResponse,
    summary="Delete airport image",
    description="Delete the image for a specific airport from cloud storage and database.",
)
async def delete_airport_image(
    icao: str, db: AsyncSession = Depends(get_db), storage: StorageService = Depends(get_storage_service)
) -> ImageDeleteResponse:
    """Delete an airport image."""
    # Validate ICAO code format
    if len(icao) != 4:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="ICAO code must be exactly 4 characters"
        )

    # Check if airport exists
    airport = await get_airport_by_icao(db, icao)
    if not airport:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Airport with ICAO code {icao} not found")

    try:
        # Delete from storage (don't fail if not found)
        await storage.delete_airport_image(icao)

        # Update database to remove URL
        success = await update_airport_image_url(db, icao, None)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail=f"Airport with ICAO code {icao} not found"
            )

        return ImageDeleteResponse(success=True, message="Image deleted successfully", icao=icao.upper())

    except Exception:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete image")


@router.get(
    "/{icao}/image/info",
    response_model=AirportImageInfo,
    summary="Get airport image info",
    description="Get information about an airport's image availability.",
)
async def get_airport_image_info(
    icao: str, db: AsyncSession = Depends(get_db), storage: StorageService = Depends(get_storage_service)
) -> AirportImageInfo:
    """Get detailed information about an airport's image."""
    # Validate ICAO code format
    if len(icao) != 4:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="ICAO code must be exactly 4 characters"
        )

    # Get airport info from database
    airport = await get_airport_by_icao(db, icao)
    if not airport:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Airport with ICAO code {icao} not found")

    # Get current image info from storage
    storage_info = await storage.get_airport_image_info(icao)

    return AirportImageInfo(
        icao=icao.upper(),
        has_image=storage_info['exists'],
        image_url=storage_info['url']
    )
