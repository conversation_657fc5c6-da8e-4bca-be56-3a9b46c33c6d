from datetime import datetime, timezone, timedelta
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.services.aircraft_service import get_aircraft_positions_at_time, get_aircraft_trace
from app.routers.utils import convert_bounding_box_to_h3_cells
from app.schemas.aircraft import (
    AircraftPositionsAtTimeRequest,
    AircraftPositionsAtTimeResponse,
    AircraftTraceResponse
)

router = APIRouter(
    prefix="/aircraft",
    tags=["aircraft"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    },
)


@router.post(
    "/pos_at_time",
    response_model=AircraftPositionsAtTimeResponse,
    summary="Get aircraft positions at specific time",
    description="""
    Get aircraft positions within specified H3 cells at a given time.
    
    This endpoint finds the most recent position of aircraft within a given set of H3 cells 
    and a specific lookback period. It uses a two-step filtering process for efficiency:
    
    1. A coarse filter using pre-calculated resolution 4 parent cells (which should be indexed)
    2. A fine-grained filter to ensure the position's H3 index is a child of one of the original input cells
    
    **Parameters:**
    - `h3_cells`: Array of H3 cell indexes to search within (required)
    - `reference_time`: Reference timestamp (defaults to NOW if not provided)
    - `lookback_seconds`: Lookback window from reference time (default: 7 seconds, max: 3600)
    
    **Example Request Body:**
    ```json
    {
        "h3_cells": [
            "8544ad77fffffff", 
            "8544ad3bfffffff", 
            "8544ad33fffffff"
        ],
        "reference_time": "2024-01-01T12:00:00Z",
        "lookback_seconds": 7
    }
    ```
    
    **Returns:**
    - List of aircraft positions with the most recent position for each unique aircraft
    - Query information including the parameters used
    """
)
async def get_aircraft_positions_at_time_endpoint(
    request: AircraftPositionsAtTimeRequest,
    db: AsyncSession = Depends(get_db)
):
    """Get aircraft positions within H3 cells at a specific time."""
    try:
        # Get aircraft positions
        positions = await get_aircraft_positions_at_time(
            db=db,
            h3_cells=request.h3_cells,
            reference_time=request.reference_time,
            lookback_seconds=request.lookback_seconds
        )
        
        # Prepare query info
        reference_time = request.reference_time or datetime.now(timezone.utc)
        query_info = {
            "h3_cells_count": len(request.h3_cells),
            "reference_time": reference_time.isoformat(),
            "lookback_seconds": request.lookback_seconds,
            "results_count": len(positions)
        }
        
        return AircraftPositionsAtTimeResponse(
            positions=positions,
            query_info=query_info
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred while processing the request: {str(e)}"
        )


@router.get(
    "/pos_at_time_bbox",
    response_model=AircraftPositionsAtTimeResponse,
    summary="Get aircraft positions within bounding box at specific time",
    description="""
    Get aircraft positions within a geographic bounding box at a given time.

    This endpoint combines bounding box to H3 conversion with aircraft position lookup.
    It automatically converts the bounding box to appropriate H3 cells and then finds
    aircraft positions within those cells.

    **Parameters:**
    - `north`: North boundary latitude (-90 to 90)
    - `east`: East boundary longitude (-180 to 180)
    - `south`: South boundary latitude (-90 to 90)
    - `west`: West boundary longitude (-180 to 180)
    - `reference_time`: Reference timestamp (defaults to NOW if not provided)
    - `lookback_seconds`: Lookback window from reference time (default: 7 seconds, max: 3600)

    **Automatic H3 Resolution:**
    The endpoint automatically selects an appropriate H3 resolution (4-12) based on the
    diagonal size of the bounding box for optimal performance and coverage.

    **Example:**
    ```
    GET /api/v1/aircraft/pos_at_time_bbox?north=29.966832&east=-80.514361&south=26.968590&west=-83.166569&lookback_seconds=7
    ```

    **Returns:**
    - List of aircraft positions with the most recent position for each unique aircraft
    - Query information including H3 cells used, resolution, and parameters
    """
)
async def get_aircraft_positions_at_time_bbox(
    north: float = Query(..., ge=-90, le=90, description="North boundary latitude"),
    east: float = Query(..., ge=-180, le=180, description="East boundary longitude"),
    south: float = Query(..., ge=-90, le=90, description="South boundary latitude"),
    west: float = Query(..., ge=-180, le=180, description="West boundary longitude"),
    reference_time: Optional[datetime] = Query(None, description="Reference timestamp (defaults to NOW)"),
    lookback_seconds: int = Query(7, description="Lookback window from reference time in seconds", ge=1, le=3600),
    db: AsyncSession = Depends(get_db)
):
    """Get aircraft positions within a bounding box at a specific time."""
    try:
        # Validate bounding box
        if south >= north:
            raise ValueError("North latitude must be greater than south latitude")

        # Convert bounding box to H3 cells with automatic resolution
        h3_cells, resolution_used, estimated_area_km2 = convert_bounding_box_to_h3_cells(
            north=north,
            east=east,
            south=south,
            west=west,
            resolution=None  # Auto-select resolution
        )

        # Get aircraft positions using the H3 cells
        positions = await get_aircraft_positions_at_time(
            db=db,
            h3_cells=h3_cells,
            reference_time=reference_time,
            lookback_seconds=lookback_seconds
        )

        # Prepare query info with additional bounding box details
        actual_reference_time = reference_time or datetime.now(timezone.utc)
        query_info = {
            "bounding_box": {
                "north": north,
                "east": east,
                "south": south,
                "west": west
            },
            "h3_cells_count": len(h3_cells),
            "h3_resolution": resolution_used,
            "estimated_search_area_km2": round(estimated_area_km2, 2),
            "reference_time": actual_reference_time.isoformat(),
            "lookback_seconds": lookback_seconds,
            "results_count": len(positions)
        }

        return AircraftPositionsAtTimeResponse(
            positions=positions,
            query_info=query_info
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred while processing the request: {str(e)}"
        )


@router.get(
    "/{hex}/trace",
    response_model=AircraftTraceResponse,
    summary="Get aircraft trace data",
    description="""
    Get all position data for a specific aircraft within a time range.

    This endpoint retrieves all position records for a given aircraft hex identifier
    within the specified time range. It returns only essential fields needed for
    trace visualization: time, altitude, ground speed, track, barometric rate, and H3 index.

    **Parameters:**
    - `hex`: Aircraft hex identifier (required, case-insensitive)
    - `start_time`: Start of time range (defaults to 24 hours ago if not provided)
    - `end_time`: End of time range (defaults to NOW if not provided)

    **Time Range Defaults:**
    - If neither start_time nor end_time are provided: last 24 hours
    - If only start_time is provided: from start_time to NOW
    - If only end_time is provided: from 24 hours before end_time to end_time

    **Example:**
    ```
    GET /api/v1/aircraft/a1b2c3/trace?start_time=2024-01-01T12:00:00Z&end_time=2024-01-01T13:00:00Z
    ```

    **Returns:**
    - Aircraft hex identifier
    - List of position records in chronological order
    - Query information including time range and result count
    """
)
async def get_aircraft_trace_endpoint(
    hex: str,
    start_time: Optional[datetime] = Query(None, description="Start of time range (defaults to 24h ago)"),
    end_time: Optional[datetime] = Query(None, description="End of time range (defaults to NOW)"),
    db: AsyncSession = Depends(get_db)
):
    """Get aircraft trace data for a specific aircraft within a time range."""
    try:
        # Validate and normalize hex
        if not hex or len(hex.strip()) == 0:
            raise ValueError("Aircraft hex identifier is required")

        hex = hex.lower().strip()

        # Set default time range if not provided
        now = datetime.now(timezone.utc)

        if end_time is None:
            end_time = now

        if start_time is None:
            start_time = end_time - timedelta(hours=24)

        # Validate time range
        if start_time >= end_time:
            raise ValueError("Start time must be before end time")

        # Check if time range is reasonable (not more than 30 days)
        max_range = timedelta(days=30)
        if end_time - start_time > max_range:
            raise ValueError("Time range cannot exceed 30 days")

        # Get aircraft trace data
        positions = await get_aircraft_trace(
            db=db,
            hex=hex,
            start_time=start_time,
            end_time=end_time
        )

        # Prepare query info
        query_info = {
            "hex": hex,
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "time_range_hours": round((end_time - start_time).total_seconds() / 3600, 2),
            "results_count": len(positions)
        }

        return AircraftTraceResponse(
            hex=hex,
            positions=positions,
            query_info=query_info
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred while processing the request: {str(e)}"
        )
