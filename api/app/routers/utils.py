from fastapi import APIRouter, HTTPException, Query, status
import math
import h3

router = APIRouter(
    prefix="/utils",
    tags=["utilities"],
    responses={
        400: {"description": "Bad request"},
        500: {"description": "Internal server error"}
    },
)


def calculate_optimal_h3_resolution(north: float, east: float, south: float, west: float) -> int:
    """
    Calculate optimal H3 resolution based on bounding box diagonal.
    
    Args:
        north: North boundary latitude
        east: East boundary longitude
        south: South boundary latitude
        west: West boundary longitude
        
    Returns:
        Optimal H3 resolution between 4 and 12
    """
    # Calculate diagonal distance in degrees
    lat_diff = abs(north - south)
    lng_diff = abs(east - west)
    
    # Handle longitude wrapping around 180/-180
    if lng_diff > 180:
        lng_diff = 360 - lng_diff
    
    # Calculate diagonal distance
    diagonal_degrees = math.sqrt(lat_diff**2 + lng_diff**2)
    
    # Map diagonal distance to H3 resolution
    # These thresholds are empirically chosen for good coverage
    if diagonal_degrees >= 8:      # Very large areas (continental)
        return 4
    elif diagonal_degrees >= 3:    
        return 5
    elif diagonal_degrees >= 1.14:
        return 6
    elif diagonal_degrees >= 0.43:    
        return 7
    elif diagonal_degrees >= 0.16:  
        return 8
    elif diagonal_degrees >= 0.06:  
        return 9
    elif diagonal_degrees >= 0.02:   
        return 10
    elif diagonal_degrees >= 0.009:  
        return 11
    else:                          
        return 12


def convert_bounding_box_to_h3_cells(
    north: float,
    east: float,
    south: float,
    west: float,
    resolution: int = None
) -> tuple[list[str], int, float]:
    """
    Convert a bounding box to H3 cells.
    
    Args:
        north: North boundary latitude
        east: East boundary longitude  
        south: South boundary latitude
        west: West boundary longitude
        resolution: H3 resolution (auto-calculated if None)
        
    Returns:
        Tuple of (h3_cells_list, resolution_used, estimated_area_km2)
    """
    # Auto-calculate resolution if not provided
    if resolution is None:
        resolution = calculate_optimal_h3_resolution(north, east, south, west)
    
    # Create a polygon from the bounding box
    # Note: h3 expects lat/lng order, polygon goes counter-clockwise
    polygon_coords = [
        (north, west),  # Northwest corner
        (north, east),  # Northeast corner
        (south, east),  # Southeast corner
        (south, west),  # Southwest corner
        (north, west)   # Close the polygon
    ]

    # Create LatLngPoly object (h3 4.3.0)
    polygon = h3.LatLngPoly(polygon_coords)

    # Convert polygon to H3 cells
    h3_cells = h3.h3shape_to_cells(polygon, resolution)
    
    # Convert to list of strings
    h3_cells_list = [str(cell) for cell in h3_cells]
    
    # Estimate total area
    area_per_cell_km2 = h3.cell_area(h3_cells_list[0], unit='km^2') if h3_cells_list else 0
    estimated_area_km2 = len(h3_cells_list) * area_per_cell_km2
    
    return h3_cells_list, resolution, estimated_area_km2


@router.get(
    "/bbox_to_h3",
    summary="Convert bounding box to H3 cells",
    description="""
    Convert a geographic bounding box to a collection of H3 cells.
    
    This utility endpoint takes a bounding box defined by north, south, east, and west coordinates
    and returns the H3 cells that cover that area. The H3 resolution is automatically chosen
    based on the size of the bounding box, or can be manually specified.
    
    **Parameters:**
    - `north`: North boundary latitude (-90 to 90)
    - `east`: East boundary longitude (-180 to 180)
    - `south`: South boundary latitude (-90 to 90)
    - `west`: West boundary longitude (-180 to 180)
    - `resolution`: H3 resolution (4-12, auto-calculated if not specified)
    
    **Automatic Resolution Selection:**
    - Diagonal ≥8°: Resolution 4 (continental scale)
    - Diagonal ≥3°: Resolution 5 (multi-state scale)
    - Diagonal ≥1.14°: Resolution 6 (state scale)
    - Diagonal ≥0.43°: Resolution 7 (city scale)
    - Diagonal ≥0.16°: Resolution 8 (metropolitan scale)
    - Diagonal ≥0.06°: Resolution 9 (urban district scale)
    - Diagonal ≥0.02°: Resolution 10 (neighborhood scale)
    - Diagonal ≥0.009°: Resolution 11 (small neighborhood scale)
    - Diagonal <0.009°: Resolution 12 (block scale)
    
    **Returns:**
    - List of H3 cell indexes covering the bounding box
    - Metadata about the conversion including resolution used and estimated area
    
    **Note:** This endpoint does not require database access and is purely computational.
    """
)
async def convert_bounding_box_to_h3_endpoint(
    north: float = Query(..., ge=-90, le=90, description="North boundary latitude"),
    east: float = Query(..., ge=-180, le=180, description="East boundary longitude"),
    south: float = Query(..., ge=-90, le=90, description="South boundary latitude"),
    west: float = Query(..., ge=-180, le=180, description="West boundary longitude"),
    resolution: int = Query(None, ge=4, le=12, description="H3 resolution (auto-calculated if not specified)")
):
    """Convert a bounding box to H3 cells."""
    try:
        # Validate bounding box
        if south >= north:
            raise ValueError("North latitude must be greater than south latitude")
        
        # Convert bounding box to H3 cells
        h3_cells, resolution_used, estimated_area_km2 = convert_bounding_box_to_h3_cells(
            north=north,
            east=east,
            south=south,
            west=west,
            resolution=resolution
        )
        
        # Calculate diagonal for info
        lat_diff = abs(north - south)
        lng_diff = abs(east - west)
        if lng_diff > 180:
            lng_diff = 360 - lng_diff
        diagonal_degrees = math.sqrt(lat_diff**2 + lng_diff**2)
        
        # Prepare response
        return {
            "h3_cells": h3_cells,
            "bounding_box": {
                "north": north,
                "east": east,
                "south": south,
                "west": west
            },
            "resolution": resolution_used,
            "resolution_auto_selected": resolution is None,
            "diagonal_degrees": round(diagonal_degrees, 4),
            "cell_count": len(h3_cells),
            "estimated_area_km2": round(estimated_area_km2, 2) if estimated_area_km2 else 0
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred while processing the request: {str(e)}"
        )
