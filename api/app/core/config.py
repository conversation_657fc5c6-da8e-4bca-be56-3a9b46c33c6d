from typing import List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    PROJECT_NAME: str = "TarmacTrack API"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    ENVIRONMENT: str = "development"
    
    # Database settings
    DATABASE_URL: str

    # Backblaze B2 settings
    B2_KEY_ID: str = ""
    B2_APPLICATION_KEY: str = ""
    B2_BUCKET_NAME: str = "skytraces"

    # Cloudflare R2 settings for recordings
    R2_ACCESS_KEY_ID: str = ""
    R2_SECRET_ACCESS_KEY: str = ""
    R2_ENDPOINT_URL: str = ""
    R2_BUCKET_NAME: str = "recordings"
    R2_REGION: str = "auto"

    # CORS settings - stored as comma-separated string, now includes production domain
    ALLOWED_ORIGINS: str = "http://localhost:3002,http://localhost:3001,http://localhost:3000,http://localhost:8080,https://api.skytraces.com,https://skytraces.com,http://api.skytraces.com,http://skytraces.com,https://www.skytraces.com,http://www.skytraces.com"
    
    @property
    def CORS_ORIGINS_LIST(self) -> List[str]:
        """Get CORS origins as a list."""
        return [origin.strip() for origin in self.ALLOWED_ORIGINS.split(',')]
    
    @property
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        """Get database URI."""
        return self.DATABASE_URL

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # Allow extra fields from Coolify


settings = Settings()
