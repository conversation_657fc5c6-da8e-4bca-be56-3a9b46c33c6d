"""
Shared custom SQLAlchemy types for the TarmacTrack API.
"""

from sqlalchemy.types import UserDefinedType


class H3Index(UserDefinedType):
    """Custom SQLAlchemy type for PostgreSQL h3index."""
    
    def get_col_spec(self):
        return "h3index"
    
    def bind_processor(self, dialect):
        def process(value):
            if value is not None:
                return str(value)
            return value
        return process
    
    def result_processor(self, dialect, coltype):
        def process(value):
            if value is not None:
                return str(value)  # Return as string for now, can be converted to h3 objects in service layer
            return value
        return process 