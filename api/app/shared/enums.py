"""
Shared enums for the TarmacTrack API.
Single source of truth for all enum definitions to prevent duplication and mismatches.
"""

from enum import Enum


class RunwaySurface(str, Enum):
    """Runway surface types - matches database enum exactly."""
    ASPHALT = "ASPHALT"
    CONCRETE = "CONCRETE"
    GRASS = "GRASS"
    GRAVEL = "GRAVEL"
    DIRT = "DIRT"
    WATER = "WATER"
    UNKNOWN = "UNKNOWN"
    OTHER = "OTHER"


class FlightPhase(str, Enum):
    """Flight phase types - matches database enum exactly."""
    startup = "startup"
    ground = "ground"
    takeoff = "takeoff"
    landing = "landing"
    appear = "appear"
    shutdown = "shutdown"
    disappear = "disappear"
    airborne = "airborne"


class AircraftCategory(str, Enum):
    """Aircraft category types - matches database enum exactly."""
    A0 = "A0"
    A1 = "A1"
    A2 = "A2"
    A3 = "A3"
    A4 = "A4"
    A5 = "A5"
    A6 = "A6"
    A7 = "A7"
    B0 = "B0"
    B1 = "B1"
    B2 = "B2"
    B3 = "B3"
    B4 = "B4"
    B5 = "B5"
    B6 = "B6"
    B7 = "B7"
    C0 = "C0"
    C1 = "C1"
    C2 = "C2"
    C3 = "C3"
    C4 = "C4"
    C5 = "C5"
    C6 = "C6"
    C7 = "C7"
    D0 = "D0"
    D1 = "D1"
    D2 = "D2"
    D3 = "D3"
    D4 = "D4"
    D5 = "D5"
    D6 = "D6"
    D7 = "D7"
    UNKNOWN = "UNKNOWN"


class WakeTurbulenceCategory(str, Enum):
    """Wake turbulence category types - matches database enum exactly."""
    L = "L"  # Light (< 7,000 kg)
    M = "M"  # Medium (7,000 - 136,000 kg)
    H = "H"  # Heavy (> 136,000 kg)
    J = "J"  # Super (A380, AN-225)