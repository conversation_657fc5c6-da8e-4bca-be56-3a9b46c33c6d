from datetime import datetime, timezone, timedelta
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, text, update
from sqlalchemy.orm import selectinload
from geoalchemy2.shape import to_shape
import logging

from app.schemas.airport import Airport, Runway, Geometry, AirportTransition, AirportTransitionsResponse
from app.models.airport import Airport as AirportModel
from app.models.aircraft import AircraftEvent as AircraftEventModel
from app.shared.enums import FlightPhase

logger = logging.getLogger(__name__)


def _convert_geometry(geo_obj) -> Optional[Geometry]:
    """Convert PostGIS geometry to GeoJSON format."""
    if not geo_obj:
        return None

    try:
        shape = to_shape(geo_obj)
        if shape.geom_type == "Point":
            return Geometry(type="Point", coordinates=[shape.x, shape.y])
        elif shape.geom_type == "LineString":
            coords = [[float(coord[0]), float(coord[1])] for coord in shape.coords]
            return Geometry(type="LineString", coordinates=coords)
    except Exception as e:
        logger.error(f"Error converting geometry: {str(e)}")
        return None
    return None


async def get_airport_by_icao(db: AsyncSession, icao: str) -> Optional[Airport]:
    """Get airport information by ICAO code."""
    try:
        # Query airport with runways in a single database round trip
        query = (
            select(AirportModel).options(selectinload(AirportModel.runways)).where(AirportModel.icao == icao.upper())
        )
        result = await db.execute(query)
        airport = result.scalar_one_or_none()

        if not airport:
            return None

        # Convert to Pydantic model
        return Airport(
            icao=airport.icao,
            iata=airport.iata,
            name=airport.name,
            location=_convert_geometry(airport.location),
            elevation_ft=airport.elevation_ft,
            timezone=airport.timezone,
            image_url=airport.image_url,
            runways=[
                Runway(
                    name=runway.name,
                    length_ft=runway.length_ft,
                    width_ft=runway.width_ft,
                    surface=runway.surface,
                    centerline=_convert_geometry(runway.centerline),
                    heading_deg=runway.heading_deg,
                )
                for runway in airport.runways
            ],
        )
    except Exception as e:
        logger.error(f"Error in get_airport_by_icao: {str(e)}", exc_info=True)
        raise


async def get_airport_transitions(
    db: AsyncSession, airport_icao: str, start_time: Optional[datetime] = None, end_time: Optional[datetime] = None
) -> AirportTransitionsResponse:
    """
    Get airport transitions (takeoffs and landings) for a specific airport.

    Args:
        db: Database session
        airport_icao: Airport ICAO code
        start_time: Start time for query (defaults to 24 hours ago if not provided)
        end_time: End time for query (defaults to now if not provided)

    Returns:
        AirportTransitionsResponse with transitions data
    """
    try:
        # Set default times if not provided
        if end_time is None:
            end_time = datetime.now(timezone.utc)
        if start_time is None:
            start_time = end_time - timedelta(hours=24)

        # Ensure start_time is before end_time
        if start_time >= end_time:
            raise ValueError("Start time must be before end time")

        # Query for takeoff and landing events
        query = (
            select(AircraftEventModel)
            .where(
                and_(
                    AircraftEventModel.flight_phase.in_([FlightPhase.takeoff, FlightPhase.landing]),
                    AircraftEventModel.airport_icao == airport_icao.upper(),
                    AircraftEventModel.runway_name.is_not(None),
                    AircraftEventModel.time >= start_time,
                    AircraftEventModel.time <= end_time,
                )
            )
            .order_by(AircraftEventModel.time.desc())
        )

        result = await db.execute(query)
        events = result.scalars().all()

        # Convert to response models
        transitions = [
            AirportTransition(
                time=event.time,
                hex=event.hex,
                flight_phase=event.flight_phase,
                runway_name=event.runway_name,
                flight=event.flight,
                alt_baro=event.alt_baro,
                alt_geom=event.alt_geom,
                gs=event.gs,
                track=event.track,
                baro_rate=event.baro_rate,
                squawk=event.squawk,
                h3_index=event.h3_index,
                h3_res4=event.h3_res4,
            )
            for event in events
        ]

        return AirportTransitionsResponse(
            airport_icao=airport_icao.upper(),
            start_time=start_time,
            end_time=end_time,
            total_transitions=len(transitions),
            transitions=transitions,
        )

    except Exception as e:
        logger.error(f"Error in get_airport_transitions: {str(e)}", exc_info=True)
        raise


async def get_airport_transitions_extended(
    db: AsyncSession, airport_icao: str, start_time: Optional[datetime] = None, end_time: Optional[datetime] = None
) -> AirportTransitionsResponse:
    """
    Get airport transitions (takeoffs and landings) for a specific airport,
    including the previous and next events for each aircraft involved.

    Args:
        db: Database session
        airport_icao: Airport ICAO code
        start_time: Start time for query (defaults to 24 hours ago if not provided)
        end_time: End time for query (defaults to now if not provided)

    Returns:
        AirportTransitionsResponse with extended transitions data including
        previous and next events for each aircraft
    """
    try:
        # Set default times if not provided
        if end_time is None:
            end_time = datetime.now(timezone.utc)
        if start_time is None:
            start_time = end_time - timedelta(hours=24)

        # Ensure start_time is before end_time
        if start_time >= end_time:
            raise ValueError("Start time must be before end time")

        # Use a single complex query with CTEs to get all events in one database call
        query = text(
            """
            WITH transition_events AS (
                -- Get the main takeoff/landing events (allow null runway_name and airport_icao)
                SELECT
                    time, hex, alt_baro, alt_geom, gs, track, baro_rate, squawk,
                    flight, h3_index, h3_res4, flight_phase, airport_icao, runway_name,
                    ROW_NUMBER() OVER (ORDER BY time, hex) as transition_id
                FROM aircraft_events
                WHERE flight_phase IN ('takeoff', 'landing')
                  AND airport_icao = :airport_icao
                  AND runway_name IS NOT NULL
                  AND time >= :start_time
                  AND time <= :end_time
            ),
            previous_events AS (
                -- Get the previous event for EACH individual transition
                SELECT DISTINCT ON (te.transition_id)
                    ae.time, ae.hex, ae.alt_baro, ae.alt_geom, ae.gs, ae.track,
                    ae.baro_rate, ae.squawk, ae.flight, ae.h3_index, ae.h3_res4,
                    ae.flight_phase, ae.airport_icao, ae.runway_name
                FROM transition_events te
                INNER JOIN aircraft_events ae ON ae.hex = te.hex
                WHERE ae.time < te.time
                  AND ae.time >= :extended_start_time
                ORDER BY te.transition_id, ae.time DESC
            ),
            next_events AS (
                -- Get the next event for EACH individual transition
                SELECT DISTINCT ON (te.transition_id)
                    ae.time, ae.hex, ae.alt_baro, ae.alt_geom, ae.gs, ae.track,
                    ae.baro_rate, ae.squawk, ae.flight, ae.h3_index, ae.h3_res4,
                    ae.flight_phase, ae.airport_icao, ae.runway_name
                FROM transition_events te
                INNER JOIN aircraft_events ae ON ae.hex = te.hex
                WHERE ae.time > te.time
                  AND ae.time <= :extended_end_time
                ORDER BY te.transition_id, ae.time ASC
            ),
            all_events AS (
                -- Union all events: transitions, previous, and next
                SELECT time, hex, alt_baro, alt_geom, gs, track, baro_rate, squawk,
                       flight, h3_index, h3_res4, flight_phase, airport_icao, runway_name
                FROM transition_events
                UNION
                SELECT time, hex, alt_baro, alt_geom, gs, track, baro_rate, squawk,
                       flight, h3_index, h3_res4, flight_phase, airport_icao, runway_name
                FROM previous_events
                UNION
                SELECT time, hex, alt_baro, alt_geom, gs, track, baro_rate, squawk,
                       flight, h3_index, h3_res4, flight_phase, airport_icao, runway_name
                FROM next_events
            )
            SELECT DISTINCT
                time, hex, alt_baro, alt_geom, gs, track, baro_rate, squawk,
                flight, h3_index, h3_res4, flight_phase, airport_icao, runway_name
            FROM all_events
            ORDER BY time DESC
        """
        )

        # Extend the time range to capture previous/next events
        # Use a wider window to ensure we get adjacent events
        extended_start_time = start_time - timedelta(hours=2)
        extended_end_time = end_time + timedelta(hours=2)

        result = await db.execute(
            query,
            {
                "airport_icao": airport_icao.upper(),
                "start_time": start_time,
                "end_time": end_time,
                "extended_start_time": extended_start_time,
                "extended_end_time": extended_end_time,
            },
        )

        events = result.fetchall()

        # Convert to response models
        transitions = [
            AirportTransition(
                time=event.time,
                hex=event.hex,
                flight_phase=FlightPhase(event.flight_phase),
                runway_name=event.runway_name,  # Can be None
                flight=event.flight,
                alt_baro=event.alt_baro,
                alt_geom=event.alt_geom,
                gs=event.gs,
                track=event.track,
                baro_rate=event.baro_rate,
                squawk=event.squawk,
                h3_index=event.h3_index,
                h3_res4=event.h3_res4,
            )
            for event in events
        ]

        return AirportTransitionsResponse(
            airport_icao=airport_icao.upper(),
            start_time=start_time,
            end_time=end_time,
            total_transitions=len(transitions),
            transitions=transitions,
        )

    except Exception as e:
        logger.error(f"Error in get_airport_transitions_extended: {str(e)}", exc_info=True)
        raise


async def update_airport_image_url(db: AsyncSession, icao: str, image_url: Optional[str]) -> bool:
    """
    Update the image URL for an airport.

    Args:
        db: Database session
        icao: Airport ICAO code
        image_url: New image URL (None to remove)

    Returns:
        True if updated successfully, False if airport not found
    """
    try:
        query = update(AirportModel).where(AirportModel.icao == icao.upper()).values(image_url=image_url)

        result = await db.execute(query)
        await db.commit()

        # Check if any rows were affected
        if result.rowcount == 0:
            logger.warning(f"Airport {icao} not found for image URL update")
            return False

        logger.info(f"Updated image URL for airport {icao}: {image_url}")
        return True

    except Exception as e:
        logger.error(f"Error updating airport image URL for {icao}: {str(e)}", exc_info=True)
        await db.rollback()
        raise
