from datetime import datetime, timezone, timedelta
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from app.schemas.aircraft import AircraftPosition as AircraftPositionSchema, AircraftTracePosition


async def get_aircraft_positions_at_time(
    db: AsyncSession,
    h3_cells: List[str],
    reference_time: Optional[datetime] = None,
    lookback_seconds: int = 7
) -> List[AircraftPositionSchema]:
    """
    Get aircraft positions within specified H3 cells at a given time.
    
    This function implements the SQL query logic to find the most recent position
    of aircraft within a given set of H3 cells and a specific lookback period.
    It uses a two-step filtering process for efficiency:
    1. A coarse filter using pre-calculated resolution 4 parent cells (which should be indexed).
    2. A fine-grained filter to ensure the position's H3 index is a child of one of the original input cells.
    
    Args:
        db: Database session
        h3_cells: List of H3 cell indexes to search within
        reference_time: Reference timestamp (defaults to NOW)
        lookback_seconds: Lookback window from reference time (default: 7 seconds)
        
    Returns:
        List of aircraft positions
    """
    if not h3_cells:
        return []
    
    if reference_time is None:
        reference_time = datetime.now(timezone.utc)
    
    # Convert the SQL query to SQLAlchemy
    # Build the H3 cells array string for the query and embed parameters directly
    h3_cells_array = ', '.join([f"'{cell}'::h3index" for cell in h3_cells])

    # Calculate time bounds
    start_time = reference_time - timedelta(seconds=lookback_seconds)

    query = text(f"""
        WITH search_area_h3 AS (
            SELECT UNNEST(ARRAY[{h3_cells_array}]) AS h3_index
        ),
        search_area_parents AS (
            SELECT DISTINCT h3_cell_to_parent(h3_index, 4) as h3_res4_parent
            FROM search_area_h3
        ),
        search_params AS (
            SELECT h3_get_resolution((SELECT h3_index FROM search_area_h3 LIMIT 1)) as original_resolution
        )
        SELECT DISTINCT ON (ap.hex)
          ap."time",
          ap.hex,
          ap.alt_baro,
          ap.alt_geom,
          ap.gs,
          ap.track,
          ap.baro_rate,
          ap.squawk,
          ap.flight,
          ap.h3_index
        FROM
          public.aircraft_positions AS ap
          JOIN search_area_parents sap ON ap.h3_res4 = sap.h3_res4_parent
          CROSS JOIN search_params sp
        WHERE
          ap."time" >= :start_time
          AND ap."time" <= :end_time
          AND h3_cell_to_parent(ap.h3_index, sp.original_resolution) IN (
              SELECT h3_index FROM search_area_h3
          )
        ORDER BY
          ap.hex, ap."time" DESC;
    """)
    
    # Execute the query with named parameters
    result = await db.execute(
        query,
        {
            "start_time": start_time,
            "end_time": reference_time
        }
    )
    
    # Convert results to Pydantic models
    positions = []
    for row in result:
        position_data = {
            "time": row.time,
            "hex": row.hex,
            "alt_baro": row.alt_baro,
            "alt_geom": row.alt_geom,
            "gs": row.gs,
            "track": row.track,
            "baro_rate": row.baro_rate,
            "squawk": row.squawk,
            "flight": row.flight,
            "h3_index": row.h3_index,
            "category": None  # Not included in this query, would need join with aircraft table
        }
        positions.append(AircraftPositionSchema(**position_data))

    return positions


async def get_aircraft_trace(
    db: AsyncSession,
    hex: str,
    start_time: datetime,
    end_time: datetime
) -> List[AircraftTracePosition]:
    """
    Get aircraft trace data (all positions) for a specific aircraft within a time range.

    This function retrieves all position records for a given aircraft hex identifier
    within the specified time range, returning only the essential fields for trace visualization.

    Args:
        db: Database session
        hex: Aircraft hex identifier
        start_time: Start of time range
        end_time: End of time range

    Returns:
        List of aircraft trace positions in chronological order
    """
    if not hex:
        return []

    # Simple query to get all positions for the aircraft in the time range
    # Only select the fields we need for trace data
    query = text("""
        SELECT
          ap."time",
          ap.alt_baro,
          ap.gs,
          ap.track,
          ap.baro_rate,
          ap.h3_index
        FROM
          public.aircraft_positions AS ap
        WHERE
          ap.hex = :hex
          AND ap."time" >= :start_time
          AND ap."time" <= :end_time
        ORDER BY
          ap."time" ASC;
    """)

    # Execute the query with named parameters
    result = await db.execute(
        query,
        {
            "hex": hex.lower(),  # Ensure hex is lowercase for consistency
            "start_time": start_time,
            "end_time": end_time
        }
    )

    # Convert results to Pydantic models
    positions = []
    for row in result:
        position_data = {
            "time": row.time,
            "alt_baro": row.alt_baro,
            "gs": row.gs,
            "track": row.track,
            "baro_rate": row.baro_rate,
            "h3_index": row.h3_index
        }
        positions.append(AircraftTracePosition(**position_data))

    return positions
