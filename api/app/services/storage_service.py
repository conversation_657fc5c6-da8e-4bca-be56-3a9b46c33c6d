"""
Storage service for handling file uploads to Backblaze B2.
"""

import io
import logging
from typing import <PERSON>ple
from PIL import Image
from b2sdk.v2 import InMemoryAccountInfo, B2Api
from app.core.config import settings

logger = logging.getLogger(__name__)


class StorageService:
    """Service for handling file storage operations with Backblaze B2."""

    def __init__(self):
        """Initialize the storage service with B2 credentials."""
        self.account_info = InMemoryAccountInfo()
        self.b2_api = B2Api(self.account_info)
        self.bucket = None
        self._authenticated = False

    async def authenticate(self) -> bool:
        """Authenticate with Backblaze B2."""
        try:
            if not settings.B2_KEY_ID or not settings.B2_APPLICATION_KEY:
                logger.error("B2 credentials not configured")
                return False

            self.b2_api.authorize_account("production", settings.B2_KEY_ID, settings.B2_APPLICATION_KEY)
            self.bucket = self.b2_api.get_bucket_by_name(settings.B2_BUCKET_NAME)
            self._authenticated = True
            logger.info(f"Successfully authenticated with B2 bucket: {settings.B2_BUCKET_NAME}")
            return True
        except Exception as e:
            logger.error(f"Failed to authenticate with B2: {e}")
            return False

    def _ensure_authenticated(self):
        """Ensure the service is authenticated before operations."""
        if not self._authenticated:
            raise RuntimeError("Storage service not authenticated. Call authenticate() first.")

    def process_image(
        self, image_data: bytes, max_width: int = 1200, max_height: int = 600, quality: int = 85, use_webp: bool = True
    ) -> Tuple[bytes, str]:
        """
        Process and optimize an image for web display.

        Args:
            image_data: Raw image bytes
            max_width: Maximum width in pixels
            max_height: Maximum height in pixels
            quality: Image quality (1-100)
            use_webp: Whether to use WebP format (better compression)

        Returns:
            Tuple of (processed_image_bytes, content_type)
        """
        try:
            # Open the image
            image = Image.open(io.BytesIO(image_data))

            # Convert to RGB if necessary (handles RGBA, P mode, etc.)
            if image.mode in ("RGBA", "LA", "P"):
                # Create a white background for transparency
                background = Image.new("RGB", image.size, (255, 255, 255))
                if image.mode == "P":
                    image = image.convert("RGBA")
                background.paste(image, mask=image.split()[-1] if image.mode == "RGBA" else None)
                image = background
            elif image.mode != "RGB":
                image = image.convert("RGB")

            # Calculate new dimensions maintaining aspect ratio
            original_width, original_height = image.size
            ratio = min(max_width / original_width, max_height / original_height)

            if ratio < 1:  # Only resize if image is larger than max dimensions
                new_width = int(original_width * ratio)
                new_height = int(original_height * ratio)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # Save in optimal format
            output = io.BytesIO()
            if use_webp:
                try:
                    image.save(output, format="WebP", quality=quality, optimize=True)
                    content_type = "image/webp"
                    file_extension = "webp"
                except Exception:
                    # Fallback to JPEG if WebP fails
                    output = io.BytesIO()
                    image.save(output, format="JPEG", quality=quality, optimize=True)
                    content_type = "image/jpeg"
                    file_extension = "jpg"
            else:
                image.save(output, format="JPEG", quality=quality, optimize=True)
                content_type = "image/jpeg"
                file_extension = "jpg"

            processed_data = output.getvalue()

            logger.info(
                f"Image processed: {original_width}x{original_height} -> "
                f"{image.size[0]}x{image.size[1]} ({file_extension})"
            )
            return processed_data, content_type

        except Exception as e:
            logger.error(f"Failed to process image: {e}")
            raise ValueError(f"Invalid image data: {e}")

    async def upload_airport_image(self, icao: str, image_data: bytes, filename: str) -> str:
        """
        Upload an airport image to B2 storage.

        Args:
            icao: Airport ICAO code
            image_data: Raw image bytes
            filename: Original filename

        Returns:
            Public URL of the uploaded image
        """
        self._ensure_authenticated()

        try:
            # Process the image
            processed_data, content_type = self.process_image(image_data)

            # Generate the storage path - use webp for better compression
            file_extension = "webp" if content_type == "image/webp" else "jpg"
            storage_path = f"airports/{icao.upper()}/image.{file_extension}"

            # Upload to B2
            file_info = self.bucket.upload_bytes(
                processed_data,
                storage_path,
                content_type=content_type,
                file_infos={"original_filename": filename, "airport_icao": icao.upper(), "processed": "true"},
            )

            # Generate S3-compatible public URL
            # Format: https://s3.us-west-005.backblazeb2.com/bucket-name/file-path
            s3_url = f"https://s3.us-west-005.backblazeb2.com/{settings.B2_BUCKET_NAME}/{storage_path}"

            logger.info(f"Successfully uploaded airport image for {icao}: {storage_path}")
            return s3_url

        except Exception as e:
            logger.error(f"Failed to upload airport image for {icao}: {e}")
            raise RuntimeError(f"Upload failed: {e}")

    async def get_airport_image_info(self, icao: str) -> dict:
        """
        Get information about an airport's current image.

        Args:
            icao: Airport ICAO code

        Returns:
            Dict with image info: {'exists': bool, 'url': str|None, 'filename': str|None, 'size': int|None}
        """
        self._ensure_authenticated()

        try:
            # Try both possible file extensions
            possible_paths = [f"airports/{icao.upper()}/image.webp", f"airports/{icao.upper()}/image.jpg"]

            for file_version, _ in self.bucket.ls(folder_to_list=f"airports/{icao.upper()}/"):
                if file_version.file_name in possible_paths:
                    # Generate S3-compatible URL
                    s3_url = f"https://s3.us-west-005.backblazeb2.com/{settings.B2_BUCKET_NAME}/{file_version.file_name}"
                    return {
                        'exists': True,
                        'url': s3_url,
                        'filename': file_version.file_name,
                        'size': file_version.size
                    }

            return {'exists': False, 'url': None, 'filename': None, 'size': None}

        except Exception as e:
            logger.error(f"Failed to get airport image info for {icao}: {e}")
            return {'exists': False, 'url': None, 'filename': None, 'size': None}

    async def delete_airport_image(self, icao: str) -> bool:
        """
        Delete an airport image from B2 storage.

        Args:
            icao: Airport ICAO code

        Returns:
            True if deleted successfully, False if not found
        """
        self._ensure_authenticated()

        try:
            # Try both possible file extensions
            possible_paths = [f"airports/{icao.upper()}/image.webp", f"airports/{icao.upper()}/image.jpg"]

            deleted = False
            # Find and delete any existing files
            for file_version, _ in self.bucket.ls(folder_to_list=f"airports/{icao.upper()}/"):
                if file_version.file_name in possible_paths:
                    self.bucket.delete_file_version(file_version.id_, file_version.file_name)
                    logger.info(f"Successfully deleted airport image for {icao}: {file_version.file_name}")
                    deleted = True

            if not deleted:
                logger.warning(f"Airport image not found for {icao}")
                return False

            return True

        except Exception as e:
            logger.error(f"Failed to delete airport image for {icao}: {e}")
            raise RuntimeError(f"Delete failed: {e}")


# Global storage service instance
storage_service = StorageService()


async def get_storage_service() -> StorageService:
    """Dependency to get the storage service."""
    if not storage_service._authenticated:
        await storage_service.authenticate()
    return storage_service
