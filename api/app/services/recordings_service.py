import boto3
import re
from datetime import datetime
from typing import List, Optional, Tuple
from botocore.exceptions import ClientError, NoCredentialsError
from fastapi import HTTPException, status

from app.core.config import settings
from app.schemas.recordings import RecordingFile, StationInfo, DateInfo


class RecordingsService:
    """Service for managing audio recordings in Cloudflare R2 storage."""
    
    def __init__(self):
        """Initialize the recordings service with R2 client."""
        try:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=settings.R2_ACCESS_KEY_ID,
                aws_secret_access_key=settings.R2_SECRET_ACCESS_KEY,
                endpoint_url=settings.R2_ENDPOINT_URL,
                region_name=settings.R2_REGION
            )
            self.bucket_name = settings.R2_BUCKET_NAME
        except NoCredentialsError:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="R2 storage credentials not configured"
            )
    
    def _parse_filename(self, filename: str) -> Optional[Tuple[datetime, int]]:
        """
        Parse recording filename to extract start time and duration.

        Format: YYYYMMDD_HHMMSS_mmm_dDDDDDD.webm
        Example: 20250723_031946_192_d002950.webm

        Returns:
            Tuple of (start_time, duration_ms) or None if parsing fails
        """
        pattern = r'^(\d{8})_(\d{6})_(\d{3})_d(\d+)\.webm$'
        match = re.match(pattern, filename)
        
        if not match:
            return None
        
        date_str, time_str, ms_str, duration_str = match.groups()
        
        try:
            # Parse date and time
            year = int(date_str[:4])
            month = int(date_str[4:6])
            day = int(date_str[6:8])
            hour = int(time_str[:2])
            minute = int(time_str[2:4])
            second = int(time_str[4:6])
            microsecond = int(ms_str) * 1000  # Convert ms to microseconds
            
            start_time = datetime(year, month, day, hour, minute, second, microsecond)
            duration_ms = int(duration_str)
            
            return start_time, duration_ms
        except ValueError:
            return None
    
    def _format_date_for_s3_path(self, date_str: str) -> str:
        """
        Convert YYYYMMDD format to YYYY/MM/DD for S3 path.
        
        Args:
            date_str: Date in YYYYMMDD format
            
        Returns:
            Date in YYYY/MM/DD format
        """
        if len(date_str) != 8:
            raise ValueError("Date must be in YYYYMMDD format")
        
        return f"{date_str[:4]}/{date_str[4:6]}/{date_str[6:8]}"
    
    async def list_recordings(self, station: str, date: str) -> List[RecordingFile]:
        """
        List all recordings for a given station and date.
        
        Args:
            station: Station identifier (e.g., "4FL5_122900")
            date: Date in YYYYMMDD format (e.g., "20250723")
            
        Returns:
            List of RecordingFile objects
        """
        try:
            # Convert date format for S3 path
            formatted_date = self._format_date_for_s3_path(date)
            prefix = f"{station}/{formatted_date}/"
            
            # List objects in S3
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=prefix
            )
            
            recordings = []
            
            if 'Contents' in response:
                for obj in response['Contents']:
                    filename = obj['Key'].split('/')[-1]  # Get just the filename
                    
                    # Parse filename to get start time and duration
                    parsed = self._parse_filename(filename)
                    if parsed is None:
                        continue  # Skip files that don't match expected format
                    
                    start_time, duration_ms = parsed
                    
                    # Generate API download URL (proxy through our API)
                    # Always use production API for recordings (handles auth and CORS)
                    download_url = f"https://api.skytraces.com/api/v1/recordings/{station}/{date}/{filename}"

                    recording = RecordingFile(
                        filename=filename,
                        start_time=start_time,
                        duration_ms=duration_ms,
                        size_bytes=obj['Size'],
                        download_url=download_url
                    )
                    recordings.append(recording)
            
            # Sort by start time
            recordings.sort(key=lambda x: x.start_time)
            
            return recordings
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchBucket':
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Storage bucket not found"
                )
            elif error_code == 'AccessDenied':
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Access denied to storage"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Storage error: {str(e)}"
                )

    async def get_recording_stream(self, station: str, date: str, filename: str):
        """
        Get a recording file as a stream for download/streaming.

        Args:
            station: Station identifier
            date: Date in YYYYMMDD format
            filename: Recording filename

        Returns:
            S3 object response for streaming
        """
        try:
            # Convert date format for S3 key
            formatted_date = self._format_date_for_s3_path(date)
            key = f"{station}/{formatted_date}/{filename}"

            # Get object from S3
            response = self.s3_client.get_object(
                Bucket=self.bucket_name,
                Key=key
            )

            return response

        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchKey':
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Recording not found"
                )
            elif error_code == 'AccessDenied':
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Access denied to storage"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Storage error: {str(e)}"
                )




# Global service instance
recordings_service = RecordingsService()
