from datetime import datetime
from typing import List, Optional, Union
from pydantic import BaseModel, Field
from app.shared.enums import RunwaySurface, FlightPhase

class Geometry(BaseModel):
    type: str = Field(..., description="Geometry type (Point or LineString)")
    coordinates: Union[List[float], List[List[float]]] = Field(..., description="Geometry coordinates")

class Runway(BaseModel):
    name: str = Field(..., description="Runway identifier (e.g., '04L/22R')")
    length_ft: Optional[int] = Field(None, description="Runway length in feet")
    width_ft: Optional[int] = Field(None, description="Runway width in feet")
    surface: RunwaySurface = Field(..., description="Runway surface type")
    centerline: Optional[Geometry] = Field(None, description="Runway centerline as LineString")
    heading_deg: Optional[int] = Field(None, description="Runway heading in degrees")

    class Config:
        from_attributes = True

class Airport(BaseModel):
    icao: str = Field(..., description="ICAO airport code (4 characters)", min_length=4, max_length=4)
    iata: Optional[str] = Field(None, description="IATA airport code (3 characters)", min_length=3, max_length=3)
    name: Optional[str] = Field(None, description="Airport name")
    location: Optional[Geometry] = Field(None, description="Airport location as Point")
    elevation_ft: Optional[int] = Field(None, description="Airport elevation in feet")
    timezone: Optional[str] = Field(None, description="Airport timezone (IANA timezone identifier)")
    image_url: Optional[str] = Field(None, description="URL to airport image stored in cloud storage")
    runways: List[Runway] = Field(default_factory=list, description="List of runways at the airport")

    class Config:
        from_attributes = True


class AirportTransition(BaseModel):
    """Airport transition event (includes takeoff/landing and related events)."""
    time: datetime = Field(..., description="Event timestamp")
    hex: str = Field(..., description="Aircraft hex identifier")
    flight_phase: FlightPhase = Field(..., description="Flight phase")
    runway_name: Optional[str] = Field(None, description="Runway name where event occurred (null for non-runway events)")
    flight: Optional[str] = Field(None, description="Flight number/callsign")
    alt_baro: Optional[int] = Field(None, description="Barometric altitude in feet")
    alt_geom: Optional[int] = Field(None, description="Geometric altitude in feet")
    gs: Optional[int] = Field(None, description="Ground speed in knots")
    track: Optional[int] = Field(None, description="Track angle in degrees")
    baro_rate: Optional[int] = Field(None, description="Barometric rate of climb/descent in feet per minute")
    squawk: Optional[int] = Field(None, description="Squawk code")
    h3_index: Optional[str] = Field(None, description="H3 geographic index for position")
    h3_res4: Optional[str] = Field(None, description="H3 geographic index at resolution 4")

    class Config:
        from_attributes = True


class AirportTransitionsResponse(BaseModel):
    """Response model for airport transitions endpoint."""
    airport_icao: str = Field(..., description="Airport ICAO code")
    start_time: datetime = Field(..., description="Query start time")
    end_time: datetime = Field(..., description="Query end time")
    total_transitions: int = Field(..., description="Total number of transitions found")
    transitions: List[AirportTransition] = Field(..., description="List of transition events and related aircraft events")

    class Config:
        from_attributes = True
