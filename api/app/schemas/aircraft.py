from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, validator

from app.shared.enums import FlightPhase, AircraftCategory, WakeTurbulenceCategory


class AircraftPositionBase(BaseModel):
    """Base model for aircraft position data."""
    alt_baro: Optional[int] = Field(None, description="Barometric altitude in feet")
    alt_geom: Optional[int] = Field(None, description="Geometric altitude in feet")
    gs: Optional[int] = Field(None, description="Ground speed in knots")
    track: Optional[int] = Field(None, description="Track angle in degrees")
    baro_rate: Optional[int] = Field(None, description="Barometric rate of climb/descent in feet per minute")
    squawk: Optional[int] = Field(None, description="Squawk code")
    flight: Optional[str] = Field(None, description="Flight number/callsign")
    h3_index: Optional[str] = Field(None, description="H3 geographic index for position")
    h3_res4: Optional[str] = Field(None, description="H3 geographic index at resolution 4")

    class Config:
        from_attributes = True


class AircraftEventBase(AircraftPositionBase):
    """Base model for aircraft event data."""
    flight_phase: FlightPhase = Field(..., description="Current flight phase")
    airport_icao: Optional[str] = Field(None, description="Airport ICAO code")
    runway_name: Optional[str] = Field(None, description="Runway name")


class AircraftBase(BaseModel):
    hex: str
    category: Optional[AircraftCategory] = None
    registration: Optional[str] = None


class Aircraft(AircraftBase):
    """Aircraft model with complete information including TAR1090 enhanced fields."""

    # TAR1090 enhanced fields
    icao_type_designator: Optional[str] = Field(None, description="ICAO aircraft type designator (e.g., A320, B738)")
    type_description: Optional[str] = Field(None, description="ICAO type description code (e.g., L2J)")
    wake_turbulence_category: Optional[WakeTurbulenceCategory] = Field(None, description="Wake turbulence category")
    aircraft_description: Optional[str] = Field(None, description="Full aircraft description/model name")
    operator: Optional[str] = Field(None, description="Aircraft owner/operator name")
    year_built: Optional[int] = Field(None, description="Year the aircraft was manufactured")

    # Flag fields
    is_military: bool = Field(False, description="True if this is a military aircraft")
    is_interesting: bool = Field(False, description="True if this is a special/interesting aircraft")
    is_pia: bool = Field(False, description="True if Privacy ICAO Address (blocked registration)")
    is_ladd: bool = Field(False, description="True if Limiting Aircraft Data Displayed")

    # Metadata fields
    country_code: Optional[str] = Field(None, description="Country code derived from ICAO hex ranges")
    database_source: Optional[str] = Field(None, description="Source of the aircraft data")
    last_updated: Optional[datetime] = Field(None, description="Last time this record was updated")
    created_at: Optional[datetime] = Field(None, description="When this record was created")

    class Config:
        from_attributes = True


class AircraftPosition(AircraftPositionBase):
    """Aircraft position data with timestamp and hex identifier."""
    time: datetime = Field(..., description="Timestamp of position report")
    hex: str = Field(..., description="Unique aircraft identifier")
    category: Optional[AircraftCategory] = Field(None, description="Aircraft category")


class AircraftEvent(AircraftEventBase):
    """Aircraft event data with timestamp and hex identifier."""
    time: datetime = Field(..., description="Timestamp of event")
    hex: str = Field(..., description="Unique aircraft identifier")
    category: Optional[AircraftCategory] = Field(None, description="Aircraft category")


class AircraftQuery(BaseModel):
    """Query parameters for aircraft search."""
    hex: Optional[List[str]] = Field(None, description="Filter by aircraft hex codes")
    flight: Optional[List[str]] = Field(None, description="Filter by flight numbers")
    category: Optional[List[AircraftCategory]] = Field(None, description="Filter by aircraft category")

    @validator('hex', 'flight', 'category', pre=True)
    def split_comma_separated(cls, v):
        if isinstance(v, str):
            return [item.strip() for item in v.split(',') if item.strip()]
        return v


class AircraftPositionsAtTimeRequest(BaseModel):
    """Request model for aircraft positions at time endpoint."""
    h3_cells: List[str] = Field(..., description="List of H3 cell indexes to search within", min_items=1)
    reference_time: Optional[datetime] = Field(None, description="Reference timestamp (defaults to NOW)")
    lookback_seconds: int = Field(7, description="Lookback window from reference time in seconds", ge=1, le=3600)

    @validator('h3_cells')
    def validate_h3_cells(cls, v):
        if not v:
            raise ValueError("At least one H3 cell must be provided")
        # Basic validation - H3 cells should be 15-character hex strings
        for cell in v:
            if not isinstance(cell, str) or len(cell) != 15:
                raise ValueError(f"Invalid H3 cell format: {cell}")
        return v


class AircraftPositionsAtTimeResponse(BaseModel):
    """Response model for aircraft positions at time endpoint."""
    positions: List[AircraftPosition] = Field(..., description="List of aircraft positions")
    query_info: dict = Field(..., description="Information about the query parameters used")

    class Config:
        from_attributes = True


class AircraftTracePosition(BaseModel):
    """Simplified aircraft position model for trace data."""
    time: datetime = Field(..., description="Timestamp of position report")
    alt_baro: Optional[int] = Field(None, description="Barometric altitude in feet")
    gs: Optional[int] = Field(None, description="Ground speed in knots")
    track: Optional[int] = Field(None, description="Track angle in degrees")
    baro_rate: Optional[int] = Field(None, description="Barometric rate of climb/descent in feet per minute")
    h3_index: Optional[str] = Field(None, description="H3 geographic index for position")

    class Config:
        from_attributes = True


class AircraftTraceResponse(BaseModel):
    """Response model for aircraft trace endpoint."""
    hex: str = Field(..., description="Aircraft hex identifier")
    positions: List[AircraftTracePosition] = Field(..., description="List of aircraft positions in chronological order")
    query_info: dict = Field(..., description="Information about the query parameters used")

    class Config:
        from_attributes = True