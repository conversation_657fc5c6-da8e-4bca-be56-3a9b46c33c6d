"""
Pydantic schemas for TarmacTrack API.
Response and request models for all endpoints.
"""

from .aircraft import (
    Aircraft,
    AircraftPosition,
    AircraftEvent,
    AircraftPositionBase,
    AircraftEventBase,
    AircraftBase,
    AircraftQuery,
    AircraftPositionsAtTimeRequest,
    AircraftPositionsAtTimeResponse,
    AircraftTracePosition,
    AircraftTraceResponse
)
from .airport import Airport, Runway, Geometry, AirportTransition, AirportTransitionsResponse

__all__ = [
    # Aircraft schemas
    "Aircraft",
    "AircraftPosition",
    "AircraftEvent",
    "AircraftPositionBase",
    "AircraftEventBase",
    "AircraftBase",
    "AircraftQuery",
    "AircraftPositionsAtTimeRequest",
    "AircraftPositionsAtTimeResponse",
    "AircraftTracePosition",
    "AircraftTraceResponse",
    # Airport schemas
    "Airport",
    "Runway",
    "Geometry",
    "AirportTransition",
    "AirportTransitionsResponse"
]