"""
Schemas for image upload and management.
"""

from pydantic import BaseModel, Field
from typing import Optional


class ImageUploadResponse(BaseModel):
    """Response model for image upload."""

    success: bool = Field(..., description="Whether the upload was successful")
    message: str = Field(..., description="Success or error message")
    image_url: Optional[str] = Field(None, description="URL of the uploaded image")
    icao: str = Field(..., description="Airport ICAO code")


class ImageDeleteResponse(BaseModel):
    """Response model for image deletion."""

    success: bool = Field(..., description="Whether the deletion was successful")
    message: str = Field(..., description="Success or error message")
    icao: str = Field(..., description="Airport ICAO code")


class AirportImageInfo(BaseModel):
    """Information about an airport's image."""

    icao: str = Field(..., description="Airport ICAO code")
    has_image: bool = Field(..., description="Whether the airport has an image")
    image_url: Optional[str] = Field(None, description="URL of the airport image if available")
    last_updated: Optional[str] = Field(None, description="When the image was last updated")
