from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field


class RecordingFile(BaseModel):
    """Schema for a single recording file."""
    filename: str = Field(..., description="Recording filename")
    start_time: datetime = Field(..., description="Recording start time in ISO format")
    duration_ms: int = Field(..., description="Duration in milliseconds")
    size_bytes: int = Field(..., description="File size in bytes")
    download_url: str = Field(..., description="Direct URL to download the recording")


class RecordingsListResponse(BaseModel):
    """Schema for recordings list response."""
    station: str = Field(..., description="Station identifier")
    date: str = Field(..., description="Date in YYYYMMDD format")
    total_recordings: int = Field(..., description="Total number of recordings")
    total_duration_ms: int = Field(..., description="Total duration of all recordings in milliseconds")
    total_size_bytes: int = Field(..., description="Total size of all recordings")
    recordings: List[RecordingFile] = Field(..., description="List of recording files")


class StationInfo(BaseModel):
    """Schema for station information."""
    name: str = Field(..., description="Station name")
    last_recording: Optional[datetime] = Field(None, description="Last recording timestamp")
    total_recordings: int = Field(..., description="Total number of recordings")


class StationsListResponse(BaseModel):
    """Schema for stations list response."""
    stations: List[StationInfo] = Field(..., description="List of available stations")


class DateInfo(BaseModel):
    """Schema for date information."""
    date: str = Field(..., description="Date in YYYYMMDD format")
    recording_count: int = Field(..., description="Number of recordings on this date")
    total_duration_ms: int = Field(..., description="Total duration for this date in milliseconds")
    total_size_bytes: int = Field(..., description="Total size for this date")


class DatesListResponse(BaseModel):
    """Schema for dates list response."""
    station: str = Field(..., description="Station identifier")
    dates: List[DateInfo] = Field(..., description="List of available dates")


class ErrorResponse(BaseModel):
    """Schema for error responses."""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[dict] = Field(None, description="Additional error details")
