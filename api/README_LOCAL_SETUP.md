# TarmacTrack API - Local Development Setup

## Quick Start

### 1. Install Dependencies
```bash
cd api
pip install -r requirements.txt
```

### 2. Run the API Server
```bash
python run_local.py
```

The API will be available at:
- **API Base**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

### 3. Test the New TransitionsExtended Endpoint
```bash
python test_transitionsextended.py
```

## New TransitionsExtended Endpoint

### Endpoint URL
```
GET /api/v1/airports/{icao}/transitionsextended
```

### Parameters
- `icao` (required): 4-character airport ICAO code
- `start_time` (optional): Start time in ISO format (defaults to 24h ago)
- `end_time` (optional): End time in ISO format (defaults to now)

### Example Usage

#### Basic Request
```bash
curl "http://localhost:8000/api/v1/airports/KJFK/transitionsextended"
```

#### With Time Range
```bash
curl "http://localhost:8000/api/v1/airports/KJFK/transitionsextended?start_time=2024-01-01T12:00:00Z&end_time=2024-01-01T18:00:00Z"
```

### What's Different from Regular Transitions?

**Regular `/transitions` endpoint:**
- Returns only takeoff and landing events at the airport

**New `/transitionsextended` endpoint:**
- Returns takeoff and landing events at the airport
- **PLUS** the previous event for each transition (for that specific aircraft)
- **PLUS** the next event for each transition (for that specific aircraft)

### Example Response Structure
```json
{
  "airport_icao": "KJFK",
  "start_time": "2024-01-01T12:00:00Z",
  "end_time": "2024-01-01T18:00:00Z",
  "total_transitions": 45,
  "transitions": [
    {
      "time": "2024-01-01T17:45:00Z",
      "hex": "a12345",
      "flight_phase": "landing",
      "runway_name": "04L",
      "flight": "UAL123",
      "alt_baro": 100,
      "gs": 140,
      ...
    },
    {
      "time": "2024-01-01T17:44:30Z",
      "hex": "a12345",
      "flight_phase": "airborne",
      "runway_name": null,
      "flight": "UAL123",
      "alt_baro": 500,
      "gs": 160,
      ...
    }
  ]
}
```

## Development Notes

### Environment Variables
The API uses these environment variables (configured in `.env`):
- `DATABASE_URL`: PostgreSQL connection string
- `ALLOWED_ORIGINS`: CORS allowed origins
- `ENVIRONMENT`: development/production

### Database Connection
The API connects to your TimescaleDB cloud instance automatically using the credentials in `.env`.

### Auto-Reload
The development server (`run_local.py`) includes auto-reload, so changes to the code will automatically restart the server.

## Testing Different Airports

You can test with different airports by changing the ICAO code in the test script or API calls:
- `KJFK` - John F. Kennedy International Airport
- `KLAX` - Los Angeles International Airport  
- `KORD` - Chicago O'Hare International Airport
- `KATL` - Hartsfield-Jackson Atlanta International Airport

## Troubleshooting

### Port Already in Use
If port 8000 is already in use, you can change it in `run_local.py`:
```python
uvicorn.run(
    "app.main:app",
    host="0.0.0.0",
    port=8001,  # Change this
    reload=True,
    ...
)
```

### Database Connection Issues
Check that your `.env` file has the correct `DATABASE_URL` and that your TimescaleDB instance is accessible.

### Import Errors
Make sure you're in the `api` directory when running the scripts and that all dependencies are installed.
