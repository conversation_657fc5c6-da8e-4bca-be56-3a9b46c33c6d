#!/usr/bin/env python3
"""
Test script for the new transitionsextended endpoint.
This script tests the new endpoint to ensure it works correctly.
"""

import requests
from datetime import datetime, timezone, timedelta

def test_api_endpoint():
    """Test the API endpoint via HTTP requests."""

    base_url = "http://localhost:8000"
    test_icao = "KJFK"  # You can change this to any airport

    # Set time range for last 24 hours
    end_time = datetime.now(timezone.utc)
    start_time = end_time - timedelta(hours=24)

    print(f"🧪 Testing API endpoints for {test_icao}")
    print(f"📅 Time range: {start_time.isoformat()} to {end_time.isoformat()}")
    print("-" * 60)

    # Test parameters
    params = {
        'start_time': start_time.isoformat(),
        'end_time': end_time.isoformat()
    }

    try:
        # Test health endpoint first
        print("🔍 Testing health endpoint...")
        health_response = requests.get(f"{base_url}/health")
        if health_response.status_code == 200:
            print("✅ Health check passed")
        else:
            print(f"❌ Health check failed: {health_response.status_code}")
            return

        # Test regular transitions
        print(f"\n📊 Testing regular transitions endpoint...")
        regular_url = f"{base_url}/api/v1/airports/{test_icao}/transitions"
        regular_response = requests.get(regular_url, params=params)

        if regular_response.status_code == 200:
            regular_data = regular_response.json()
            print(f"✅ Regular transitions: {regular_data['total_transitions']} events")
        else:
            print(f"❌ Regular transitions failed: {regular_response.status_code}")
            print(f"Response: {regular_response.text}")
            return

        # Test extended transitions
        print(f"\n🔍 Testing extended transitions endpoint...")
        extended_url = f"{base_url}/api/v1/airports/{test_icao}/transitionsextended"
        extended_response = requests.get(extended_url, params=params)

        if extended_response.status_code == 200:
            extended_data = extended_response.json()
            print(f"✅ Extended transitions: {extended_data['total_transitions']} events")

            # Compare results
            regular_count = regular_data['total_transitions']
            extended_count = extended_data['total_transitions']
            additional_events = extended_count - regular_count

            print(f"\n📈 Comparison:")
            print(f"   Regular: {regular_count} events")
            print(f"   Extended: {extended_count} events")
            print(f"   Additional: {additional_events} events")

            # Show sample events
            if extended_data['transitions']:
                print(f"\n📋 Sample extended transitions (first 5):")
                for i, transition in enumerate(extended_data['transitions'][:5]):
                    runway = transition.get('runway_name', 'N/A')
                    flight = transition.get('flight', 'N/A')
                    print(f"   {i+1}. {transition['time']} - {transition['hex']} - {transition['flight_phase']} - RWY:{runway} - FLT:{flight}")

            print(f"\n✅ All tests completed successfully!")
            print(f"🌐 View API docs at: {base_url}/docs")

        else:
            print(f"❌ Extended transitions failed: {extended_response.status_code}")
            print(f"Response: {extended_response.text}")

    except requests.exceptions.ConnectionError:
        print(f"❌ Could not connect to API at {base_url}")
        print("💡 Make sure the API is running with: python run_local.py")
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_api_endpoint()
