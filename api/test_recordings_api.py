#!/usr/bin/env python3
"""
Test script for the recordings API endpoints.

This script tests the recordings API by making requests to the local development server.
Make sure to start the API server first with: python run_local.py
"""

import asyncio
import aiohttp
import json
from datetime import datetime


async def test_recordings_api():
    """Test the recordings API endpoints."""
    base_url = "http://localhost:8000/api/v1"
    
    # Test data from the specification
    station = "4FL5_122900"
    date = "20250723"
    
    async with aiohttp.ClientSession() as session:
        print("🎵 Testing Recordings API")
        print("=" * 50)
        
        # Test 1: List recordings for a station and date
        print(f"\n1. Testing GET /recordings/{station}/{date}")
        try:
            async with session.get(f"{base_url}/recordings/{station}/{date}") as response:
                print(f"   Status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"   Station: {data['station']}")
                    print(f"   Date: {data['date']}")
                    print(f"   Total recordings: {data['total_recordings']}")
                    print(f"   Total duration: {data['total_duration_ms']:,} ms ({data['total_duration_ms']/1000:.2f} seconds)")
                    print(f"   Total size: {data['total_size_bytes']:,} bytes")
                    
                    if data['recordings']:
                        print(f"\n   First few recordings:")
                        for i, recording in enumerate(data['recordings'][:3]):
                            print(f"     {i+1}. {recording['filename']}")
                            print(f"        Start: {recording['start_time']}")
                            print(f"        Duration: {recording['duration_ms']}ms ({recording['duration_ms']/1000:.2f}s)")
                            print(f"        Size: {recording['size_bytes']:,} bytes")
                            print(f"        Download URL: {recording['download_url']}")
                        
                        if len(data['recordings']) > 3:
                            print(f"     ... and {len(data['recordings']) - 3} more recordings")
                        
                        # Test that download URLs are properly formatted and files are WebM
                        first_recording = data['recordings'][0]
                        download_url = first_recording['download_url']
                        filename = first_recording['filename']
                        print(f"   Sample download URL: {download_url}")
                        print(f"   Sample filename: {filename}")

                        if filename.endswith('.webm'):
                            print("   ✅ File format is WebM")
                        else:
                            print("   ❌ File format is not WebM")

                        if download_url.startswith('https://api.skytraces.com'):
                            print("   ✅ Download URL format looks correct")
                        else:
                            print("   ❌ Download URL format may be incorrect")
                    else:
                        print("   No recordings found")
                else:
                    error_text = await response.text()
                    print(f"   Error: {error_text}")

        except Exception as e:
            print(f"   Exception: {e}")

        # Test 2: Test error cases
        print(f"\n2. Testing error cases")
        
        # Invalid date format
        print(f"   Testing invalid date format...")
        try:
            async with session.get(f"{base_url}/recordings/{station}/invalid-date") as response:
                print(f"   Invalid date status: {response.status}")
                if response.status != 200:
                    error_data = await response.json()
                    print(f"   Error message: {error_data.get('detail', 'No detail')}")
        except Exception as e:
            print(f"   Exception: {e}")
        
        # Invalid station format
        print(f"   Testing invalid station format...")
        try:
            async with session.get(f"{base_url}/recordings/invalid@station/{date}") as response:
                print(f"   Invalid station status: {response.status}")
                if response.status != 200:
                    error_data = await response.json()
                    print(f"   Error message: {error_data.get('detail', 'No detail')}")
        except Exception as e:
            print(f"   Exception: {e}")
        
        # Non-existent date
        print(f"   Testing non-existent date...")
        try:
            fake_date = "20990101"  # Future date
            async with session.get(f"{base_url}/recordings/{station}/{fake_date}") as response:
                print(f"   Non-existent date status: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"   Recordings found: {data['total_recordings']}")
                else:
                    error_data = await response.json()
                    print(f"   Error message: {error_data.get('detail', 'No detail')}")
        except Exception as e:
            print(f"   Exception: {e}")
        
        print("\n" + "=" * 50)
        print("✅ Recordings API testing completed!")


async def test_health_check():
    """Test that the API server is running."""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8000/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ API server is running (version: {data.get('version', 'unknown')})")
                    return True
                else:
                    print(f"❌ API server health check failed: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Cannot connect to API server: {e}")
        print("   Make sure to start the server with: python run_local.py")
        return False


async def main():
    """Main test function."""
    print("🚀 Starting Recordings API Tests")
    print("=" * 50)
    
    # Check if server is running
    if not await test_health_check():
        return
    
    # Run the recordings API tests
    await test_recordings_api()


if __name__ == "__main__":
    asyncio.run(main())
