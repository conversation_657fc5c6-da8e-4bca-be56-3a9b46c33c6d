# TAR1090 Aircraft Type Detection and Icon Selection

## Overview

TAR1090 determines aircraft types and selects appropriate icons through a sophisticated multi-layered approach that combines:

1. **ADS-B Category Codes** (broadcasted by aircraft)
2. **ICAO Aircraft Type Designators** (from external databases)
3. **ICAO Type Description Codes** (from external databases)
4. **Wake Turbulence Categories** (from external databases)
5. **Registration-based lookups** (from external databases)

## Key Components

### 1. Data Sources

#### ADS-B Transmitted Data
- **Category**: Broadcasted by aircraft (e.g., "A3", "B1", "C2")
- **ICAO Hex**: Aircraft identifier (e.g., "A12345")
- **Flight ID**: Callsign (e.g., "AAL1429")

#### External Database Lookups
- **ICAO Type Designator**: 4-character code (e.g., "A320", "B738", "C172")
- **Type Description**: 3-character code (e.g., "L2J" = Land plane, 2 engines, Jet)
- **Wake Turbulence Category**: Single character (e.g., "M" = Medium, "H" = Heavy)
- **Registration**: Aircraft registration number

### 2. Core Files and Functions

#### `markers.js` - Icon Definitions and Selection Logic
```javascript
// Location: /html/markers.js
// Key function: getBaseMarker(category, typeDesignator, typeDescription, wtc, addrtype, altitude, eastbound)
```

**Aircraft Shape Definitions** (lines 4-714):
- 50+ detailed SVG aircraft shapes
- Examples: 'airliner', 'cessna', 'helicopter', 'hi_perf', 'heavy_2e', etc.
- Each shape has properties: id, width, height, viewBox, strokeScale, path

**Type Designator Mappings** (lines 727-1162):
```javascript
let TypeDesignatorIcons = {
    'A320': ['airliner', 0.94],     // Airbus A320 -> airliner icon, 94% scale
    'B738': ['airliner', 0.96],     // Boeing 737-800 -> airliner icon, 96% scale
    'C172': ['cessna', 1],          // Cessna 172 -> cessna icon, 100% scale
    'F16': ['hi_perf', 1],          // F-16 -> high performance icon
    'R44': ['helicopter', 1],       // Robinson R44 -> helicopter icon
    // ... 400+ more mappings
};
```

**Type Description Mappings** (lines 1164-1203):
```javascript
let TypeDescriptionIcons = {
    'H': ['helicopter', 1],         // H = Helicopter
    'L1P': ['cessna', 1],          // Land plane, 1 engine, Piston
    'L2J': ['airliner', 0.96],     // Land plane, 2 engines, Jet
    'L2T': ['twin_large', 0.92],   // Land plane, 2 engines, Turboprop
    // ... more mappings
};
```

**Category Mappings** (lines 1205-1235):
```javascript
let CategoryIcons = {
    "A1": ['cessna', 1],           // Light aircraft < 7t
    "A2": ['jet_swept', 0.94],     // Medium aircraft < 34t
    "A3": ['airliner', 0.96],      // Large aircraft < 136t
    "A5": ['heavy_2e', 0.92],      // Heavy aircraft > 136t
    "A7": ['helicopter', 1],       // Helicopter
    "B6": ['uav', 1],              // UAV
    "C0": ['ground_unknown', 1],   // Ground vehicle
    // ... more mappings
};
```

#### `planeObject.js` - Aircraft Object Management
```javascript
// Location: /html/planeObject.js
// Key functions: updateMarker(), setTypeData(), checkForDB()
```

**Icon Selection Logic** (lines 1723-1737):
```javascript
if (!this.shape || this.baseMarkerKey != baseMarkerKey) {
    this.baseMarkerKey = baseMarkerKey;
    let baseMarker = null;
    try {
        baseMarker = getBaseMarker(this.category, icaoType, this.typeDescription, 
                                 this.wtc, this.addrtype, this.altitude, eastbound);
    } catch (error) {
        console.error(error);
    }
    if (!baseMarker) {
        baseMarker = ['pumpkin', 1];  // Fallback icon
    }
    this.shape = shapes[baseMarker[0]];
    this.baseScale = baseMarker[1] * 0.96;
}
```

#### `dbloader.js` - Database Loading
```javascript
// Location: /html/dbloader.js
// Key functions: dbLoad(), lookupIcaoAircraftType()
```

**Type Cache Loading** (script.js lines 473-480):
```javascript
function db_load_type_cache() {
    return jQuery.getJSON(databaseFolder + "/icao_aircraft_types2.js").done(function(typeLookupData) {
        g.type_cache = typeLookupData;
        for (let i in g.planesOrdered) {
            g.planesOrdered[i].setTypeData();
        }
    });
}
```

### 3. Icon Selection Algorithm

The `getBaseMarker()` function follows this priority order:

1. **Special Cases**:
   - AIS vessels -> 'ground_square'
   - Halloween mode -> 'pumpkin' or 'witch'
   - ATC style -> 'asterisk'

2. **Type Designator** (Highest Priority):
   ```javascript
   if (typeDesignator in TypeDesignatorIcons) {
       return TypeDesignatorIcons[typeDesignator];
   }
   ```

3. **Type Description + Wake Turbulence**:
   ```javascript
   if (typeDescription && wtc) {
       let typeDescriptionWithWtc = typeDescription + "-" + wtc;
       if (typeDescriptionWithWtc in TypeDescriptionIcons) {
           return TypeDescriptionIcons[typeDescriptionWithWtc];
       }
   }
   ```

4. **Type Description Only**:
   ```javascript
   if (typeDescription in TypeDescriptionIcons) {
       return TypeDescriptionIcons[typeDescription];
   }
   ```

5. **Basic Type Letter**:
   ```javascript
   let basicType = typeDescription.charAt(0);  // e.g., "H" for helicopter
   if (basicType in TypeDescriptionIcons) {
       return TypeDescriptionIcons[basicType];
   }
   ```

6. **Category** (Fallback):
   ```javascript
   if (category in CategoryIcons) {
       return CategoryIcons[category];
   }
   ```

7. **Final Fallback**:
   - Ground vehicles: 'ground_square'
   - Unknown: 'unknown' or 'pumpkin'

### 4. Data Flow

1. **Aircraft Data Reception**:
   - ADS-B message contains: hex, category, lat, lon, altitude, etc.
   - Example: `{"hex": "ad6884", "category": "A3", "lat": 27.358936, ...}`

2. **Database Lookup**:
   - ICAO hex -> registration, type designator, type description, WTC
   - Example: "ad6884" -> registration: "N123AA", type: "A320", desc: "L2J", wtc: "M"

3. **Icon Selection**:
   - `updateMarker()` calls `getBaseMarker()` with all available data
   - Returns shape name and scale factor
   - Example: `["airliner", 0.94]`

4. **Icon Rendering**:
   - SVG shape rendered with appropriate color, rotation, scale
   - Color based on altitude, selection state, data source

### 5. Database Structure

The aircraft database (`icao_aircraft_types2.js`) contains mappings like:
```javascript
{
    "A320": ["Airbus A320", "L2J", "M"],  // [Long name, Type description, WTC]
    "B738": ["Boeing 737-800", "L2J", "M"],
    "C172": ["Cessna 172", "L1P", "L"],
    // ... thousands more entries
}
```

### 6. Key Insights

1. **No Direct Type Broadcasting**: Aircraft don't broadcast their specific type (A320, B737, etc.) via ADS-B
2. **Database Dependency**: Type determination requires external database lookups by ICAO hex
3. **Fallback Strategy**: Multiple fallback levels ensure every aircraft gets an appropriate icon
4. **Category Reliability**: ADS-B category is the most reliable data as it's directly transmitted
5. **Registration Lookup**: Some aircraft types are inferred from registration patterns
6. **Dynamic Updates**: Icons can change as more data becomes available (database lookups complete)

## Implementation Notes

- Database files are loaded asynchronously after initial aircraft data
- Icons may start as generic and become more specific as database lookups complete
- The system handles missing data gracefully with sensible fallbacks
- Special handling for military aircraft, ground vehicles, and unusual aircraft types
- Performance optimized with caching and batched database requests

## Detailed Code Examples

### Example 1: Complete Aircraft Processing Flow

```javascript
// 1. Initial ADS-B data (script.js)
let aircraftData = {
    "hex": "ad6884",
    "type": "adsb_icao",
    "flight": "AAL1429",
    "category": "A3",
    "lat": 27.358936,
    "lon": -82.302387,
    "alt_baro": 36000
};

// 2. Database lookup (dbloader.js:39-50)
function dbLoad(icao) {
    let defer = Promise.unwrapped();
    if (icao[0] == '~') {
        defer.resolve(null);
        return defer;
    }
    icao = icao.toUpperCase();
    request_from_db(icao, 1, defer);
    return defer;
}

// 3. Type data setting (planeObject.js:2759-2782)
PlaneObject.prototype.setTypeData = function() {
    if (g.type_cache == null || !this.icaoType || this.icaoType == this.icaoTypeCache)
        return;

    let typeCode = this.icaoType.toUpperCase();
    if (!(typeCode in g.type_cache))
        return;

    let typeData = g.type_cache[typeCode];
    const typeLong = typeData[0];      // "Airbus A320"
    const desc = typeData[1];          // "L2J"
    const wtc = typeData[2];           // "M"

    if (desc != null)
        this.typeDescription = `${desc}`;
    if (wtc != null)
        this.wtc = `${wtc}`;
    if (this.typeLong == null && typeLong != null)
        this.typeLong = `${typeLong}`;
};

// 4. Icon selection (markers.js:1237-1318)
function getBaseMarker(category, typeDesignator, typeDescription, wtc, addrtype, altitude, eastbound) {
    // Priority 1: Type designator
    if (typeDesignator in TypeDesignatorIcons) {
        return TypeDesignatorIcons[typeDesignator];  // ["airliner", 0.94]
    }

    // Priority 2: Type description + WTC
    if (typeDescription != null && wtc !== null) {
        let typeDescriptionWithWtc = typeDescription + "-" + wtc;  // "L2J-M"
        if (typeDescriptionWithWtc in TypeDescriptionIcons) {
            return TypeDescriptionIcons[typeDescriptionWithWtc];
        }
    }

    // Priority 3: Category fallback
    if (category in CategoryIcons) {
        return CategoryIcons[category];  // ["airliner", 0.96] for "A3"
    }

    return ['unknown', 1];
}
```

### Example 2: Icon Rendering Process

```javascript
// Icon update (planeObject.js:831-1011)
PlaneObject.prototype.updateIcon = function() {
    let fillColor = hslToRgb(this.getMarkerColor());
    let svgKey = fillColor + '!' + this.shape.name + '!' + this.strokeWidth;

    if (iconCache[svgKey] == undefined) {
        // Create SVG icon
        let svgURI = svgShapeToURI(this.shape, fillColor, OutlineADSBColor, this.strokeWidth);

        this.markerIcon = new ol.style.Icon({
            scale: this.scale,
            imgSize: [this.shape.w, this.shape.h],
            src: svgURI,
            rotation: (this.shape.noRotate ? 0 : this.rotation * Math.PI / 180.0),
            rotateWithView: (this.shape.noRotate ? false : true),
        });
    }
};
```

### Example 3: Database File Structure

The `icao_aircraft_types2.js` file contains entries like:
```javascript
{
    "A320": ["Airbus A320-200", "L2J", "M"],
    "A321": ["Airbus A321-200", "L2J", "M"],
    "B738": ["Boeing 737-800", "L2J", "M"],
    "B77W": ["Boeing 777-300ER", "L2J", "H"],
    "C172": ["Cessna 172", "L1P", "L"],
    "PC12": ["Pilatus PC-12", "L1T", "L"],
    "H60": ["Sikorsky UH-60 Black Hawk", "H1T", "M"],
    "F16": ["General Dynamics F-16 Fighting Falcon", "L1J", "M"]
}
```

Where each entry is: `[Full Name, Type Description, Wake Turbulence Category]`

## Critical Files for Implementation

1. **`/html/markers.js`** (1477 lines)
   - Lines 4-714: Aircraft shape definitions
   - Lines 727-1162: Type designator mappings
   - Lines 1164-1203: Type description mappings
   - Lines 1205-1235: Category mappings
   - Lines 1237-1318: `getBaseMarker()` function

2. **`/html/planeObject.js`** (3016 lines)
   - Lines 1698-1740: `updateMarker()` function
   - Lines 2759-2782: `setTypeData()` function
   - Lines 2800-2820: `checkForDB()` function

3. **`/html/dbloader.js`** (157 lines)
   - Lines 39-50: `dbLoad()` function
   - Lines 85-100: `lookupIcaoAircraftType()` function
   - Lines 119-156: Database request handling

4. **`/html/script.js`** (9071 lines)
   - Lines 473-480: Type cache loading
   - Lines 5541-5544: Type data integration

## Performance Considerations

- Database lookups are batched and cached
- Icons are cached by SVG key to avoid regeneration
- WebGL rendering for high aircraft counts
- Fallback icons prevent rendering delays
- Asynchronous loading prevents UI blocking

## Database Sources and Implementation Guide

### Database Repository Structure

TAR1090 uses the **wiedehopf/tar1090-db** repository (https://github.com/wiedehopf/tar1090-db) which contains:

1. **Primary Database Source**: Mictronics Aircraft Database (https://www.mictronics.de/aircraft-database/)
   - Maintained by Mictronics (https://github.com/Mictronics/readsb)
   - Contains civil and military aircraft data
   - Automatically and manually maintained to reduce duplicates

2. **Database Files Structure**:
   ```
   tar1090-db/
   ├── db/                          # Main database folder (created during build)
   │   ├── icao_aircraft_types2.js  # Aircraft type mappings
   │   ├── regIcao.js               # Registration to ICAO mappings
   │   ├── airport-coords.js        # Airport coordinates
   │   └── [hex].js                 # Individual aircraft data files (A.js, B.js, etc.)
   ├── icao_aircraft_types.json    # Source type data
   ├── ranges.json                 # ICAO hex ranges by country
   └── airport-coords.json         # Airport coordinate data
   ```

### Complete Aircraft Data Fields Available

When you lookup an aircraft by ICAO hex, you can obtain the following data:

#### From Database Lookup (format: `[r, t, f, desc, ownOp, year]`):
- **`data[0]` (r)**: Registration (tail number) - e.g., "N123AA", "G-ABCD"
- **`data[1]` (t)**: ICAO Type Designator - e.g., "A320", "B738", "C172"
- **`data[2]` (f)**: Flags (4-character string):
  - `f[0]`: Military (1=military, 0=civilian)
  - `f[1]`: Interesting (1=interesting aircraft, 0=normal)
  - `f[2]`: PIA - Privacy ICAO Address (1=blocked registration, 0=normal)
  - `f[3]`: LADD - Limiting Aircraft Data Displayed (1=limited data, 0=normal)
- **`data[3]` (desc)**: Long description/model name - e.g., "Airbus A320-200"
- **`data[4]` (ownOp)**: Owner/Operator - e.g., "American Airlines", "United Airlines"
- **`data[5]` (year)**: Year built/manufactured - e.g., "2015", "1998"

#### From Type Cache Lookup (`icao_aircraft_types2.js`):
- **Type Long Name**: Full aircraft name - e.g., "Boeing 737-800"
- **Type Description**: 3-character code - e.g., "L2J" (Land plane, 2 engines, Jet)
- **Wake Turbulence Category**: Single character - e.g., "M" (Medium), "H" (Heavy), "L" (Light)

#### Additional Computed Data:
- **Country**: Derived from ICAO hex ranges
- **Registration Pattern**: Computed from ICAO hex using registration algorithms
- **Military Status**: Determined from ICAO hex ranges and database flags

### How to Implement in Your Own Webapp

#### Option 1: Use TAR1090 Database Directly

1. **Download Database Files**:
   ```bash
   # Clone the database repository
   git clone https://github.com/wiedehopf/tar1090-db.git

   # The database is in the 'db' folder after build
   # Key files you need:
   # - db/icao_aircraft_types2.js (type mappings)
   # - db/[A-F].js (aircraft data by hex prefix)
   # - db/regIcao.js (registration mappings)
   ```

2. **Database Lookup Implementation**:
   ```javascript
   // Load type cache
   const typeCache = await fetch('/db/icao_aircraft_types2.js').then(r => r.json());

   // Lookup aircraft data by ICAO hex
   async function lookupAircraft(icaoHex) {
       const prefix = icaoHex.substring(0, 1).toUpperCase();
       const dbFile = await fetch(`/db/${prefix}.js`).then(r => r.json());

       // Navigate through the hierarchical structure
       let current = dbFile;
       for (let i = 1; i < icaoHex.length; i++) {
           const key = icaoHex.substring(0, i + 1);
           if (current.children && current.children.includes(key)) {
               const nextFile = await fetch(`/db/${key}.js`).then(r => r.json());
               current = nextFile;
           } else if (current[icaoHex.substring(i)]) {
               return current[icaoHex.substring(i)];
           }
       }
       return null;
   }

   // Get complete aircraft info
   async function getAircraftInfo(icaoHex) {
       const dbData = await lookupAircraft(icaoHex);
       if (!dbData) return null;

       const [registration, icaoType, flags, description, operator, year] = dbData;

       // Get type information
       let typeInfo = null;
       if (icaoType && typeCache[icaoType]) {
           const [typeLong, typeDescription, wtc] = typeCache[icaoType];
           typeInfo = { typeLong, typeDescription, wtc };
       }

       return {
           registration,
           icaoType,
           military: flags?.[0] === '1',
           interesting: flags?.[1] === '1',
           pia: flags?.[2] === '1',
           ladd: flags?.[3] === '1',
           description,
           operator,
           year,
           ...typeInfo
       };
   }
   ```

#### Option 2: Use Mictronics Database API

1. **Direct Database Access**:
   - The Mictronics database (https://www.mictronics.de/aircraft-database/) appears to be the source
   - You could potentially scrape or request access to their data
   - They offer export functionality for various formats

2. **Alternative Data Sources**:
   - **OpenSky Network**: Has aircraft database API
   - **FlightAware**: Commercial API with aircraft data
   - **ADS-B Exchange**: Community database
   - **FAA Registry**: US aircraft registration data
   - **EASA**: European aircraft registration data

#### Option 3: Build Your Own Database

1. **Data Sources to Combine**:
   ```javascript
   // Example data structure you'd want to build
   const aircraftDatabase = {
       "A12345": {
           registration: "N123AA",
           icaoType: "A320",
           typeLong: "Airbus A320-200",
           typeDescription: "L2J",
           wtc: "M",
           operator: "American Airlines",
           year: "2015",
           military: false,
           country: "United States"
       }
   };
   ```

2. **Registration Pattern Algorithms**:
   - TAR1090 includes `registrations.js` with algorithms to compute likely registration from ICAO hex
   - Different countries have different patterns (N-numbers for US, G- for UK, etc.)

### Database Update Process

The TAR1090 database is updated via:

1. **Automated Updates**: The `update.sh` script pulls from Mictronics database
2. **Manual Curation**: False entries and duplicates are manually removed
3. **Version Control**: Database versions are tracked and cached
4. **Incremental Loading**: Only changed data is downloaded

### Key Implementation Considerations

1. **Caching Strategy**: Database files are large, implement proper caching
2. **Fallback Handling**: Not all aircraft will be in the database
3. **Privacy Considerations**: Some aircraft have PIA (Privacy ICAO Address) flags
4. **Performance**: Batch requests and cache results
5. **Updates**: Plan for regular database updates
6. **Legal**: Ensure compliance with data usage terms

### Example Integration for Your Project

```javascript
// For your skytraces API, you could add aircraft type lookup:
app.post('/aircraft/positions_with_types', async (req, res) => {
    const positions = await getAircraftPositions(req.body);

    // Enhance with aircraft type data
    const enhancedPositions = await Promise.all(
        positions.map(async (pos) => {
            const aircraftInfo = await getAircraftInfo(pos.hex);
            return {
                ...pos,
                registration: aircraftInfo?.registration,
                icaoType: aircraftInfo?.icaoType,
                operator: aircraftInfo?.operator,
                military: aircraftInfo?.military
            };
        })
    );

    res.json(enhancedPositions);
});
```

## Database File Structure and Naming Convention Analysis

### Hierarchical File Organization

The TAR1090 database uses a **hierarchical file structure** based on ICAO hex prefixes to optimize lookup performance:

#### File Naming Pattern
The database files follow a **progressive hex prefix** naming convention:

1. **Single Character Files** (Root Level):
   - `0.json`, `1.json`, `2.json`, ..., `9.json`, `A.json`, `B.json`, `C.json`, `D.json`, `E.json`, `F.json`
   - These contain aircraft with ICAO hex codes starting with that character

2. **Two Character Files** (Second Level):
   - `00.json`, `04.json`, `06.json`, `0A.json`, `0D.json`
   - `30.json`, `34.json`, `38.json`, `39.json`
   - `A0.json`, `A1.json`, `A2.json`, ..., `AB.json`, `AC.json`, `AD.json`, `AE.json`
   - `C0.json`, `C8.json`

3. **Three Character Files** (Third Level):
   - `383.json`, `390.json`, `391.json`, `392.json`, `393.json`, `394.json`, `398.json`
   - `3C.json`, `3D.json`, `3E.json`, `3F.json`
   - `A00.json`, `A01.json`, `A02.json`, ..., `AAA.json`, `AAB.json`, `AAD.json`
   - `C00.json`, `C01.json`, `C02.json`, ..., `C09.json`

4. **Four+ Character Files** (Deeper Levels):
   - `3D0.json`, `3D1.json`, `3D2.json`, `3D3.json`
   - `3E6.json`, `3E7.json`, `3FF.json`
   - `400.json`, `401.json`, `404.json`, `405.json`, `406.json`, `407.json`
   - `C001.json`, `C002.json`, `C006.json`, `C00C.json`

### Database Entry Structure

Each aircraft entry follows this exact format:
```javascript
{
    "ICAO_HEX": {
        "r": "REGISTRATION",     // Tail number (e.g., "N123AA", "D-ABCD")
        "t": "TYPE_DESIGNATOR",  // ICAO type code (e.g., "A320", "B738", "C172")
        "f": "FLAGS",            // 2-character flag string
        "desc": "DESCRIPTION"    // Long aircraft description (optional)
    }
}
```

#### Flag Field Breakdown (`f` field)
The flags field is a 2-character string where each character represents:
- **Position 0**: Military status (`0` = civilian, `1` = military)
- **Position 1**: Special flags (`0` = normal, `1` = interesting/special aircraft)

Examples:
- `"00"` = Civilian, normal aircraft
- `"10"` = Military aircraft
- `"01"` = Civilian, interesting aircraft (VIP, special livery, etc.)

### Lookup Algorithm

The hierarchical structure enables efficient lookups:

1. **Start with ICAO hex** (e.g., "A12345")
2. **Check single character file** (`A.json`)
3. **Look for "children" array** indicating further subdivision
4. **Navigate progressively** through `A1.json` → `A12.json` → `A123.json` etc.
5. **Find exact match** in the deepest applicable file

### Example Database Entries

#### German Aircraft (3D prefix):
```javascript
{
    "C026": {"r": "D-GTAC", "t": "PA34", "f": "00", "desc": "Piper PA-34-220T"},
    "C0A8": {"r": "D-GTFC", "t": "PA44", "f": "00", "desc": "Piper PA-44-180"},
    "C100": {"r": "D-GTIM", "t": "P06T", "f": "00", "desc": "Tecnam P.2006-T"}
}
```

#### US Military Aircraft (A prefix):
```javascript
{
    "F04FC": {"r": "22-03495", "t": "H64", "f": "10", "desc": ""},
    "F08A2": {"r": "22-03496", "t": "H64", "f": "10", "desc": ""},
    "F351F": {"r": "DEFAULT", "t": "F35", "f": "10", "desc": ""}
}
```

#### African Aircraft (0 prefix):
```javascript
{
    "7001C": {"r": "6V-HAW", "t": "A139", "f": "00", "desc": ""},
    "700B6": {"r": "6V-AFW", "t": "AT43", "f": "00", "desc": "Atr ATR 42-312"},
    "70180": {"r": "6V-AMA", "t": "A319", "f": "00", "desc": ""}
}
```

### Children Array Structure

Files with subdivisions include a "children" array:
```javascript
{
    // ... aircraft entries ...
    "children": ["AE", "AD", "A8", "A7", "A5", "AB", "AA", "A4", "AC", "A6", "A2", "A9", "A3", "A1", "A0"]
}
```

This indicates which prefixes have their own dedicated files.

### Performance Optimization

The hierarchical structure provides several benefits:

1. **Reduced File Sizes**: Each file contains only relevant aircraft
2. **Faster Lookups**: No need to search through millions of entries
3. **Efficient Caching**: Can cache frequently accessed prefix files
4. **Scalable**: Easy to add new aircraft without reorganizing entire database
5. **Network Efficient**: Only download needed file segments

### Implementation Strategy

For your project, you can implement this as:

```javascript
async function lookupAircraftHierarchical(icaoHex) {
    let currentPrefix = '';
    let currentData = null;

    // Start with single character
    for (let i = 1; i <= icaoHex.length; i++) {
        const prefix = icaoHex.substring(0, i);

        try {
            const response = await fetch(`/database/${prefix}.json`);
            if (response.ok) {
                currentData = await response.json();
                currentPrefix = prefix;

                // Check if we found the exact aircraft
                const remainingHex = icaoHex.substring(i);
                if (remainingHex in currentData) {
                    return currentData[remainingHex];
                }

                // Check if there are children to explore
                if (!currentData.children || !currentData.children.includes(icaoHex.substring(0, i + 1))) {
                    break;
                }
            } else {
                break;
            }
        } catch (error) {
            break;
        }
    }

    // Fallback: search in current data
    const remainingHex = icaoHex.substring(currentPrefix.length);
    return currentData?.[remainingHex] || null;
}
```

This structure makes the TAR1090 database extremely efficient for real-time aircraft lookups while maintaining reasonable file sizes and network performance.
