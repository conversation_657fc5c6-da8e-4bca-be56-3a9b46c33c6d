# SkyTraces Landing

A Next.js web application for viewing real-time airport transitions (takeoffs and landings) from the SkyTraces API.

## Features

- **Dynamic Airport Pages**: Visit `/[ICAO]` to view transitions for any airport (e.g., `/KJFK`, `/EGLL`)
- **Real-time Updates**: Automatically refreshes transition data every 5 seconds
- **14-Day History**: Shows takeoffs and landings from the last 14 days
- **Airport Information**: Displays basic airport details including runways
- **Timezone Support**: All times displayed in airport local time (GMT-4)
- **Date Grouping**: Transitions organized by date with "Today", "Yesterday", and date headers
- **Top Bar Layout**: Clean header showing airport name, identifier, and current time
- **Responsive Design**: Works on desktop and mobile devices

## Getting Started

### Prerequisites

- Node.js 18+
- The SkyTraces API server running on `http://localhost:8000`

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.local.example .env.local
# Edit .env.local to set NEXT_PUBLIC_API_URL if different from default
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Usage

1. **Home Page**: Enter a 4-letter ICAO airport code or click on one of the popular airports
2. **Airport Page**: View real-time transitions for the selected airport
   - **Top Bar**: Shows airport name, identifier, and current local time (GMT-4)
   - **Airport Details**: Displays elevation, runway information, and update status
   - **Grouped Transitions**: Organized by date with headers like "Today", "Yesterday", "Wed 25 Jun"
   - **Time Display**: All transition times shown in 24-hour format (HH:MM) in airport timezone
   - **Auto-refresh**: Updates every 5 seconds
   - **Visual Design**: Color-coded badges for takeoffs (green) and landings (blue)
   - **Simplified Data**: Shows time, type, flight, runway, and aircraft hex (altitude/speed removed)

### API Integration

The app connects to the SkyTraces API endpoints:
- `GET /api/v1/airports/{icao}` - Airport information
- `GET /api/v1/airports/{icao}/transitions` - Airport transitions

### Environment Variables

- `NEXT_PUBLIC_API_URL` - Base URL for the SkyTraces API (default: `http://localhost:8000`)

## Development

### Project Structure

```
src/
├── app/
│   ├── [icao]/          # Dynamic airport pages
│   │   └── page.tsx     # Airport transitions page
│   ├── layout.tsx       # Root layout
│   └── page.tsx         # Home page
├── lib/
│   ├── api.ts           # API client functions
│   └── utils.ts         # Utility functions and constants
└── types/
    └── api.ts           # TypeScript types for API responses
```

### Key Components

- **Airport Page** (`/[icao]/page.tsx`): Main page showing airport transitions
- **API Client** (`lib/api.ts`): Functions for fetching data from the SkyTraces API
- **Types** (`types/api.ts`): TypeScript interfaces for API responses

## Building for Production

```bash
npm run build
npm start
```

### Docker Deployment

The Dockerfile is configured for production deployment with the API URL set to `https://api.skytraces.com`.

To build and run with Docker:
```bash
docker build -t landing .
docker run -p 3000:3000 landing
```

For custom API URL, override the environment variable:
```bash
docker run -p 3000:3000 -e NEXT_PUBLIC_API_URL=https://your-api-domain.com landing
```

## Technologies Used

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **React Hooks** - State management and effects
