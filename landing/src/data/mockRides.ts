import { RideOption, RideCategory } from "@/types/uber"

export const mockRides: RideOption[] = [
  // Recommended
  {
    id: "1",
    name: "<PERSON>",
    description: "Luxury rides with professional drivers",
    image: "🚗",
    capacity: 4,
    price: "US$27.12",
    originalPrice: "US$38.75",
    eta: "4 mins away",
    arrivalTime: "13:39",
    isSelected: true,
    category: "recommended"
  },
  {
    id: "8",
    name: "UberX",
    description: "Affordable rides for everyday",
    image: "🚙",
    capacity: 4,
    price: "US$30.07",
    originalPrice: "US$42.96",
    eta: "5 mins away",
    arrivalTime: "13:38",
    features: ["Faster"],
    category: "recommended"
  },
  {
    id: "942",
    name: "UberXL",
    description: "Affordable rides for groups up to 6",
    image: "🚐",
    capacity: 6,
    price: "US$31.46",
    originalPrice: "US$44.95",
    eta: "4 mins away",
    arrivalTime: "13:38",
    category: "recommended"
  },
  {
    id: "20064151",
    name: "UberXXL",
    description: "Rides for 6 with room for extra luggage",
    image: "🚐",
    capacity: 6,
    price: "US$36.36",
    originalPrice: "US$51.36",
    eta: "4 mins away",
    arrivalTime: "13:39",
    category: "recommended"
  },
  
  // Popular
  {
    id: "20041963",
    name: "Comfort Electric",
    description: "Newer electric vehicles with extra legroom",
    image: "⚡",
    capacity: 4,
    price: "US$31.52",
    originalPrice: "US$45.03",
    eta: "5 mins away",
    arrivalTime: "13:39",
    category: "popular"
  },
  {
    id: "20017885",
    name: "Comfort",
    description: "Newer cars with extra legroom",
    image: "🚗",
    capacity: 4,
    price: "US$31.52",
    originalPrice: "US$45.03",
    eta: "4 mins away",
    arrivalTime: "13:38",
    category: "popular"
  },
  
  // Economy
  {
    id: "20030601",
    name: "Green",
    description: "Affordable rides in electric vehicles",
    image: "🌱",
    capacity: 4,
    price: "US$30.07",
    originalPrice: "US$42.96",
    eta: "5 mins away",
    arrivalTime: "13:39",
    category: "economy"
  },
  {
    id: "20040031",
    name: "Uber Pet",
    description: "For you and your pet",
    image: "🐕",
    capacity: 4,
    price: "US$33.18",
    originalPrice: "US$47.40",
    eta: "5 mins away",
    arrivalTime: "13:39",
    category: "economy"
  },
  
  // Premium
  {
    id: "2",
    name: "Black SUV",
    description: "Luxury rides for 6 with professional drivers",
    image: "🚙",
    capacity: 6,
    price: "US$35.17",
    originalPrice: "US$50.17",
    eta: "4 mins away",
    arrivalTime: "13:39",
    category: "premium"
  },
  
  // More
  {
    id: "4651",
    name: "Assist",
    description: "Special assistance from certified drivers",
    image: "♿",
    capacity: 4,
    price: "US$30.07",
    originalPrice: "US$42.96",
    eta: "6 mins away",
    arrivalTime: "13:42",
    category: "more"
  },
  {
    id: "20066179",
    name: "Car Seat",
    description: "For children 5 - 65 lbs",
    image: "👶",
    capacity: 6,
    price: "Select time",
    eta: "5 mins away",
    arrivalTime: "13:41",
    category: "more"
  }
]

export const rideCategories: RideCategory[] = [
  {
    title: "Rides we think you'll like",
    rides: mockRides.filter(ride => ride.category === "recommended")
  },
  {
    title: "Popular",
    rides: mockRides.filter(ride => ride.category === "popular")
  },
  {
    title: "Economy",
    rides: mockRides.filter(ride => ride.category === "economy")
  },
  {
    title: "Premium",
    rides: mockRides.filter(ride => ride.category === "premium")
  },
  {
    title: "More",
    rides: mockRides.filter(ride => ride.category === "more")
  }
]
