import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Time utilities for playback functionality
export const timeUtils = {
  // Convert time string to seconds from start of day
  timeToSeconds: (timeString: string): number => {
    const date = new Date(timeString);
    return date.getHours() * 3600 + date.getMinutes() * 60 + date.getSeconds();
  },

  // Convert seconds to HH:MM:SS format
  secondsToTime: (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  },

  // Convert seconds to HH:MM format (for display)
  secondsToTimeShort: (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  },

  // Check if time is within a recording
  isTimeInRecording: (time: number, recording: { start_time: string; duration_ms: number }): boolean => {
    const startTime = timeUtils.timeToSeconds(recording.start_time);
    const endTime = startTime + (recording.duration_ms / 1000);
    return time >= startTime && time < endTime;
  },

  // Get offset within a recording
  getOffsetInRecording: (time: number, recording: { start_time: string }): number => {
    const startTime = timeUtils.timeToSeconds(recording.start_time);
    return Math.max(0, time - startTime);
  },

  // Handle date rollover
  handleDateRollover: (time: number, currentDate: Date): { newTime: number; newDate: Date } => {
    if (time >= 86400) {
      // Time went past 24:00 - move to next day
      const nextDay = new Date(currentDate);
      nextDay.setDate(nextDay.getDate() + 1);
      return { newTime: time - 86400, newDate: nextDay };
    } else if (time < 0) {
      // Time went before 00:00 - move to previous day
      const prevDay = new Date(currentDate);
      prevDay.setDate(prevDay.getDate() - 1);
      return { newTime: time + 86400, newDate: prevDay };
    }
    return { newTime: time, newDate: currentDate };
  },

  // Clamp time to valid range (0-86399 seconds)
  clampTime: (time: number): number => {
    return Math.max(0, Math.min(86399, time));
  }
};

// API configuration
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://api.skytraces.com'

// Full API URL for download links (includes protocol and domain)
export const API_DOWNLOAD_BASE_URL = API_BASE_URL

// API endpoints
export const API_ENDPOINTS = {
  AIRPORT_BY_ICAO: (icao: string) => `/api/v1/airports/${icao}`,
  AIRPORT_TRANSITIONS: (icao: string) => `/api/v1/airports/${icao}/transitions`,
  AIRPORT_TRANSITIONS_EXTENDED: (icao: string) => `/api/v1/airports/${icao}/transitionsextended`,
  AIRCRAFT_POSITIONS_BBOX: '/api/v1/aircraft/pos_at_time_bbox',
  AIRCRAFT_TRACE: (hex: string) => `/api/v1/aircraft/${hex}/trace`,
  RECORDINGS_LIST: (station: string, date: string) => `/api/v1/recordings/${station}/${date}`,
} as const

// Timezone utilities (assuming GMT-4 for all airports for now)
// GMT-4 means 4 hours behind UTC, so we subtract 4 hours from UTC
const AIRPORT_TIMEZONE_OFFSET_HOURS = -4;

export function convertToAirportTime(utcTimeString: string): Date {
  // Parse the UTC timestamp from the database
  const utcDate = new Date(utcTimeString);

  // Convert UTC to GMT-4 by subtracting 4 hours (adding negative offset)
  const offsetMs = AIRPORT_TIMEZONE_OFFSET_HOURS * 60 * 60 * 1000;
  const airportTime = new Date(utcDate.getTime() + offsetMs);

  return airportTime;
}

export function formatTimeOnly(airportTime: Date | string): string {
  // Convert to Date object if it's a string
  const date = typeof airportTime === 'string' ? new Date(airportTime) : airportTime;

  // Validate that we have a valid Date object
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    return '--:--';
  }

  // Format time in HH:MM format using the airport time directly
  const hours = date.getUTCHours().toString().padStart(2, '0');
  const minutes = date.getUTCMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
}

export function getAirportDateKey(airportTime: Date): string {
  // Extract date components using UTC methods since airportTime is already adjusted to airport timezone
  const year = airportTime.getUTCFullYear();
  const month = (airportTime.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = airportTime.getUTCDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

export function dateKeyToAirportTime(dateKey: string): Date {
  // Convert YYYY-MM-DD string back to a Date object in airport timezone
  // We create a Date object that represents the date at midnight in airport timezone
  const [year, month, day] = dateKey.split('-').map(Number);
  // Create a UTC date and then adjust it to represent the airport timezone
  const utcDate = new Date(Date.UTC(year, month - 1, day, 0, 0, 0));
  // Since we want this to represent midnight in airport timezone, we need to adjust
  // by the opposite of our timezone offset
  return new Date(utcDate.getTime() - (AIRPORT_TIMEZONE_OFFSET_HOURS * 60 * 60 * 1000));
}

export function formatDateHeader(airportTime: Date): string {
  // Get current time in airport timezone for comparison
  const nowAirport = getCurrentAirportTime();

  // Create yesterday in airport timezone
  const yesterdayAirport = new Date(nowAirport.getTime() - (24 * 60 * 60 * 1000));

  // Compare dates using the new getAirportDateKey function
  const transitionDateStr = getAirportDateKey(airportTime);
  const todayDateStr = getAirportDateKey(nowAirport);
  const yesterdayDateStr = getAirportDateKey(yesterdayAirport);

  if (transitionDateStr === todayDateStr) {
    return 'Today';
  } else if (transitionDateStr === yesterdayDateStr) {
    return 'Yesterday';
  } else {
    // Format the date using UTC methods since airportTime is already in airport timezone
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    const weekday = weekdays[airportTime.getUTCDay()];
    const day = airportTime.getUTCDate();
    const month = months[airportTime.getUTCMonth()];

    return `${weekday} ${day} ${month}`;
  }
}

export function getCurrentAirportTime(): Date {
  // Get current UTC time and convert to airport timezone
  const nowUtc = new Date();
  return new Date(nowUtc.getTime() + (AIRPORT_TIMEZONE_OFFSET_HOURS * 60 * 60 * 1000));
}

export function countTodaysTransitions(transitions: { time: string }[]): number {
  const todayDateKey = getAirportDateKey(getCurrentAirportTime());

  return transitions.filter(transition => {
    const airportTime = convertToAirportTime(transition.time);
    const transitionDateKey = getAirportDateKey(airportTime);
    return transitionDateKey === todayDateKey;
  }).length;
}


