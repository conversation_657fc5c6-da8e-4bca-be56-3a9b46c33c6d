import { cellToLatLng } from 'h3-js';

/**
 * Converts H3 index to latitude and longitude coordinates
 * @param h3Index - The H3 cell index
 * @returns [lat, lng] coordinates or null if invalid
 */
export function h3ToLatLng(h3Index: string): [number, number] | null {
  if (!h3Index) return null;
  
  try {
    const [lat, lng] = cellToLatLng(h3Index);
    return [lat, lng];
  } catch (error) {
    console.warn(`Failed to convert H3 index ${h3Index} to coordinates:`, error);
    return null;
  }
}

/**
 * Converts an array of H3 indexes to coordinate pairs
 * @param h3Indexes - Array of H3 cell indexes
 * @returns Array of [lat, lng] coordinate pairs, filtering out invalid ones
 */
export function h3ArrayToLatLngArray(h3Indexes: string[]): [number, number][] {
  return h3Indexes
    .map(h3ToLatLng)
    .filter((coords): coords is [number, number] => coords !== null);
}

/**
 * Converts aircraft trace positions with H3 indexes to coordinate-based positions
 * @param positions - Array of positions with h3_index
 * @returns Array of positions with lat/lng coordinates
 */
export function convertTracePositionsToCoordinates<T extends { h3_index: string }>(
  positions: T[]
): (T & { lat: number; lng: number })[] {
  return positions
    .map(position => {
      const coords = h3ToLatLng(position.h3_index);
      if (!coords) return null;
      
      return {
        ...position,
        lat: coords[0],
        lng: coords[1]
      };
    })
    .filter((position): position is T & { lat: number; lng: number } => position !== null);
}
