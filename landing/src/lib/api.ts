import { API_BASE_URL, API_ENDPOINTS } from './utils';
import { Airport, AirportTransitionsResponse, AircraftPositionsAtTimeResponse, AircraftTraceResponse, RecordingsListResponse } from '@/types/api';

class ApiError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'ApiError';
  }
}

async function fetchApi<T>(endpoint: string): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;

  try {
    const response = await fetch(url);

    if (!response.ok) {
      // Try to get error details from response body
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      try {
        const errorBody = await response.text();
        if (errorBody) {
          errorMessage += ` - ${errorBody}`;
        }
      } catch {
        // Ignore error reading body
      }
      throw new ApiError(response.status, errorMessage);
    }

    return await response.json();
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new Error(`Failed to fetch from ${url}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function getAirportByIcao(icao: string): Promise<Airport> {
  return fetchApi<Airport>(API_ENDPOINTS.AIRPORT_BY_ICAO(icao));
}

export async function getAirportTransitions(
  icao: string,
  startTime?: string,
  endTime?: string
): Promise<AirportTransitionsResponse> {
  let endpoint = API_ENDPOINTS.AIRPORT_TRANSITIONS(icao);

  const params = new URLSearchParams();
  if (startTime) params.append('start_time', startTime);
  if (endTime) params.append('end_time', endTime);

  if (params.toString()) {
    endpoint += `?${params.toString()}`;
  }

  return fetchApi<AirportTransitionsResponse>(endpoint);
}

export async function getAirportTransitionsExtended(
  icao: string,
  startTime?: string,
  endTime?: string
): Promise<AirportTransitionsResponse> {
  let endpoint = API_ENDPOINTS.AIRPORT_TRANSITIONS_EXTENDED(icao);

  const params = new URLSearchParams();
  if (startTime) params.append('start_time', startTime);
  if (endTime) params.append('end_time', endTime);

  if (params.toString()) {
    endpoint += `?${params.toString()}`;
  }

  return fetchApi<AirportTransitionsResponse>(endpoint);
}

// Helper function to get transitions for the last 14 days
export async function getAirportTransitionsLast14Days(icao: string): Promise<AirportTransitionsResponse> {
  const endTime = new Date();
  const startTime = new Date(endTime.getTime() - 14 * 24 * 60 * 60 * 1000); // 14 days ago

  return getAirportTransitions(
    icao,
    startTime.toISOString(),
    endTime.toISOString()
  );
}

// Get aircraft positions within a bounding box
export async function getAircraftPositionsInBbox(
  north: number,
  east: number,
  south: number,
  west: number,
  referenceTime?: string,
  lookbackSeconds: number = 7
): Promise<AircraftPositionsAtTimeResponse> {
  const params = new URLSearchParams({
    north: north.toString(),
    east: east.toString(),
    south: south.toString(),
    west: west.toString(),
    lookback_seconds: lookbackSeconds.toString(),
  });

  if (referenceTime) {
    params.append('reference_time', referenceTime);
  }

  const endpoint = `${API_ENDPOINTS.AIRCRAFT_POSITIONS_BBOX}?${params.toString()}`;
  console.log('Aircraft positions endpoint:', endpoint);
  console.log('Full URL:', `${API_BASE_URL}${endpoint}`);
  return fetchApi<AircraftPositionsAtTimeResponse>(endpoint);
}

// Get aircraft trace (flight path)
export async function getAircraftTrace(
  hex: string,
  startTime?: string,
  endTime?: string
): Promise<AircraftTraceResponse> {
  let endpoint = API_ENDPOINTS.AIRCRAFT_TRACE(hex);

  const params = new URLSearchParams();
  if (startTime) params.append('start_time', startTime);
  if (endTime) params.append('end_time', endTime);

  if (params.toString()) {
    endpoint += `?${params.toString()}`;
  }

  return fetchApi<AircraftTraceResponse>(endpoint);
}

// Get recordings for a station and date
export async function getRecordings(
  station: string,
  date: string // YYYYMMDD format
): Promise<RecordingsListResponse> {
  const endpoint = API_ENDPOINTS.RECORDINGS_LIST(station, date);
  return fetchApi<RecordingsListResponse>(endpoint);
}
