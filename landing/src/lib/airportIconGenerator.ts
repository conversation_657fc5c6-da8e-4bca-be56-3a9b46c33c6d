import { Airport, Runway } from '@/types/api';
import L from 'leaflet';

// --- INTERFACES (Data Structures) ---

interface Point {
  x: number;
  y: number;
}

interface Point2D {
  lng: number;
  lat: number;
}

interface RunwayLine {
  start: Point;
  end: Point;
  name: string;
}

// CHANGED: This now stores the radius in meters, which is more useful for scaling.
interface BoundingCircle {
  centerLng: number;
  centerLat: number;
  radiusMeters: number;
}

// NEW: Represents a circle in the local 2D projected coordinate system.
interface LocalCircle {
  center: Point;
  radius: number;
}


// --- COORDINATE SYSTEM CONVERSION ---

/**
 * Converts geographic coordinates (lng, lat) to a local 2D coordinate system
 * relative to a center point. This projection is essential for accurate geometric calculations.
 */
function geoToLocal(lng: number, lat: number, centerLng: number, centerLat: number): Point {
  // Use a standard approximation for converting degrees to meters.
  const metersPerDegreeLng = 111320 * Math.cos(centerLat * Math.PI / 180);
  const metersPerDegreeLat = 110540;
  
  const x = (lng - centerLng) * metersPerDegreeLng;
  const y = (lat - centerLat) * metersPerDegreeLat;
  
  return { x, y };
}

/**
 * Converts a local 2D point back to geographic coordinates.
 */
function localToGeo(point: Point, centerLng: number, centerLat: number): Point2D {
    const metersPerDegreeLng = 111320 * Math.cos(centerLat * Math.PI / 180);
    const metersPerDegreeLat = 110540;

    const lng = (point.x / metersPerDegreeLng) + centerLng;
    const lat = (point.y / metersPerDegreeLat) + centerLat;

    return { lng, lat };
}


// --- GEOMETRIC HELPERS (for the Local 2D Plane) ---
// These functions now correctly operate on projected {x, y} points.

/**
 * Calculates the squared Euclidean distance between two local points.
 * (Using squared distance is often faster as it avoids a square root).
 */
function localDistanceSq(p1: Point, p2: Point): number {
  return Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2);
}

/**
 * Creates a circle from a single point (radius 0).
 */
function localCircleFromPoint(p: Point): LocalCircle {
  return { center: p, radius: 0 };
}

/**
 * Creates a circle from two points (diameter).
 */
function localCircleFromTwoPoints(p1: Point, p2: Point): LocalCircle {
  const centerX = (p1.x + p2.x) / 2;
  const centerY = (p1.y + p2.y) / 2;
  const radius = Math.sqrt(localDistanceSq(p1, p2)) / 2;
  return { center: { x: centerX, y: centerY }, radius };
}

/**
 * Creates a circumcircle from three points.
 */
function localCircleFromThreePoints(p1: Point, p2: Point, p3: Point): LocalCircle | null {
  const d = 2 * (p1.x * (p2.y - p3.y) + p2.x * (p3.y - p1.y) + p3.x * (p1.y - p2.y));

  // If d is close to zero, the points are collinear.
  if (Math.abs(d) < 1e-10) {
    // For collinear points, the smallest circle is defined by the two most distant points.
    const d12 = localDistanceSq(p1, p2);
    const d13 = localDistanceSq(p1, p3);
    const d23 = localDistanceSq(p2, p3);

    if (d12 >= d13 && d12 >= d23) return localCircleFromTwoPoints(p1, p2);
    if (d13 >= d23) return localCircleFromTwoPoints(p1, p3);
    return localCircleFromTwoPoints(p2, p3);
  }

  const ux = ((p1.x * p1.x + p1.y * p1.y) * (p2.y - p3.y) + (p2.x * p2.x + p2.y * p2.y) * (p3.y - p1.y) + (p3.x * p3.x + p3.y * p3.y) * (p1.y - p2.y)) / d;
  const uy = ((p1.x * p1.x + p1.y * p1.y) * (p3.x - p2.x) + (p2.x * p2.x + p2.y * p2.y) * (p1.x - p3.x) + (p3.x * p3.x + p3.y * p3.y) * (p2.x - p1.x)) / d;

  const center = { x: ux, y: uy };
  const radius = Math.sqrt(localDistanceSq(center, p1));

  return { center, radius };
}

/**
 * Checks if a point is inside or on a local circle boundary.
 */
function isPointInLocalCircle(point: Point, circle: LocalCircle): boolean {
  return localDistanceSq(point, circle.center) <= circle.radius * circle.radius + 1e-10;
}


// --- WELZL'S ALGORITHM (for Local 2D Points) ---

/**
 * Welzl's algorithm for finding the smallest enclosing circle.
 *
 * This is the optimal randomized algorithm with O(n) expected time complexity.
 * It works by recursively building the circle, ensuring that certain points
 * lie on the boundary of the final circle.
 *
 * @param points - Array of points to enclose (will be modified during recursion)
 * @param boundary - Points that must lie on the circle boundary (0-3 points)
 * @returns The smallest circle enclosing all points with boundary constraints
 */
function welzlAlgorithm(points: Point[], boundary: Point[] = []): LocalCircle {
  // Base cases: when we have no more points or boundary is fully determined
  if (points.length === 0 || boundary.length === 3) {
    switch (boundary.length) {
      case 0:
        // No constraints - return empty circle
        return { center: { x: 0, y: 0 }, radius: 0 };
      case 1:
        // Circle with single point (radius 0)
        return localCircleFromPoint(boundary[0]);
      case 2:
        // Circle with two points as diameter
        return localCircleFromTwoPoints(boundary[0], boundary[1]);
      case 3:
        // Circle through three points (circumcircle)
        return localCircleFromThreePoints(boundary[0], boundary[1], boundary[2])!;
      default:
        // Should never be reached
        return { center: { x: 0, y: 0 }, radius: 0 };
    }
  }

  // Welzl's key insight: pick a random point and recurse
  const p = points[points.length - 1];
  const remainingPoints = points.slice(0, -1);

  // Find the smallest circle for remaining points with current boundary
  const circle = welzlAlgorithm(remainingPoints, boundary);

  // If our picked point is already inside this circle, we're done
  if (isPointInLocalCircle(p, circle)) {
    return circle;
  }

  // Point is outside - it must be on the boundary of the optimal solution
  // Recurse with this point added to the boundary constraint
  return welzlAlgorithm(remainingPoints, [...boundary, p]);
}


// --- MAIN LOGIC & SVG GENERATION ---

/**
 * REFACTORED: Calculates the smallest enclosing circle for all runway points.
 */
function calculateRunwayBoundingCircle(runways: Runway[]): BoundingCircle | null {
  const allGeoPoints: Point2D[] = [];

  // 1. Collect all geographic runway points
  for (const runway of runways) {
    if (runway.centerline?.type === 'LineString') {
      const coordinates = runway.centerline.coordinates as number[][];
      for (const [lng, lat] of coordinates) {
        allGeoPoints.push({ lng, lat });
      }
    }
  }

  if (allGeoPoints.length === 0) {
    return null;
  }

  // 2. Find an average center point for an accurate projection
  let totalLng = 0, totalLat = 0;
  for (const p of allGeoPoints) {
    totalLng += p.lng;
    totalLat += p.lat;
  }
  const centerLng = totalLng / allGeoPoints.length;
  const centerLat = totalLat / allGeoPoints.length;

  // 3. Project all geographic points to a local 2D plane
  const localPoints: Point[] = allGeoPoints.map(p => geoToLocal(p.lng, p.lat, centerLng, centerLat));

  // 4. Shuffle points for Welzl's algorithm's O(n) expected time
  for (let i = localPoints.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [localPoints[i], localPoints[j]] = [localPoints[j], localPoints[i]];
  }

  // 5. Run Welzl's algorithm on the *local* points
  const localCircle = welzlAlgorithm(localPoints);

  // 6. Convert the local circle's center back to geographic coordinates
  const finalGeoCenter = localToGeo(localCircle.center, centerLng, centerLat);

  return {
    centerLng: finalGeoCenter.lng,
    centerLat: finalGeoCenter.lat,
    radiusMeters: localCircle.radius, // Radius is already in meters from the projection
  };
}

/**
 * Converts runway centerlines to local coordinates for SVG rendering.
 */
function processRunwaysForSVG(runways: Runway[], centerLng: number, centerLat: number, scale: number): RunwayLine[] {
  const runwayLines: RunwayLine[] = [];
  
  for (const runway of runways) {
    if (runway.centerline?.type === 'LineString') {
      const coords = runway.centerline.coordinates as number[][];
      if (coords.length < 2) continue;

      const [startLng, startLat] = coords[0];
      const [endLng, endLat] = coords[coords.length - 1];
      
      // Project using geoToLocal but also apply the SVG scale factor
      const startLocal = geoToLocal(startLng, startLat, centerLng, centerLat);
      const endLocal = geoToLocal(endLng, endLat, centerLng, centerLat);

      runwayLines.push({
        start: { x: startLocal.x * scale, y: startLocal.y * scale },
        end: { x: endLocal.x * scale, y: endLocal.y * scale },
        name: runway.name
      });
    }
  }
  return runwayLines;
}

/**
 * Generates the final SVG content for the airport icon.
 */
function generateAirportSVG(runwayLines: RunwayLine[], iconRadius: number, isControlled: boolean): string {
  const size = iconRadius * 2;
  const center = iconRadius;
  
  const circleColor = isControlled ? '#0066cc' : '#cc0066'; // Blue for controlled, Magenta for uncontrolled
  const runwayColor = '#333333';
  const fillColor = isControlled ? '#e6f2ff' : '#ffe6f2';
  
  let svgContent = `
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
      <circle cx="${center}" cy="${center}" r="${iconRadius - 2}" fill="${fillColor}" stroke="${circleColor}" stroke-width="2" opacity="0.9" />`;
  
  for (const runway of runwayLines) {
    // The runway start/end points are already scaled. Add them relative to the SVG center.
    // Flip Y-axis for SVG coordinate system (y grows downwards).
    svgContent += `
      <line x1="${center + runway.start.x}" y1="${center - runway.start.y}" x2="${center + runway.end.x}" y2="${center - runway.end.y}" stroke="${runwayColor}" stroke-width="2.5" stroke-linecap="round" />`;
  }
  
  svgContent += `</svg>`;
  return svgContent;
}


// --- EXPORTED MAIN FUNCTIONS ---

/**
 * Main function to generate the complete airport icon SVG string.
 */
export function generateAirportIcon(airport: Airport, iconSize: number = 40): string | null {
  if (!airport.runways || airport.runways.length === 0) {
    return null;
  }

  // 1. Calculate the true smallest enclosing circle using the corrected algorithm.
  const boundingCircle = calculateRunwayBoundingCircle(airport.runways);
  if (!boundingCircle || boundingCircle.radiusMeters === 0) {
    return null;
  }

  const { centerLng, centerLat, radiusMeters } = boundingCircle;
  const iconRadius = iconSize / 2;

  // Add 15% padding by expanding the bounding circle radius (not shrinking the runways)
  const paddingFactor = 1.15; // Expand the circle by 15% to create breathing room
  const expandedRadiusMeters = radiusMeters * paddingFactor;

  // 2. Calculate the scale needed to fit the expanded runway layout into the icon.
  const scale = iconRadius / expandedRadiusMeters;

  // 3. Process runway lines for SVG rendering using the calculated center and scale.
  const runwayLines = processRunwaysForSVG(airport.runways, centerLng, centerLat, scale);

  if (runwayLines.length === 0) {
    return null;
  }

  // Determine if airport is controlled (example logic)
  const isControlled = airport.runways.some(r => r.length_ft && r.length_ft > 5000);

  // 4. Generate the final SVG.
  return generateAirportSVG(runwayLines, iconRadius, isControlled);
}

/**
 * Creates a Leaflet DivIcon for the airport.
 */
export function createAirportIcon(airport: Airport, iconSize: number = 40): L.DivIcon | null {
  const svgContent = generateAirportIcon(airport, iconSize);
  
  if (!svgContent) {
    return null;
  }
  
  return L.divIcon({
    html: svgContent,
    className: 'airport-icon',
    iconSize: [iconSize, iconSize],
    iconAnchor: [iconSize / 2, iconSize / 2],
  });
}
