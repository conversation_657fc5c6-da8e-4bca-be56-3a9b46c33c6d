import L from 'leaflet';

// Custom pan control for Leaflet
export const PanControl = L.Control.extend({
  options: {
    position: 'topleft'
  },

  onAdd: function(map: L.Map) {
    const container = L.DomUtil.create('div', 'leaflet-control-pan leaflet-bar');

    // Create pan buttons in cross pattern
    const panUp = L.<PERSON>til.create('a', 'leaflet-control-pan-up', container);
    const panLeft = L.DomUtil.create('a', 'leaflet-control-pan-left', container);
    const panRight = L.DomUtil.create('a', 'leaflet-control-pan-right', container);
    const panDown = L.DomUtil.create('a', 'leaflet-control-pan-down', container);

    panUp.innerHTML = '▲';
    panDown.innerHTML = '▼';
    panLeft.innerHTML = '◀';
    panRight.innerHTML = '▶';

    panUp.href = '#';
    panDown.href = '#';
    panLeft.href = '#';
    panRight.href = '#';

    panUp.title = 'Pan up';
    panDown.title = 'Pan down';
    panLeft.title = 'Pan left';
    panRight.title = 'Pan right';

    // Pan distance (in pixels)
    const panDistance = 100;

    // Add event listeners
    L.DomEvent.on(panUp, 'click', function(e) {
      L.DomEvent.preventDefault(e);
      map.panBy([0, -panDistance]);
    });

    L.DomEvent.on(panDown, 'click', function(e) {
      L.DomEvent.preventDefault(e);
      map.panBy([0, panDistance]);
    });

    L.DomEvent.on(panLeft, 'click', function(e) {
      L.DomEvent.preventDefault(e);
      map.panBy([-panDistance, 0]);
    });

    L.DomEvent.on(panRight, 'click', function(e) {
      L.DomEvent.preventDefault(e);
      map.panBy([panDistance, 0]);
    });

    // Prevent map events on control
    L.DomEvent.disableClickPropagation(container);
    L.DomEvent.disableScrollPropagation(container);

    return container;
  }
});

// Factory function
export const panControl = (options?: L.ControlOptions) => new PanControl(options);
