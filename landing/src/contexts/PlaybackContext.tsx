'use client';

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { RecordingFile, RecordingsListResponse } from '@/types/api';
import { timeUtils } from '@/lib/utils';

// Types
interface PlaybackState {
  // Date and recordings
  selectedDate: Date;
  recordings: RecordingFile[];
  loading: boolean;
  error: string | null;
  
  // Time and playback
  currentTime: number; // seconds from start of day
  isPlaying: boolean;
  currentRecording: RecordingFile | null;
  
  // Audio state
  isLoading: boolean;
  volume: number;
  isMuted: boolean;
}

type PlaybackAction =
  | { type: 'SET_DATE'; payload: Date }
  | { type: 'SET_RECORDINGS'; payload: RecordingsListResponse }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_CURRENT_TIME'; payload: number }
  | { type: 'SET_IS_PLAYING'; payload: boolean }
  | { type: 'SET_CURRENT_RECORDING'; payload: RecordingFile | null }
  | { type: 'SET_AUDIO_LOADING'; payload: boolean }
  | { type: 'SET_VOLUME'; payload: number }
  | { type: 'SET_MUTED'; payload: boolean }
  | { type: 'RESET_STATE' };

interface PlaybackContextType {
  state: PlaybackState;
  dispatch: React.Dispatch<PlaybackAction>;
  
  // Computed values
  currentHour: number;
  recordingsForCurrentHour: RecordingFile[];
  
  // Actions
  setDate: (date: Date) => void;
  setCurrentTime: (time: number) => void;
  jumpTime: (seconds: number) => void;
  togglePlayback: () => void;
  findRecordingAtTime: (time: number) => RecordingFile | null;
  findNearbyRecordings: (time: number, windowSeconds?: number) => RecordingFile[];
}

// Initial state
const initialState: PlaybackState = {
  selectedDate: new Date(),
  recordings: [],
  loading: false,
  error: null,
  currentTime: 0,
  isPlaying: false,
  currentRecording: null,
  isLoading: false,
  volume: 80,
  isMuted: false,
};

// Reducer
function playbackReducer(state: PlaybackState, action: PlaybackAction): PlaybackState {
  switch (action.type) {
    case 'SET_DATE':
      return { ...state, selectedDate: action.payload };
    case 'SET_RECORDINGS':
      return { 
        ...state, 
        recordings: action.payload.recordings,
        loading: false,
        error: null 
      };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'SET_CURRENT_TIME':
      return { ...state, currentTime: action.payload };
    case 'SET_IS_PLAYING':
      return { ...state, isPlaying: action.payload };
    case 'SET_CURRENT_RECORDING':
      return { ...state, currentRecording: action.payload };
    case 'SET_AUDIO_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_VOLUME':
      return { ...state, volume: action.payload };
    case 'SET_MUTED':
      return { ...state, isMuted: action.payload };
    case 'RESET_STATE':
      return { ...initialState, selectedDate: state.selectedDate };
    default:
      return state;
  }
}

// Context
const PlaybackContext = createContext<PlaybackContextType | undefined>(undefined);

// Provider component
export function PlaybackProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(playbackReducer, initialState);

  // Computed values
  const currentHour = Math.floor(state.currentTime / 3600);
  
  const recordingsForCurrentHour = state.recordings.filter(recording => {
    const hourStart = currentHour * 3600;
    const hourEnd = (currentHour + 1) * 3600;
    const recordingStart = timeUtils.timeToSeconds(recording.start_time);
    const recordingEnd = recordingStart + (recording.duration_ms / 1000);
    return recordingEnd > hourStart && recordingStart < hourEnd;
  });

  // Actions
  const setDate = useCallback((date: Date) => {
    dispatch({ type: 'SET_DATE', payload: date });
  }, []);

  const setCurrentTime = useCallback((time: number) => {
    const clampedTime = timeUtils.clampTime(time);
    dispatch({ type: 'SET_CURRENT_TIME', payload: clampedTime });
  }, []);

  const jumpTime = useCallback((seconds: number) => {
    const newTime = timeUtils.clampTime(state.currentTime + seconds);
    dispatch({ type: 'SET_CURRENT_TIME', payload: newTime });
  }, [state.currentTime]);

  const togglePlayback = useCallback(() => {
    dispatch({ type: 'SET_IS_PLAYING', payload: !state.isPlaying });
  }, [state.isPlaying]);

  const findRecordingAtTime = useCallback((time: number): RecordingFile | null => {
    return state.recordings.find(recording => 
      timeUtils.isTimeInRecording(time, recording)
    ) || null;
  }, [state.recordings]);

  const findNearbyRecordings = useCallback((time: number, windowSeconds: number = 60): RecordingFile[] => {
    return state.recordings.filter(recording => {
      const startTime = timeUtils.timeToSeconds(recording.start_time);
      const endTime = startTime + (recording.duration_ms / 1000);
      return Math.abs(startTime - time) <= windowSeconds || Math.abs(endTime - time) <= windowSeconds;
    });
  }, [state.recordings]);

  // Auto-update current recording when time changes
  useEffect(() => {
    const recordingAtTime = findRecordingAtTime(state.currentTime);
    if (recordingAtTime?.filename !== state.currentRecording?.filename) {
      dispatch({ type: 'SET_CURRENT_RECORDING', payload: recordingAtTime });
    }
  }, [state.currentTime, state.recordings, findRecordingAtTime, state.currentRecording]);

  const contextValue: PlaybackContextType = {
    state,
    dispatch,
    currentHour,
    recordingsForCurrentHour,
    setDate,
    setCurrentTime,
    jumpTime,
    togglePlayback,
    findRecordingAtTime,
    findNearbyRecordings,
  };

  return (
    <PlaybackContext.Provider value={contextValue}>
      {children}
    </PlaybackContext.Provider>
  );
}

// Hook to use the context
export function usePlayback() {
  const context = useContext(PlaybackContext);
  if (context === undefined) {
    throw new Error('usePlayback must be used within a PlaybackProvider');
  }
  return context;
}
