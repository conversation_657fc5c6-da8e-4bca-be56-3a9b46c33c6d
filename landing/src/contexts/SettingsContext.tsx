'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

/** Font size options for TV display optimization */
export type FontSize = 'small' | 'medium' | 'large';

/** Layout settings for persistent UI state */
export interface LayoutSettings {
  /** Resizable panel sizes as percentages */
  resizablePanelSizes?: number[];
  /** Overview map center coordinates [lat, lng] */
  mapCenter?: [number, number];
  /** Overview map zoom level */
  mapZoom?: number;
  /** Satellite map center coordinates [lat, lng] */
  satelliteMapCenter?: [number, number];
  /** Satellite map zoom level */
  satelliteMapZoom?: number;
}

/** Complete application settings */
export interface AppSettings {
  fontSize: FontSize;
  layout: LayoutSettings;
}

interface SettingsContextType {
  settings: AppSettings;
  updateFontSize: (fontSize: FontSize) => void;
  updateLayout: (layout: Partial<LayoutSettings>) => void;
  saveSettings: () => void;
  resetSettings: () => void;
}

const defaultSettings: AppSettings = {
  fontSize: 'medium',
  layout: {}
};

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

const STORAGE_KEY = 'airport-dashboard-settings';

export function SettingsProvider({ children }: { children: React.ReactNode }) {
  const [settings, setSettings] = useState<AppSettings>(defaultSettings);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load settings from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedSettings = JSON.parse(stored) as Partial<AppSettings>;
        // Validate and merge with defaults
        setSettings({
          fontSize: parsedSettings.fontSize || defaultSettings.fontSize,
          layout: { ...defaultSettings.layout, ...parsedSettings.layout }
        });
      }
    } catch (error) {
      console.warn('Failed to load settings from localStorage:', error);
      // Reset to defaults on error
      setSettings(defaultSettings);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Apply font size to document root
  useEffect(() => {
    if (!isLoaded) return;
    
    const root = document.documentElement;
    root.setAttribute('data-font-size', settings.fontSize);
  }, [settings.fontSize, isLoaded]);

  const updateFontSize = (fontSize: FontSize) => {
    setSettings(prev => ({ ...prev, fontSize }));
  };

  const updateLayout = (layout: Partial<LayoutSettings>) => {
    setSettings(prev => ({
      ...prev,
      layout: { ...prev.layout, ...layout }
    }));
  };

  const saveSettings = () => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save settings to localStorage:', error);
    }
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Failed to remove settings from localStorage:', error);
    }
  };

  const value: SettingsContextType = {
    settings,
    updateFontSize,
    updateLayout,
    saveSettings,
    resetSettings
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
}

export function useSettings() {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
}
