'use client';

import { useParams } from 'next/navigation';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';

import { cn } from '@/lib/utils';
import { PlaybackProvider, usePlayback } from '@/contexts/PlaybackContext';
import { useRecordings } from '@/hooks/useRecordings';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Skeleton } from '@/components/ui/skeleton';
import { AudioZoomedTimeline } from '@/components/AudioZoomedTimeline';
import { HourSelector } from '@/components/HourSelector';
import { HourlyTimeline } from '@/components/HourlyTimeline';
import { PlaybackControls } from '@/components/PlaybackControls';
import { useIsMobile } from '@/hooks/use-mobile';

// Internal component that uses the context
function PlaybackPageContent() {
  const params = useParams();
  const station = params.station as string;
  const isMobile = useIsMobile();

  // Use centralized state and hooks
  const { state, setDate, setCurrentTime } = usePlayback();
  const { selectedDate, currentTime } = state;
  const { recordings, loading, error } = useRecordings(station);

  // Format date for display
  const formatDateForDisplay = (date: Date) => {
    return format(date, 'PPP');
  };

  if (!station) {
    return <div className="p-8">Invalid station code</div>;
  }

  return (
    <div className="min-h-screen bg-slate-50 p-2 sm:p-4">
      <div className="max-w-6xl mx-auto space-y-4 sm:space-y-6">
        {/* Header */}
        <div className={cn(
          "flex items-center justify-between",
          isMobile && "flex-col space-y-4"
        )}>
          <div className={cn(isMobile && "text-center")}>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-900">Audio Playback Console</h1>
            <p className="text-slate-600">Station: {station}</p>
          </div>

          {/* Date and Hour Selectors */}
          <div className={cn(
            "flex items-center space-x-2",
            isMobile && "w-full flex-col space-y-2 space-x-0"
          )}>
            {/* Date Picker */}
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "justify-start text-left font-normal",
                    isMobile ? "w-full" : "w-[200px]",
                    !selectedDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedDate ? formatDateForDisplay(selectedDate) : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={(date) => date && setDate(date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>

            {/* Hour Selector */}
            {recordings.length > 0 && (
              <HourSelector
                recordings={recordings}
                currentTime={currentTime}
                onTimeChange={setCurrentTime}
                className={isMobile ? "w-full" : ""}
              />
            )}
          </div>
        </div>

        {/* Main Player Interface */}
        <div className="space-y-4">
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-600 mb-2">Error loading recordings</p>
              <p className="text-sm text-slate-500">{error}</p>
            </div>
          ) : recordings.length > 0 ? (
            <>
              {/* 5-Minute Zoomed Timeline */}
              <AudioZoomedTimeline
                recordings={recordings}
                currentTime={currentTime}
                onTimeChange={setCurrentTime}
              />

              {/* Player Controls */}
              <PlaybackControls />

              {/* Hourly Timeline at Bottom */}
              <HourlyTimeline
                recordings={recordings}
                currentTime={currentTime}
              />
            </>
          ) : (
            <div className="text-center py-8">
              <p className="text-slate-500">No recordings found for this date</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Main component with provider wrapper
export default function PlaybackPage() {
  return (
    <PlaybackProvider>
      <PlaybackPageContent />
    </PlaybackProvider>
  );
}
