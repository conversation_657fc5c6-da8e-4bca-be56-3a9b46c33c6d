@import "tailwindcss";
@import "tw-animate-css";
@import "leaflet/dist/leaflet.css";

/* Override Leaflet z-index values to ensure proper stacking */
.leaflet-map-pane,
.leaflet-tile-pane,
.leaflet-overlay-pane,
.leaflet-shadow-pane,
.leaflet-marker-pane,
.leaflet-tooltip-pane,
.leaflet-popup-pane,
.leaflet-control-container {
  z-index: 1 !important;
}

.leaflet-control-zoom {
  z-index: 2 !important;
}

/* Hide scrollbars for horizontal scrolling containers */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  html, body {
    @apply bg-background text-foreground;
    height: 100%;
    overflow: hidden;
  }
  #__next {
    height: 100%;
  }
}

/* Font size scaling for TV display optimization */
:root {
  --font-scale: 1;
}

[data-font-size="small"] {
  --font-scale: 0.875;
}

[data-font-size="large"] {
  --font-scale: 1.25;
}

/* Apply font scaling to all text elements */
body {
  font-size: calc(1rem * var(--font-scale));
}

/* Mobile drawer and map styles */
.pb-safe {
  padding-bottom: env(safe-area-inset-bottom, 1rem);
}

/* Leaflet map styles */
.leaflet-container {
  background: #f8fafc;
}

.leaflet-control-zoom {
  border: none !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
}

.leaflet-control-zoom a {
  background-color: white !important;
  border: none !important;
  color: #374151 !important;
  font-weight: 600 !important;
}

.leaflet-control-zoom a:hover {
  background-color: #f9fafb !important;
}

/* Mobile-specific map controls positioning */
@media (max-width: 767px) {
  .leaflet-control-zoom {
    margin-top: 10px !important;
    margin-right: 10px !important;
  }
}

/* Override Tailwind text utilities with scaling */
@layer utilities {
  .text-xs { font-size: calc(0.75rem * var(--font-scale)) !important; }
  .text-sm { font-size: calc(0.875rem * var(--font-scale)) !important; }
  .text-base { font-size: calc(1rem * var(--font-scale)) !important; }
  .text-lg { font-size: calc(1.125rem * var(--font-scale)) !important; }
  .text-xl { font-size: calc(1.25rem * var(--font-scale)) !important; }
  .text-2xl { font-size: calc(1.5rem * var(--font-scale)) !important; }
  .text-3xl { font-size: calc(1.875rem * var(--font-scale)) !important; }
  .text-4xl { font-size: calc(2.25rem * var(--font-scale)) !important; }
}

/* Aircraft icon styles */
.aircraft-icon {
  background: none !important;
  border: none !important;
  transition: transform 0.3s ease;
}

.aircraft-icon svg {
  filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.3));
}

/* Aircraft and runway label styles */
.aircraft-label-icon {
  background: none !important;
  border: none !important;
  pointer-events: none !important;
}

.runway-label-icon {
  background: none !important;
  border: none !important;
  pointer-events: none !important;
}

/* Airport icon styles */
.airport-icon {
  background: none !important;
  border: none !important;
  transition: transform 0.2s ease;
}

.airport-icon:hover {
  transform: scale(1.1);
}

.airport-icon svg {
  filter: drop-shadow(1px 1px 3px rgba(0,0,0,0.3));
}

/* Map controls - hidden by default, shown on hover */
.leaflet-control-container {
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.leaflet-container:hover .leaflet-control-container,
.leaflet-container:focus-within .leaflet-control-container {
  opacity: 1;
  pointer-events: auto;
}

/* Keep controls visible when interacting with them */
.leaflet-control-container:hover,
.leaflet-control-container:focus-within {
  opacity: 1;
  pointer-events: auto;
}

/* Show controls on touch devices */
@media (hover: none) and (pointer: coarse) {
  .leaflet-control-container {
    opacity: 1;
    pointer-events: auto;
  }
}

/* Ensure map controls have proper z-index */
.leaflet-control-zoom,
.leaflet-control-pan {
  z-index: 1000 !important;
}

/* Style the pan controls */
.leaflet-control-pan {
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 5px rgba(0,0,0,0.4);
  display: grid;
  grid-template-columns: 26px 26px 26px;
  grid-template-rows: 26px 26px;
  gap: 1px;
  padding: 2px;
}

.leaflet-control-pan a {
  background-color: white;
  border: none;
  width: 26px;
  height: 26px;
  line-height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: #333;
  font-size: 14px;
  font-weight: bold;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.leaflet-control-pan a:hover {
  background-color: #f0f0f0;
}

.leaflet-control-pan-up {
  grid-column: 2;
  grid-row: 1;
}

.leaflet-control-pan-left {
  grid-column: 1;
  grid-row: 2;
}

.leaflet-control-pan-right {
  grid-column: 3;
  grid-row: 2;
}

.leaflet-control-pan-down {
  grid-column: 2;
  grid-row: 2;
}
