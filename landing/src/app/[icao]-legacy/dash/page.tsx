'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { getAirportByIcao, getAirportTransitionsLast14Days } from '@/lib/api';
import { Airport, AirportTransitionsResponse, AirportTransition } from '@/types/api';
import { convertToAirportTime, getAirportDateKey, countTodaysTransitions } from '@/lib/utils';
import dynamic from 'next/dynamic';
import AirportHeader from '@/components/AirportHeader';
import TransitionsSidebar from '@/components/TransitionsSidebar';
import TransitionsHorizontal from '@/components/TransitionsHorizontal';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useIsMobile } from '@/hooks/use-mobile';
import { usePersistentLayout } from '@/hooks/use-persistent-layout';
import { MapIcon, ClockIcon, SatelliteIcon } from 'lucide-react';

// Dynamically import the map components to avoid SSR issues
const AirportMap = dynamic(() => import('@/components/AirportMap'), {
  ssr: false,
  loading: () => <div className="bg-slate-100 flex items-center justify-center h-full">Loading map...</div>
});

const AirportMapSatellite = dynamic(() => import('@/components/AirportMapSatellite'), {
  ssr: false,
  loading: () => <div className="bg-slate-100 flex items-center justify-center h-full">Loading satellite map...</div>
});

export default function AirportPage() {
  const params = useParams();
  const icao = params.icao as string;
  const isMobile = useIsMobile();
  const { panelGroupRef, handleLayoutChange, defaultSizes } = usePersistentLayout();

  const [airport, setAirport] = useState<Airport | null>(null);
  const [transitions, setTransitions] = useState<AirportTransitionsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Fetch airport info (only once)
  useEffect(() => {
    if (!icao) return;

    const fetchAirport = async () => {
      try {
        const airportData = await getAirportByIcao(icao.toUpperCase());
        setAirport(airportData);
      } catch (err) {
        console.error('Failed to fetch airport:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch airport data');
      }
    };

    fetchAirport();
  }, [icao]);

  // Fetch transitions (initially and every 5 seconds)
  useEffect(() => {
    if (!icao) return;

    const fetchTransitions = async () => {
      try {
        setError(null);
        const transitionsData = await getAirportTransitionsLast14Days(icao.toUpperCase());
        setTransitions(transitionsData);
        setLastUpdated(new Date());
        setLoading(false);
      } catch (err) {
        console.error('Failed to fetch transitions:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch transitions data');
        setLoading(false);
      }
    };

    // Initial fetch
    fetchTransitions();

    // Set up interval for every 5 seconds
    const interval = setInterval(fetchTransitions, 5000);

    return () => clearInterval(interval);
  }, [icao]);

  // Group transitions by date
  const groupTransitionsByDate = (transitions: AirportTransition[]) => {
    const groups: { [key: string]: AirportTransition[] } = {};

    transitions.forEach(transition => {
      const airportTime = convertToAirportTime(transition.time);
      const dateKey = getAirportDateKey(airportTime);

      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(transition);
    });

    // Sort groups by date (most recent first)
    const sortedGroups = Object.entries(groups).sort(([dateA], [dateB]) => {
      return dateB.localeCompare(dateA); // Simple string comparison for YYYY-MM-DD format
    });

    return sortedGroups.map(([dateKey, transitions]) => ({
      date: dateKey,
      transitions: transitions.sort((a, b) =>
        new Date(b.time).getTime() - new Date(a.time).getTime()
      )
    }));
  };

  if (!icao) {
    return <div className="p-8">Invalid airport code</div>;
  }

  const groupedTransitions = transitions ? groupTransitionsByDate(transitions.transitions) : [];
  const todaysOperationsCount = transitions ? countTodaysTransitions(transitions.transitions) : 0;

  if (isMobile) {
    return (
      <div className="h-screen flex flex-col bg-slate-50 overflow-hidden">
        {/* Header */}
        <AirportHeader
          airport={airport}
          totalTransitions={todaysOperationsCount}
          aircraftCount={0}
          lastUpdated={lastUpdated}
          loading={loading}
        />

        {/* Mobile Tabs Layout */}
        <div className="flex-1 overflow-hidden">
          <Tabs defaultValue="transitions" className="h-full flex flex-col">
            <div className="px-4 py-2 bg-white border-b">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="transitions" className="flex items-center space-x-1">
                  <ClockIcon className="h-4 w-4" />
                  <span className="hidden sm:inline">Transitions</span>
                  <span className="sm:hidden">Trans</span>
                </TabsTrigger>
                <TabsTrigger value="map" className="flex items-center space-x-1">
                  <MapIcon className="h-4 w-4" />
                  <span className="hidden sm:inline">Overview</span>
                  <span className="sm:hidden">Over</span>
                </TabsTrigger>
                <TabsTrigger value="satellite" className="flex items-center space-x-1">
                  <SatelliteIcon className="h-4 w-4" />
                  <span className="hidden sm:inline">Satellite</span>
                  <span className="sm:hidden">Sat</span>
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="map" className="flex-1 m-0">
              <div className="h-full">
                {airport ? (
                  <AirportMap airport={airport} className="h-full" />
                ) : (
                  <div className="h-full bg-slate-100 flex items-center justify-center">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                      <p className="text-slate-500">Loading airport data...</p>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="satellite" className="flex-1 m-0">
              <div className="h-full">
                {airport ? (
                  <AirportMapSatellite airport={airport} className="h-full" />
                ) : (
                  <div className="h-full bg-slate-100 flex items-center justify-center">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                      <p className="text-slate-500">Loading airport data...</p>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="transitions" className="flex-1 m-0">
              <TransitionsSidebar
                groupedTransitions={groupedTransitions}
                loading={loading}
                error={error}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-slate-50 overflow-hidden">
      {/* Desktop Header */}
      <AirportHeader
        airport={airport}
        totalTransitions={todaysOperationsCount}
        aircraftCount={0}
        lastUpdated={lastUpdated}
        loading={loading}
      />

      {/* Desktop Layout with Dual Maps and Fixed Horizontal Transitions */}
      <div className="flex-1 overflow-hidden flex flex-col">
        {/* Top Section - Dual Maps */}
        <div className="flex-1 overflow-hidden">
          <ResizablePanelGroup
            ref={panelGroupRef}
            direction="horizontal"
            className="h-full"
            onLayout={handleLayoutChange}
          >
            {/* Left Map - Overview */}
            <ResizablePanel defaultSize={defaultSizes[0] || 50} minSize={30}>
              <div className="h-full relative">
                <div className="absolute top-2 left-2 z-10 bg-white/90 px-2 py-1 rounded text-sm font-medium text-slate-700 shadow">
                  Overview Map
                </div>
                {airport ? (
                  <AirportMap airport={airport} className="h-full" />
                ) : (
                  <div className="h-full bg-slate-100 flex items-center justify-center">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                      <p className="text-slate-500">Loading airport data...</p>
                    </div>
                  </div>
                )}
              </div>
            </ResizablePanel>

            <ResizableHandle withHandle />

            {/* Right Map - Satellite */}
            <ResizablePanel defaultSize={defaultSizes[1] || 50} minSize={30}>
              <div className="h-full relative">
                <div className="absolute top-2 left-2 z-10 bg-white/90 px-2 py-1 rounded text-sm font-medium text-slate-700 shadow">
                  Satellite View
                </div>
                {airport ? (
                  <AirportMapSatellite airport={airport} className="h-full" />
                ) : (
                  <div className="h-full bg-slate-100 flex items-center justify-center">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                      <p className="text-slate-500">Loading airport data...</p>
                    </div>
                  </div>
                )}
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>

        {/* Bottom Section - Auto-Height Horizontal Transitions */}
        <div className="flex-shrink-0">
          <TransitionsHorizontal
            groupedTransitions={groupedTransitions}
            loading={loading}
            error={error}
          />
        </div>
      </div>
    </div>
  );
}
