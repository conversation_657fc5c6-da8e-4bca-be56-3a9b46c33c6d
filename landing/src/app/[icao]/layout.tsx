import type { Metadata, Viewport } from 'next'

export const metadata: Metadata = {
  title: 'Skytraces',
  description: 'Real-time airport operations dashboard showing aircraft movements, takeoffs, and landings',
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  viewportFit: 'cover',
}

export default function Test2Layout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-gray-100 overflow-auto">
      {children}
    </div>
  )
}
