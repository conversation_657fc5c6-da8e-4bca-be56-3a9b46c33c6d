"use client"

import React, { memo, useCallback } from 'react'
import { Card } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { PlaneLanding, PlaneTakeoff, Clock, Plane } from 'lucide-react'
import { PlanePatternIcon } from './PlanePatternIcon'
import { PlaneTaxiIcon } from './PlaneTaxiIcon'
import { formatOperationTime } from '../utils/dateUtils'
import type { FlightOperation } from '../types'

interface OperationCardProps {
  operation: FlightOperation
  onSelect?: (operation: FlightOperation) => void
}

const OperationCardComponent = ({ operation, onSelect }: OperationCardProps) => {
  const getOperationIcon = useCallback(() => {
    switch (operation.operationType) {
      case 'landing':
        return <PlaneLanding className="w-6 h-6 text-blue-600" />
      case 'takeoff':
        return <PlaneTakeoff className="w-6 h-6 text-green-600" />
      case 'pattern':
        return <PlanePatternIcon className="w-6 h-6 text-orange-600" />
      case 'taxi':
        return <PlaneTaxiIcon className="w-6 h-6 text-purple-600" />
      default:
        return <Plane className="w-6 h-6 text-gray-600" />
    }
  }, [operation.operationType])

  const getOperationColor = useCallback(() => {
    switch (operation.operationType) {
      case 'landing': return 'bg-blue-50 border-blue-200'
      case 'takeoff': return 'bg-green-50 border-green-200'
      case 'pattern': return 'bg-orange-50 border-orange-200'
      case 'taxi': return 'bg-purple-50 border-purple-200'
      default: return 'bg-gray-50 border-gray-200'
    }
  }, [operation.operationType])

  const handleClick = useCallback(() => {
    onSelect?.(operation)
  }, [onSelect, operation])

  return (
    <Card
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md border-2 p-4",
        operation.isSelected
          ? "border-blue-500 bg-blue-50"
          : "border-gray-200 hover:border-gray-300"
      )}
      onClick={handleClick}
    >
      <div className="flex items-center gap-4">
        {/* Operation Type Icon */}
        <div className="flex-shrink-0">
          <div className={cn(
            "w-12 h-12 rounded-lg flex items-center justify-center border-2",
            getOperationColor()
          )}>
            {getOperationIcon()}
          </div>
        </div>

        {/* Flight Info - Compact 2-line layout */}
        <div className="flex-1 min-w-0">
          {/* Line 1: Registration + Operation Type + Time */}
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-gray-900">{operation.registration}</h3>
              <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 capitalize">
                {operation.operationType}
              </span>
            </div>
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <Clock className="w-3 h-3" />
              <span className="font-medium">{formatOperationTime(operation.time)}</span>
            </div>
          </div>

          {/* Line 2: Aircraft Type + Runway */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <Plane className="w-3 h-3" />
              <span>{operation.aircraftType}</span>
            </div>
            <div className="text-sm text-gray-600">
              <span className="font-medium">RWY {operation.runway}</span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}

// Memoized export for performance
export const OperationCard = memo(OperationCardComponent)

interface SectionHeaderProps {
  title: string
  className?: string
}

export function SectionHeader({ title, className }: SectionHeaderProps) {
  return (
    <h2 className={cn("text-lg font-semibold text-gray-900 mb-3 mt-6 first:mt-0", className)}>
      {title}
    </h2>
  )
}
