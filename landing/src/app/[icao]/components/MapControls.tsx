"use client"

import React from 'react'
import { cn } from '@/lib/utils'
import { Maximize2 } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface MapControlsProps {
  onFullscreen?: () => void
  showFullscreenButton?: boolean
  className?: string
}

export function MapControls({ 
  onFullscreen, 
  showFullscreenButton = false, 
  className 
}: MapControlsProps) {
  if (!showFullscreenButton || !onFullscreen) {
    return null
  }

  return (
    <div className={cn("absolute top-4 right-4 z-10", className)}>
      <Button
        onClick={onFullscreen}
        variant="outline"
        size="sm"
        className="bg-white/90 backdrop-blur-sm hover:bg-white shadow-lg"
        aria-label="View fullscreen"
      >
        <Maximize2 className="w-4 h-4" />
      </Button>
    </div>
  )
}
