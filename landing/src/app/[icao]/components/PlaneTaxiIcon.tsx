import React from 'react'

interface PlaneTaxiIconProps {
  className?: string
  size?: number
}

export function PlaneTaxiIcon({ className, size = 24 }: PlaneTaxiIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      {/* Aircraft body */}
      <path d="M8 12 L16 12" />
      <path d="M12 8 L12 16" />
      
      {/* Wings */}
      <path d="M6 10 L18 10" />
      <path d="M6 14 L18 14" />
      
      {/* Taxi path - curved ground path */}
      <path d="M4 20 Q8 18 12 20 T20 20" strokeDasharray="2,2" />
      
      {/* Direction arrows along taxi path */}
      <path d="M7 19 L9 20 L7 21" />
      <path d="M15 19 L17 20 L15 21" />
    </svg>
  )
}
