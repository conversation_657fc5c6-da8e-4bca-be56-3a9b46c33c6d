import React from 'react'

interface PlanePatternIconProps {
  className?: string
  size?: number
}

export function PlanePatternIcon({ className, size = 24 }: PlanePatternIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      {/* Pattern path - rectangular flight pattern */}
      <path d="M20 20 L22 20 L22 4 L4 4 L4 20 L8 20" />
      
      {/* Aircraft symbol in the pattern */}
      <path d="M10 12 L14 12" />
      <path d="M12 10 L12 14" />
      <path d="M9 11 L15 11" />
      <path d="M9 13 L15 13" />
      
      {/* Direction arrow */}
      <path d="M18 18 L20 20 L18 22" />
    </svg>
  )
}
