"use client"

import React, { useMemo } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { Card } from '@/components/ui/card'
import { Plane, Clock } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import type { FlightOperation } from '../types'

interface RecentlySeenAircraftProps {
  operations: FlightOperation[]
}

interface AircraftSummary {
  registration: string
  hex: string
  aircraftType: string
  lastSeen: string
  operationCount: number
  lastOperationType: string
}

export function RecentlySeenAircraft({ operations }: RecentlySeenAircraftProps) {
  const router = useRouter()
  const params = useParams()
  const icao = params.icao as string

  // Process operations to get unique aircraft with their last seen time and operation count
  const aircraftSummaries = useMemo(() => {
    const aircraftMap = new Map<string, AircraftSummary>()

    operations.forEach(operation => {
      const existing = aircraftMap.get(operation.registration)
      
      if (!existing) {
        aircraftMap.set(operation.registration, {
          registration: operation.registration,
          hex: operation.hex,
          aircraftType: operation.aircraftType,
          lastSeen: operation.time,
          operationCount: 1,
          lastOperationType: operation.operationType
        })
      } else {
        // Update if this operation is more recent
        if (new Date(operation.time) > new Date(existing.lastSeen)) {
          existing.lastSeen = operation.time
          existing.lastOperationType = operation.operationType
        }
        existing.operationCount++
      }
    })

    // Convert to array and sort by last seen (most recent first)
    return Array.from(aircraftMap.values())
      .sort((a, b) => new Date(b.lastSeen).getTime() - new Date(a.lastSeen).getTime())
      .slice(0, 12) // Show top 12 aircraft
  }, [operations])

  const getOperationTypeColor = (operationType: string) => {
    switch (operationType) {
      case 'landing': return 'text-blue-600 bg-blue-50'
      case 'takeoff': return 'text-green-600 bg-green-50'
      case 'pattern': return 'text-orange-600 bg-orange-50'
      case 'taxi': return 'text-purple-600 bg-purple-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const handleAircraftClick = (registration: string) => {
    // Navigate to operations page with aircraft filter
    router.push(`/${icao}/operations?aircraft=${encodeURIComponent(registration)}`)
  }

  return (
    <section>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 mb-4 sm:mb-6">
        <h2 className="text-xl sm:text-2xl font-bold text-gray-900">Recently Seen Aircraft</h2>
        <div className="text-sm text-gray-600">
          {aircraftSummaries.length} aircraft • Scroll to see more →
        </div>
      </div>

      {aircraftSummaries.length === 0 ? (
        <Card className="p-8 text-center">
          <Plane className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No aircraft data available</p>
        </Card>
      ) : (
        <div className="relative">
          {/* Horizontal scrolling container */}
          <div className="flex gap-4 overflow-x-auto pb-4 scrollbar-hide snap-x snap-mandatory px-1" style={{ scrollPaddingLeft: '1rem' }}>
            {aircraftSummaries.map((aircraft) => (
              <Card
                key={aircraft.registration}
                className="flex-none w-72 p-4 hover:shadow-md transition-all duration-200 cursor-pointer snap-start hover:scale-105"
                onClick={() => handleAircraftClick(aircraft.registration)}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Plane className="w-5 h-5 text-gray-600" />
                    <h3 className="font-semibold text-gray-900">{aircraft.registration}</h3>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getOperationTypeColor(aircraft.lastOperationType)}`}>
                    {aircraft.lastOperationType}
                  </span>
                </div>

                <div className="space-y-2 text-sm text-gray-600">
                  {aircraft.aircraftType && (
                    <div>
                      <span className="font-medium">Type:</span> {aircraft.aircraftType}
                    </div>
                  )}

                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    <span>
                      {formatDistanceToNow(new Date(aircraft.lastSeen), { addSuffix: true })}
                    </span>
                  </div>

                  <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                    <span className="text-xs text-gray-500">
                      {aircraft.operationCount} operation{aircraft.operationCount !== 1 ? 's' : ''}
                    </span>
                    <span className="text-xs text-gray-400 font-mono">
                      {aircraft.hex.toUpperCase()}
                    </span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}
    </section>
  )
}
