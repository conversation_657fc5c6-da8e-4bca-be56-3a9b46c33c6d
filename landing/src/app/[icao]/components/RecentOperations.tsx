"use client"

import React, { useMemo } from 'react'
import Link from 'next/link'
import { useRouter, useParams } from 'next/navigation'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowRight, Activity, Clock, Plane, Navigation, PlaneLanding, PlaneTakeoff } from 'lucide-react'
import { PlanePatternIcon } from './PlanePatternIcon'
import { PlaneTaxiIcon } from './PlaneTaxiIcon'
import { formatOperationDateTime } from '../utils/dateUtils'
import { cn } from '@/lib/utils'
import type { FlightOperation } from '../types'

interface RecentOperationsProps {
  operations: FlightOperation[]
  timezone?: string
}

export function RecentOperations({ operations, timezone }: RecentOperationsProps) {
  const router = useRouter()
  const params = useParams()
  const icao = params.icao as string

  // Get the 3 most recent operations
  const recentOperations = useMemo(() => {
    return operations
      .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime())
      .slice(0, 3)
  }, [operations])

  const handleOperationClick = (operation: FlightOperation) => {
    // Navigate to operations page with operation selected
    router.push(`/${icao}/operations?operation=${encodeURIComponent(operation.id)}`)
  }

  return (
    <section>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0 mb-4 sm:mb-6">
        <h2 className="text-xl sm:text-2xl font-bold text-gray-900">Recent Operations</h2>
        <Link href={`/${icao}/operations`}>
          <Button variant="outline" className="flex items-center gap-2 w-full sm:w-auto">
            See All
            <ArrowRight className="w-4 h-4" />
          </Button>
        </Link>
      </div>

      {recentOperations.length === 0 ? (
        <Card className="p-8 text-center">
          <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No recent operations</p>
        </Card>
      ) : (
        <div className="space-y-3 sm:space-y-4">
          {recentOperations.map((operation) => (
            <div key={operation.id}>
              <Card
                className="border-2 border-gray-200 bg-white p-4 cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleOperationClick(operation)}
              >
                <div className="flex items-center gap-4">
                  {/* Operation Type Icon */}
                  <div className="flex-shrink-0">
                    <div className={cn(
                      "w-10 h-10 rounded-lg flex items-center justify-center border-2",
                      operation.operationType === 'landing' ? 'bg-blue-50 border-blue-200' :
                      operation.operationType === 'takeoff' ? 'bg-green-50 border-green-200' :
                      operation.operationType === 'pattern' ? 'bg-orange-50 border-orange-200' :
                      operation.operationType === 'taxi' ? 'bg-purple-50 border-purple-200' :
                      'bg-gray-50 border-gray-200'
                    )}>
                      {operation.operationType === 'landing' ? <PlaneLanding className="w-5 h-5 text-blue-600" /> :
                       operation.operationType === 'takeoff' ? <PlaneTakeoff className="w-5 h-5 text-green-600" /> :
                       operation.operationType === 'pattern' ? <PlanePatternIcon className="w-5 h-5 text-orange-600" /> :
                       operation.operationType === 'taxi' ? <PlaneTaxiIcon className="w-5 h-5 text-purple-600" /> :
                       <Plane className="w-5 h-5 text-gray-600" />}
                    </div>
                  </div>

                  {/* Operation Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2 mb-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-semibold text-gray-900">{operation.registration}</h4>
                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 capitalize">
                          {operation.operationType}
                        </span>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-gray-600">
                        <Clock className="w-3 h-3" />
                        <span>{formatOperationDateTime(operation.time, timezone)}</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 text-xs text-gray-600">
                      <div className="flex items-center gap-1">
                        <Plane className="w-3 h-3" />
                        <span>{operation.aircraftType}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Navigation className="w-3 h-3" />
                        <span>Runway {operation.runway}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          ))}
        </div>
      )}

      {operations.length > 3 && (
        <div className="mt-4 sm:mt-6 text-center">
          <Link href={`/${icao}/operations`}>
            <Button className="flex items-center gap-2 w-full sm:w-auto">
              View All {operations.length} Operations
              <ArrowRight className="w-4 h-4" />
            </Button>
          </Link>
        </div>
      )}
    </section>
  )
}
