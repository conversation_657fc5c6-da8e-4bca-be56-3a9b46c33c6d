"use client"

import React, { useRef, useEffect, useState } from 'react'
import { Maximize2, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { InteractiveMap } from './InteractiveMap'
import { OperationsDrawer, type OperationsDrawerRef } from './OperationsDrawer'
import { OperationsSidebar } from './OperationsSidebar'
import { OperationsList } from './OperationsList'
import { SelectionHeader } from './SelectionHeader'
import { EmptyState } from './EmptyState'
import { FlightTimeline } from './FlightTimeline'
import { DateSelector } from './DateSelector'
import { AircraftSelector } from './AircraftSelector'

import { useResponsive } from '../hooks/useResponsive'

import type { FlightOperation, SelectedOperation, DateRange, AircraftSelection } from '../types'

interface ResponsiveLayoutProps {
  operations: FlightOperation[] // Filtered operations for display
  allOperations: FlightOperation[] // All operations for aircraft selector
  selectedOperation: SelectedOperation
  onOperationSelect: (operation: FlightOperation) => void
  onOperationDeselect?: () => void
  selectedDateRange: DateRange
  onDateRangeChange: (range: DateRange) => void
  selectedAircraft: AircraftSelection
  onAircraftSelectionChange: (selection: AircraftSelection) => void
  airportLocation?: {
    lat: number
    lng: number
    name?: string
    icao?: string
  } | null
  timezone?: string
}

export function ResponsiveLayout({
  operations,
  allOperations,
  selectedOperation,
  onOperationSelect,
  onOperationDeselect,
  selectedDateRange,
  onDateRangeChange,
  selectedAircraft,
  onAircraftSelectionChange,
  airportLocation,
  timezone
}: ResponsiveLayoutProps) {
  const { isMobile, isDesktop, width } = useResponsive()
  const drawerContentRef = useRef<HTMLDivElement>(null)
  const drawerRef = useRef<OperationsDrawerRef>(null)

  const [mapKey, setMapKey] = useState(0) // Force map re-render on layout changes
  const [isFullscreen, setIsFullscreen] = useState(false) // Mobile fullscreen mode

  // Force map re-render when responsive breakpoint changes
  useEffect(() => {
    setMapKey(prev => prev + 1)
  }, [width])

  // Reset drawer position and scroll to top when operation is selected on mobile
  useEffect(() => {
    if (isMobile && selectedOperation && drawerRef.current) {
      // Small delay to ensure the drawer content is rendered
      setTimeout(() => {
        drawerRef.current?.resetPosition()
      }, 100)
    }
  }, [selectedOperation, isMobile])

  const handleOperationSelect = (operation: FlightOperation) => {
    onOperationSelect(operation)
  }

  const handleDeselect = () => {
    if (onOperationDeselect) {
      onOperationDeselect()
    } else {
      // Fallback: select the first operation if available
      if (operations.length > 0) {
        onOperationSelect(operations[0])
      }
    }
  }

  const handleEnterFullscreen = () => {
    setIsFullscreen(true)
    setMapKey(prev => prev + 1) // Force map re-render for new layout
  }

  const handleExitFullscreen = () => {
    setIsFullscreen(false)
    setMapKey(prev => prev + 1) // Force map re-render for new layout
  }



  // Mobile/Tablet Layout
  if (isMobile || !isDesktop) {
    // Fullscreen mobile layout (similar to desktop)
    if (isFullscreen) {
      return (
        <div className="h-screen w-full bg-gray-100 flex flex-col">
          {/* Map Area - Takes most of the height */}
          <div className="flex-1 relative">
            <InteractiveMap
              key={`mobile-fullscreen-${mapKey}`}
              className="absolute inset-0"
              selectedOperation={selectedOperation}
              forceResize={true}
              showZoomControls={false} // Remove zoom controls on mobile
              airportLocation={airportLocation}
            />

            {/* Back Button */}
            <div className="absolute top-4 left-4 z-10">
              <Button
                variant="secondary"
                size="sm"
                onClick={handleExitFullscreen}
                className="bg-white/90 hover:bg-white shadow-lg"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
            </div>

            {!selectedOperation && (
              <div className="absolute inset-0 bg-white/90 flex items-center justify-center">
                <EmptyState
                  type="no-selection"
                  title="Select a Flight"
                  description="Go back to select a flight operation to view its route on the map"
                />
              </div>
            )}
          </div>

          {/* Flight Timeline - Bottom section */}
          <FlightTimeline
            selectedOperation={selectedOperation}
            className="flex-shrink-0 h-64"
          />
        </div>
      )
    }

    // Regular mobile layout with drawer
    return (
      <div className="h-screen w-full bg-gray-100 relative overflow-hidden">
        {/* Map - Upper 40% (drawer starts at 60%) */}
        <div className="absolute top-0 left-0 right-0 z-0 h-[40vh]">
          <InteractiveMap
            key={`mobile-${mapKey}`}
            className="absolute inset-0"
            selectedOperation={selectedOperation}
            showZoomControls={false} // Remove zoom controls on mobile
            airportLocation={airportLocation}
          />

          {/* Fullscreen Button - Only show when flight is selected */}
          {selectedOperation && (
            <div className="absolute top-4 right-4 z-10">
              <Button
                variant="secondary"
                size="sm"
                onClick={handleEnterFullscreen}
                className="bg-white/90 hover:bg-white shadow-lg"
              >
                <Maximize2 className="w-4 h-4" />
              </Button>
            </div>
          )}

          {!selectedOperation && (
            <div className="absolute inset-0 bg-white/90 flex items-center justify-center">
              <EmptyState
                type="no-selection"
                title="Select a Flight"
                description="Choose a flight operation below to view its route on the map"
              />
            </div>
          )}
        </div>

        {/* Operations Drawer - Lower 50% initially, expandable to full screen */}
        <OperationsDrawer ref={drawerRef} contentRef={drawerContentRef}>
          {/* Selection Header - shows selected flight at top (only if something is selected) */}
          {selectedOperation && (
            <SelectionHeader
              operation={selectedOperation}
              onDeselect={handleDeselect}
              className="mb-6"
              timezone={timezone}
            />
          )}

          {/* Filter controls */}
          <div className="flex gap-3 mb-6">
            <DateSelector
              selectedRange={selectedDateRange}
              onDateRangeChange={onDateRangeChange}
              className="flex-1"
              timezone={timezone}
            />
            <AircraftSelector
              operations={allOperations}
              selectedAircraft={selectedAircraft}
              onAircraftSelectionChange={onAircraftSelectionChange}
              className="flex-1"
            />
          </div>

          {/* Flight Operations */}
          <OperationsList
            operations={operations}
            selectedOperation={selectedOperation}
            onOperationSelect={handleOperationSelect}
            showSelectedAsDisabled={true}
          />
        </OperationsDrawer>
      </div>
    )
  }

  // Desktop Layout
  return (
    <div className="h-screen w-full bg-gray-100 flex overflow-hidden">
      {/* Operations Sidebar - Left side */}
      <OperationsSidebar
        operations={operations}
        allOperations={allOperations}
        selectedOperation={selectedOperation}
        onOperationSelect={onOperationSelect}
        selectedDateRange={selectedDateRange}
        onDateRangeChange={onDateRangeChange}
        selectedAircraft={selectedAircraft}
        onAircraftSelectionChange={onAircraftSelectionChange}
        className="w-96 flex-shrink-0"
      />

      {/* Map and Timeline - Right side */}
      <div className="flex-1 flex flex-col">
        {/* Map Area */}
        <div className="flex-1 relative">
          <InteractiveMap
            key={`desktop-${mapKey}`}
            className="absolute inset-0"
            selectedOperation={selectedOperation}
            forceResize={true}
            airportLocation={airportLocation}
          />
          {!selectedOperation && (
            <div className="absolute inset-0 bg-white/90 flex items-center justify-center">
              <EmptyState
                type="no-selection"
                title="Select a Flight"
                description="Choose a flight operation from the sidebar to view its route on the map"
              />
            </div>
          )}
        </div>

        {/* Timeline Component - Always visible on desktop */}
        <FlightTimeline
          selectedOperation={selectedOperation}
          className="flex-shrink-0 h-64"
        />
      </div>
    </div>
  )
}
