"use client"

import React, { useState } from 'react'
import Image from 'next/image'
import { Card } from '@/components/ui/card'
import { MapPin, Camera } from 'lucide-react'
import type { Airport } from '@/types/api'

interface AirportHeaderProps {
  airport: Airport | null
}

export function AirportHeader({ airport }: AirportHeaderProps) {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  const hasImage = airport?.image_url && !imageError

  const handleImageLoad = () => {
    setImageLoading(false)
  }

  const handleImageError = () => {
    setImageError(true)
    setImageLoading(false)
  }

  return (
    <div className="relative">
      {/* Airport Picture or Placeholder */}
      <div className="h-40 sm:h-48 md:h-64 bg-gradient-to-r from-blue-500 to-blue-600 relative overflow-hidden">
        {/* Airport Image */}
        {hasImage && airport.image_url && (
          <>
            <Image
              src={airport.image_url}
              alt={`${airport.name || airport.icao} Airport`}
              fill
              className={`object-cover transition-opacity duration-300 ${
                imageLoading ? 'opacity-0' : 'opacity-100'
              }`}
              onLoad={handleImageLoad}
              onError={handleImageError}
              sizes="100vw"
              priority
            />
            {/* Loading state for image */}
            {imageLoading && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                  <p className="text-sm opacity-80">Loading image...</p>
                </div>
              </div>
            )}
          </>
        )}

        {/* Placeholder content (shown when no image or error) */}
        {!hasImage && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-white">
              <Camera className="w-12 h-12 mx-auto mb-2 opacity-60" />
              <p className="text-lg font-medium opacity-80">Airport Photo</p>
              <p className="text-sm opacity-60">Coming Soon</p>
            </div>
          </div>
        )}

        {/* Overlay gradient - always present for text readability */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
      </div>

      {/* Airport Info Overlay */}
      <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6">
        <div className="max-w-7xl mx-auto">
          <Card className="bg-white/95 backdrop-blur-sm border-0 shadow-lg p-3 sm:p-4 md:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
              <div>
                <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-1 sm:mb-2">
                  {airport?.name || 'Loading...'}
                </h1>
                <div className="flex items-center gap-2 text-gray-600">
                  <MapPin className="w-4 h-4" />
                  <span className="font-medium">{airport?.icao || '----'}</span>
                  {airport?.timezone && (
                    <span className="text-sm hidden sm:inline">• {airport.timezone}</span>
                  )}
                </div>
              </div>

              {/* Quick Stats - can be expanded later */}
              <div className="flex gap-4 text-sm">
                <div className="text-center">
                  <div className="font-semibold text-gray-900">Live</div>
                  <div className="text-gray-600">Operations</div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
