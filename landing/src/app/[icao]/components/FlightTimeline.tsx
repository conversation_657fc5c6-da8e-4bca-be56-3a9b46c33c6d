"use client"

import React from 'react'
import { cn } from '@/lib/utils'
import { TrendingUp, Clock } from 'lucide-react'
import type { FlightOperation } from '../types'

interface FlightTimelineProps {
  selectedOperation?: FlightOperation | null
  className?: string
}

export function FlightTimeline({ selectedOperation, className }: FlightTimelineProps) {
  if (!selectedOperation) {
    return (
      <div className={cn("bg-white border-t border-gray-200 p-6 h-full", className)}>
        <div className="flex items-center justify-center h-full text-gray-500">
          <div className="text-center">
            <Clock className="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm">Select a flight to view timeline data</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("bg-white border-t border-gray-200 p-6 h-full flex flex-col", className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Flight Timeline</h3>
          <p className="text-sm text-gray-600">
            {selectedOperation.registration} • {selectedOperation.operationType}
          </p>
        </div>
      </div>

      {/* Coming Soon Message */}
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <TrendingUp className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">Timeline Coming Soon</h4>
          <p className="text-sm text-gray-600 max-w-sm">
            Detailed flight timeline with altitude, speed, and phase data will be available in a future update.
          </p>
        </div>
      </div>
    </div>
  )
}
