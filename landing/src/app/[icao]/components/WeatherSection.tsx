"use client"

import React from 'react'
import { Card } from '@/components/ui/card'
import { Cloud, Wind, Eye, Thermometer, Gauge } from 'lucide-react'

export function WeatherSection() {
  return (
    <section>
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Cloud className="w-6 h-6 text-blue-600" />
          <h3 className="text-xl font-semibold text-gray-900">Weather Information</h3>
        </div>

        <p className="text-gray-600 mb-6">
          Current weather conditions and forecasts for airport operations.
        </p>

        {/* Weather Data Grid */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <Thermometer className="w-4 h-4 text-red-500" />
            <div>
              <div className="text-sm font-medium text-gray-900">Temperature</div>
              <div className="text-xs text-gray-600">-- °F</div>
            </div>
          </div>

          <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <Wind className="w-4 h-4 text-blue-500" />
            <div>
              <div className="text-sm font-medium text-gray-900">Wind</div>
              <div className="text-xs text-gray-600">-- kt</div>
            </div>
          </div>

          <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <Eye className="w-4 h-4 text-green-500" />
            <div>
              <div className="text-sm font-medium text-gray-900">Visibility</div>
              <div className="text-xs text-gray-600">-- SM</div>
            </div>
          </div>

          <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <Gauge className="w-4 h-4 text-purple-500" />
            <div>
              <div className="text-sm font-medium text-gray-900">Pressure</div>
              <div className="text-xs text-gray-600">-- inHg</div>
            </div>
          </div>
        </div>

        {/* METAR placeholder */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="text-sm font-medium text-gray-900 mb-2">METAR</div>
          <div className="text-xs text-gray-600 font-mono">
            Weather data integration coming soon...
          </div>
        </div>

        <p className="text-xs text-gray-500 mt-4 text-center">
          Weather integration coming soon
        </p>
      </Card>
    </section>
  )
}
