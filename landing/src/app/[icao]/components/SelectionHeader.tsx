"use client"

import React from 'react'
import { Card } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { X, Plane, Clock, Navigation, PlaneLanding, PlaneTakeoff } from 'lucide-react'
import { PlanePatternIcon } from './PlanePatternIcon'
import { PlaneTaxiIcon } from './PlaneTaxiIcon'
import { formatOperationDateTime } from '../utils/dateUtils'
import type { FlightOperation } from '../types'

interface SelectionHeaderProps {
  operation: FlightOperation
  onDeselect: () => void
  className?: string
  timezone?: string
}

export function SelectionHeader({ operation, onDeselect, className, timezone }: SelectionHeaderProps) {
  const getOperationIcon = () => {
    switch (operation.operationType) {
      case 'landing':
        return <PlaneLanding className="w-5 h-5 text-blue-600" />
      case 'takeoff':
        return <PlaneTakeoff className="w-5 h-5 text-green-600" />
      case 'pattern':
        return <PlanePatternIcon className="w-5 h-5 text-orange-600" />
      case 'taxi':
        return <PlaneTaxiIcon className="w-5 h-5 text-purple-600" />
      default:
        return <Plane className="w-5 h-5 text-gray-600" />
    }
  }

  const getOperationColor = () => {
    switch (operation.operationType) {
      case 'landing': return 'bg-blue-50 border-blue-200'
      case 'takeoff': return 'bg-green-50 border-green-200'
      case 'pattern': return 'bg-orange-50 border-orange-200'
      case 'taxi': return 'bg-purple-50 border-purple-200'
      default: return 'bg-gray-50 border-gray-200'
    }
  }

  return (
    <div className={cn("mb-4", className)}>
      <h3 className="text-sm font-medium text-gray-600 mb-2">Selected Operation</h3>
      <Card className="border-2 border-blue-500 bg-blue-50 p-4 relative">
        {/* Deselect button */}
        <button
          onClick={onDeselect}
          className="absolute top-3 right-3 p-1 rounded-full hover:bg-blue-100 transition-colors"
          aria-label="Deselect operation"
        >
          <X className="w-4 h-4 text-gray-600" />
        </button>

        <div className="flex items-center gap-4 pr-8">
          {/* Operation Type Icon */}
          <div className="flex-shrink-0">
            <div className={cn(
              "w-10 h-10 rounded-lg flex items-center justify-center border-2",
              getOperationColor()
            )}>
              {getOperationIcon()}
            </div>
          </div>

          {/* Operation Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-semibold text-gray-900">{operation.registration}</h4>
              <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 capitalize">
                {operation.operationType}
              </span>
            </div>

            <div className="flex items-center gap-4 text-xs text-gray-600">
              <div className="flex items-center gap-1">
                <Plane className="w-3 h-3" />
                <span>{operation.aircraftType}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                <span>{formatOperationDateTime(operation.time, timezone)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Navigation className="w-3 h-3" />
                <span>Runway {operation.runway}</span>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}
