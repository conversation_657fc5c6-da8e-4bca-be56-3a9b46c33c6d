import React from 'react'
import { OperationCard, SectionHeader } from './OperationCard'
import { EmptyState } from './EmptyState'
import { groupOperationsByDate } from '../utils/dateUtils'
import type { FlightOperation, SelectedOperation } from '../types'

interface OperationsListProps {
  operations: FlightOperation[]
  selectedOperation: SelectedOperation
  onOperationSelect: (operation: FlightOperation) => void
  showSelectedAsDisabled?: boolean // For mobile where selected items are grayed out
}

export function OperationsList({ 
  operations, 
  selectedOperation, 
  onOperationSelect,
  showSelectedAsDisabled = false
}: OperationsListProps) {
  if (operations.length === 0) {
    return <EmptyState type="no-results" />
  }

  const groupedOperations = groupOperationsByDate(operations)

  return (
    <div className="space-y-4">
      {groupedOperations.map((group) => (
        <div key={group.label}>
          <SectionHeader title={group.label} />
          <div className="space-y-3">
            {group.operations.map((operation) => (
              <div
                key={operation.id}
                className={
                  showSelectedAsDisabled && selectedOperation?.id === operation.id
                    ? 'opacity-50 pointer-events-none'
                    : ''
                }
              >
                <OperationCard
                  operation={{
                    ...operation,
                    isSelected: !showSelectedAsDisabled && selectedOperation?.id === operation.id
                  }}
                  onSelect={onOperationSelect}
                />
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}
