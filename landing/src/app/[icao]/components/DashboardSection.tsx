"use client"

import React from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ExternalLink, BarChart3, TrendingUp, Users } from 'lucide-react'

export function DashboardSection() {
  return (
    <section>
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <BarChart3 className="w-6 h-6 text-blue-600" />
          <h3 className="text-xl font-semibold text-gray-900">Analytics Dashboard</h3>
        </div>

        <p className="text-gray-600 mb-6">
          Access detailed analytics and insights about airport operations, traffic patterns, and performance metrics.
        </p>

        {/* Quick Stats Preview */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <TrendingUp className="w-5 h-5 text-green-600 mx-auto mb-1" />
            <div className="text-sm font-medium text-gray-900">Daily Ops</div>
            <div className="text-xs text-gray-600">Coming Soon</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <Users className="w-5 h-5 text-blue-600 mx-auto mb-1" />
            <div className="text-sm font-medium text-gray-900">Peak Hours</div>
            <div className="text-xs text-gray-600">Coming Soon</div>
          </div>
        </div>

        <Button 
          className="w-full flex items-center justify-center gap-2" 
          variant="outline"
          disabled
        >
          Open Dashboard
          <ExternalLink className="w-4 h-4" />
        </Button>

        <p className="text-xs text-gray-500 mt-2 text-center">
          Dashboard integration coming soon
        </p>
      </Card>
    </section>
  )
}
