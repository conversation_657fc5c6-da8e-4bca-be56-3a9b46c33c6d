"use client"

import React, { useEffect, useRef, useState } from 'react'
import { cn } from '@/lib/utils'
import type { SelectedOperation } from '../types'

interface InteractiveMapProps {
  className?: string
  style?: React.CSSProperties
  selectedOperation?: SelectedOperation
  forceResize?: boolean // Trigger map resize when container changes
  showZoomControls?: boolean // Show/hide zoom controls
  airportLocation?: {
    lat: number
    lng: number
    name?: string
    icao?: string
  } | null // Airport location for map center
}

// Loading placeholder component
function MapLoadingPlaceholder({ className }: { className?: string }) {
  return (
    <div
      className={cn(
        "relative bg-gradient-to-br from-blue-100 to-green-100 overflow-hidden flex items-center justify-center",
        className
      )}
    >
      <div className="text-gray-600 text-lg font-medium">Loading interactive map...</div>
    </div>
  )
}

export function InteractiveMap({ className, style, selectedOperation, forceResize, showZoomControls = true, airportLocation }: InteractiveMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const mapInstanceRef = useRef<any>(null)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const currentLayersRef = useRef<any[]>([])
  const [isLoaded, setIsLoaded] = useState(false)
  const [loadError, setLoadError] = useState(false)

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined' || !mapRef.current || mapInstanceRef.current) return

    let mounted = true

    // Dynamic import to avoid SSR issues
    const loadMap = async () => {
      try {
        // Import Leaflet dynamically
        const L = await import('leaflet')

        // Import CSS - we'll handle this in the global CSS instead
        // await import('leaflet/dist/leaflet.css')

        // Check if component is still mounted
        if (!mounted || !mapRef.current) return

        // Determine map center - use airport location if available, otherwise default
        const defaultCenter: [number, number] = [37.6213, -122.3790] // San Francisco Airport fallback
        const mapCenter: [number, number] = airportLocation
          ? [airportLocation.lat, airportLocation.lng]
          : defaultCenter

        // Initialize the map
        const map = L.map(mapRef.current, {
          center: mapCenter,
          zoom: 14,
          zoomControl: showZoomControls,
          scrollWheelZoom: true,
          doubleClickZoom: true,
          dragging: true,
          touchZoom: true,
          boxZoom: true,
          keyboard: true,
        })

        // Add CARTO light all tile layer (slightly darker for better contrast)
        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
          attribution: '© OpenStreetMap contributors, © CARTO',
          maxZoom: 19,
          subdomains: 'abcd'
        }).addTo(map)

        // Clear any existing flight path layers
        currentLayersRef.current.forEach(layer => {
          if (map.hasLayer(layer)) {
            map.removeLayer(layer)
          }
        })
        currentLayersRef.current = []

        // Custom icon function removed since markers are no longer used

        // Airport marker removed for cleaner map display

        // Show selected operation's flight path if provided
        if (selectedOperation?.flightPath) {
          const { coordinates, color, name, segments } = selectedOperation.flightPath

          if (segments && segments.length > 0) {
            // Render altitude-based segments
            segments.forEach((segment, index) => {
              const polyline = L.polyline(segment.coordinates, {
                color: segment.color,
                weight: 2, // Thinner line
                opacity: 0.9,
                dashArray: segment.isGround ? '5, 5' : undefined // Dashed for ground segments
              }).addTo(map)

              // Track this layer for cleanup
              currentLayersRef.current.push(polyline)

              if (index === 0) {
                polyline.bindPopup(`${name} (${segment.isGround ? 'Ground' : 'Airborne'})`)
              }
            })
          } else if (coordinates.length >= 2) {
            // Fallback to single polyline for operations without segments (only if we have coordinates)
            const selectedPolyline = L.polyline(coordinates as [number, number][], {
              color: color,
              weight: 2, // Thinner line
              opacity: 0.9,
              dashArray: coordinates[0][0] > coordinates[1][0] ? '5, 5' : '10, 5' // arrival vs departure
            }).addTo(map)

            // Track this layer for cleanup
            currentLayersRef.current.push(selectedPolyline)

            selectedPolyline.bindPopup(`${name} (Selected)`)
          }

          // Origin/destination markers removed for cleaner map display

          // Only change map view if we have coordinates to avoid unnecessary zooming
          if (coordinates.length >= 2) {
            if (selectedOperation.operationType === 'pattern') {
              // For pattern operations, fit the entire flight path
              const bounds = L.latLngBounds(coordinates as [number, number][])
              map.fitBounds(bounds, { padding: [50, 50] })
            } else {
              // For takeoff/landing operations, center on airport with fixed zoom
              map.setView(mapCenter, 14)
            }
          }
          // If no coordinates, don't change the map view to avoid unnecessary zooming
        }

        // Store map instance
        mapInstanceRef.current = map

        if (mounted) {
          setIsLoaded(true)
        }

      } catch (error) {
        console.error('Failed to load map:', error)
        if (mounted) {
          setLoadError(true)
        }
      }
    }

    loadMap()

    return () => {
      mounted = false
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
      }
    }
  }, [selectedOperation, showZoomControls, airportLocation])

  // Handle map resize when container changes (e.g., fullscreen mode)
  useEffect(() => {
    if (mapInstanceRef.current && forceResize) {
      // Small delay to ensure container has resized
      setTimeout(() => {
        if (mapInstanceRef.current) {
          mapInstanceRef.current.invalidateSize()
        }
      }, 100)
    }
  }, [forceResize])

  if (loadError) {
    return (
      <div
        className={cn(
          "relative bg-red-50 overflow-hidden flex items-center justify-center",
          className
        )}
      >
        <div className="text-red-600 text-center">
          <div className="font-medium">Failed to load map</div>
          <div className="text-sm">Please refresh the page</div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("relative z-0", className)} style={style}>
      {!isLoaded && <MapLoadingPlaceholder className="absolute inset-0" />}
      <div
        ref={mapRef}
        className="w-full h-full z-0"
        style={{
          minHeight: '200px',
          opacity: isLoaded ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out',
          zIndex: 0
        }}
      />
    </div>
  )
}
