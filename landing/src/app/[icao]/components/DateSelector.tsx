"use client"

import React, { useState } from 'react'
import { format, subDays, startOfDay, endOfDay } from 'date-fns'
import { Calendar as CalendarIcon, Clock } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { toAirportTime } from '../utils/dateUtils'
import type { DateRange, DatePreset } from '../types'

interface DateSelectorProps {
  selectedRange: DateRange
  onDateRangeChange: (range: DateRange) => void
  className?: string
  timezone?: string
}

export function DateSelector({ selectedRange, onDateRangeChange, className, timezone }: DateSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [tempRange, setTempRange] = useState<DateRange>(selectedRange)

  // Predefined date ranges (all in airport timezone)
  const airportToday = toAirportTime(new Date(), timezone)
  const datePresets: Array<{ key: DatePreset; label: string; range: DateRange }> = [
    {
      key: 'today',
      label: 'Today',
      range: {
        from: startOfDay(airportToday),
        to: endOfDay(airportToday),
        label: 'Today'
      }
    },
    {
      key: 'yesterday',
      label: 'Yesterday',
      range: {
        from: startOfDay(subDays(airportToday, 1)),
        to: endOfDay(subDays(airportToday, 1)),
        label: 'Yesterday'
      }
    },
    {
      key: 'last7days',
      label: 'Last 7 days',
      range: {
        from: startOfDay(subDays(airportToday, 6)),
        to: endOfDay(airportToday),
        label: 'Last 7 days'
      }
    },
    {
      key: 'last30days',
      label: 'Last 30 days',
      range: {
        from: startOfDay(subDays(airportToday, 29)),
        to: endOfDay(airportToday),
        label: 'Last 30 days'
      }
    }
  ]

  const handlePresetSelect = (preset: DateRange) => {
    setTempRange(preset)
  }

  const handleCustomDateSelect = (date: Date | undefined) => {
    if (!date) return

    const customRange: DateRange = {
      from: startOfDay(date),
      to: endOfDay(date),
      label: format(date, 'MMM d, yyyy')
    }
    setTempRange(customRange)
  }

  const handleDone = () => {
    onDateRangeChange(tempRange)
    setIsOpen(false)
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className={cn("flex-1 justify-start gap-2", className)}>
          <Clock className="w-4 h-4" />
          <span>{selectedRange.label}</span>
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg">
            <CalendarIcon className="w-5 h-5" />
            Select Date Range
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-2">
          {/* Selected Range Display - Moved to top */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <CalendarIcon className="w-4 h-4 text-blue-600" />
                <p className="text-sm font-semibold text-blue-900">Selected Range</p>
              </div>
              <Button onClick={handleDone} size="sm" className="h-8 px-3">
                Apply
              </Button>
            </div>
            <p className="font-bold text-blue-900 text-lg mb-1">{tempRange.label}</p>
            <p className="text-sm text-blue-700">
              {format(tempRange.from, 'MMM d, yyyy')} - {format(tempRange.to, 'MMM d, yyyy')}
            </p>
          </div>

          {/* Preset Options */}
          <div>
            <h4 className="text-sm font-semibold text-gray-900 mb-4">Quick Select</h4>
            <div className="grid grid-cols-2 gap-3">
              {datePresets.map((preset) => (
                <Button
                  key={preset.key}
                  variant={tempRange.label === preset.range.label ? "default" : "outline"}
                  size="default"
                  onClick={() => handlePresetSelect(preset.range)}
                  className="justify-start h-11 font-medium"
                >
                  {preset.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Custom Date Picker */}
          <div>
            <h4 className="text-sm font-semibold text-gray-900 mb-4">Custom Date</h4>
            <div className="flex justify-center">
              <Calendar
                mode="single"
                selected={tempRange.from}
                onSelect={handleCustomDateSelect}
                disabled={(date) => date > airportToday || date < subDays(airportToday, 365)}
                initialFocus
                className="rounded-lg border border-gray-200 shadow-sm"
              />
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
