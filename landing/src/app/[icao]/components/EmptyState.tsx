"use client"

import React from 'react'
import { cn } from '@/lib/utils'
import { Plane, Search, MapPin } from 'lucide-react'

interface EmptyStateProps {
  type: 'no-selection' | 'no-results' | 'loading'
  title?: string
  description?: string
  className?: string
}

export function EmptyState({ 
  type, 
  title, 
  description, 
  className 
}: EmptyStateProps) {
  const getDefaultContent = () => {
    switch (type) {
      case 'no-selection':
        return {
          icon: <MapPin className="w-12 h-12 text-gray-400" />,
          title: title || 'No Flight Selected',
          description: description || 'Select a flight operation to view its route on the map and see detailed information.'
        }
      case 'no-results':
        return {
          icon: <Search className="w-12 h-12 text-gray-400" />,
          title: title || 'No Operations Found',
          description: description || 'No flight operations match your current search criteria. Try adjusting your filters.'
        }
      case 'loading':
        return {
          icon: <Plane className="w-12 h-12 text-gray-400 animate-pulse" />,
          title: title || 'Loading Operations',
          description: description || 'Fetching the latest flight operations...'
        }
      default:
        return {
          icon: <Plane className="w-12 h-12 text-gray-400" />,
          title: title || 'Airport Operations',
          description: description || 'Flight operations will appear here.'
        }
    }
  }

  const content = getDefaultContent()

  return (
    <div className={cn(
      "flex flex-col items-center justify-center p-8 text-center",
      className
    )}>
      <div className="mb-4">
        {content.icon}
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        {content.title}
      </h3>
      <p className="text-sm text-gray-600 max-w-sm">
        {content.description}
      </p>
    </div>
  )
}
