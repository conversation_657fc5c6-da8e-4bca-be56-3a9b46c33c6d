"use client"

import React, { useRef, useState, useEffect, useCallback, useImperativeHandle, forwardRef } from 'react'
import { cn } from '@/lib/utils'
import { SCROLL_THRESHOLD, MIN_CONTENT_HEIGHT_FOR_SCROLL } from '../constants/layout'

interface OperationsDrawerProps {
  children: React.ReactNode
  className?: string
  contentRef?: React.RefObject<HTMLDivElement | null>
}

export interface OperationsDrawerRef {
  resetPosition: () => void
}

export const OperationsDrawer = forwardRef<OperationsDrawerRef, OperationsDrawerProps>(
  function OperationsDrawer({ children, className, contentRef: externalContentRef }, ref) {
  const [isExpanded, setIsExpanded] = useState(false)
  const drawerRef = useRef<HTMLDivElement>(null)
  const internalContentRef = useRef<HTMLDivElement>(null)
  const contentRef = externalContentRef || internalContentRef
  const [touchStart, setTouchStart] = useState<number | null>(null)

  // Check if content is tall enough to warrant scroll expansion
  const shouldEnableScrollExpansion = useCallback(() => {
    if (!contentRef.current) return false
    const contentHeight = contentRef.current.scrollHeight
    const containerHeight = contentRef.current.clientHeight
    return contentHeight > containerHeight && contentHeight > MIN_CONTENT_HEIGHT_FOR_SCROLL
  }, [contentRef])

  // Unified scroll handler for both touch and wheel events
  const handleScroll = useCallback(() => {
    if (!contentRef.current || !shouldEnableScrollExpansion()) return

    const scrollTop = contentRef.current.scrollTop

    if (scrollTop > SCROLL_THRESHOLD && !isExpanded) {
      // User scrolled down - expand drawer to full height
      setIsExpanded(true)
    } else if (scrollTop <= 0 && isExpanded) {
      // User scrolled back to top - collapse drawer
      setIsExpanded(false)
    }
  }, [isExpanded, contentRef, shouldEnableScrollExpansion])

  // Handle drawer handle click for manual collapse when content fits
  const handleDrawerHandleClick = useCallback(() => {
    if (isExpanded) {
      // If expanded, always allow collapse via handle click
      setIsExpanded(false)
      if (contentRef.current) {
        contentRef.current.scrollTo({ top: 0, behavior: 'smooth' })
      }
    }
  }, [isExpanded, contentRef])

  // Touch handlers for swipe-down gesture when content doesn't scroll
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (isExpanded && contentRef.current) {
      const contentHeight = contentRef.current.scrollHeight
      const containerHeight = contentRef.current.clientHeight
      const hasScroll = contentHeight > containerHeight

      // Only enable swipe-down if content doesn't scroll (fits in expanded view)
      if (!hasScroll) {
        setTouchStart(e.touches[0].clientY)
      }
    }
  }, [isExpanded, contentRef])

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (touchStart !== null && isExpanded) {
      const currentY = e.touches[0].clientY
      const deltaY = currentY - touchStart

      // If user swiped down more than 50px, collapse the drawer
      if (deltaY > 50) {
        setIsExpanded(false)
        setTouchStart(null)
        if (contentRef.current) {
          contentRef.current.scrollTo({ top: 0, behavior: 'smooth' })
        }
      }
    }
  }, [touchStart, isExpanded, contentRef])

  const handleTouchEnd = useCallback(() => {
    setTouchStart(null)
  }, [])

  // Set up scroll listener
  useEffect(() => {
    const contentElement = contentRef.current
    if (!contentElement) return

    contentElement.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      contentElement.removeEventListener('scroll', handleScroll)
    }
  }, [handleScroll, contentRef])

  // Expose reset function to parent
  useImperativeHandle(ref, () => ({
    resetPosition: () => {
      setIsExpanded(false)
      if (contentRef.current) {
        contentRef.current.scrollTo({ top: 0, behavior: 'smooth' })
      }
    }
  }), [contentRef])



  return (
    <div
      ref={drawerRef}
      className={cn(
        "fixed bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-2xl z-50 transition-all duration-300 ease-out",
        className
      )}
      style={{
        height: isExpanded ? '100vh' : '60vh'
      }}
    >
      {/* Drawer Handle */}
      <div
        className="flex justify-center pt-3 pb-2 cursor-pointer"
        onClick={handleDrawerHandleClick}
      >
        <div className="w-12 h-1 bg-gray-300 rounded-full" />
      </div>

      {/* Scrollable Content */}
      <div
        ref={contentRef}
        className="overflow-y-auto pb-safe px-4 overscroll-contain h-[calc(100%-24px)]"
        style={{
          WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS - no Tailwind equivalent
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {children}
      </div>
    </div>
  )
})
