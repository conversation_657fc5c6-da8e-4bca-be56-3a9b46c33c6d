"use client"

import React from 'react'
import { cn } from '@/lib/utils'
import { OperationsList } from './OperationsList'
import { DateSelector } from './DateSelector'
import { AircraftSelector } from './AircraftSelector'

import type { FlightOperation, SelectedOperation, DateRange, AircraftSelection } from '../types'

interface OperationsSidebarProps {
  operations: FlightOperation[] // Filtered operations for display
  allOperations: FlightOperation[] // All operations for aircraft selector
  selectedOperation: SelectedOperation
  onOperationSelect: (operation: FlightOperation) => void
  selectedDateRange: DateRange
  onDateRangeChange: (range: DateRange) => void
  selectedAircraft: AircraftSelection
  onAircraftSelectionChange: (selection: AircraftSelection) => void
  className?: string
}

export function OperationsSidebar({
  operations,
  allOperations,
  selectedOperation,
  onOperationSelect,
  selectedDateRange,
  onDateRangeChange,
  selectedAircraft,
  onAircraftSelectionChange,
  className
}: OperationsSidebarProps) {
  return (
    <div className={cn("flex flex-col h-full bg-white border-r border-gray-200", className)}>
      {/* Header */}
      <div className="flex-shrink-0 p-6 border-b border-gray-200">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Airport Operations</h1>

        {/* Filter controls */}
        <div className="flex gap-3">
          <DateSelector
            selectedRange={selectedDateRange}
            onDateRangeChange={onDateRangeChange}
            className="flex-1"
          />
          <AircraftSelector
            operations={allOperations}
            selectedAircraft={selectedAircraft}
            onAircraftSelectionChange={onAircraftSelectionChange}
            className="flex-1"
          />
        </div>
      </div>

      {/* Scrollable Operations List */}
      <div className="flex-1 overflow-y-auto p-6">
        <OperationsList
          operations={operations}
          selectedOperation={selectedOperation}
          onOperationSelect={onOperationSelect}
        />
      </div>


    </div>
  )
}
