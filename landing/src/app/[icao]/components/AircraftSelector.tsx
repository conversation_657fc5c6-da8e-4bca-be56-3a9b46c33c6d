"use client"

import React, { useState, useMemo } from 'react'
import { Plane } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import type { FlightOperation, AircraftSelection } from '../types'
import { getAircraftSortedByOperationCount } from '../utils/dateUtils'

interface AircraftSelectorProps {
  operations: FlightOperation[]
  selectedAircraft: AircraftSelection
  onAircraftSelectionChange: (selection: AircraftSelection) => void
  className?: string
}

export function AircraftSelector({ 
  operations, 
  selectedAircraft, 
  onAircraftSelectionChange, 
  className 
}: AircraftSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [tempSelection, setTempSelection] = useState<AircraftSelection>(selectedAircraft)

  // Extract unique aircraft registrations from operations, sorted by operation count
  const availableAircraft = useMemo(() => {
    return getAircraftSortedByOperationCount(operations)
  }, [operations])

  // Handle "All Aircraft" selection
  const handleAllAircraftToggle = () => {
    if (tempSelection.selectedAircraft.length === availableAircraft.length) {
      // If all are selected, deselect all
      setTempSelection({
        selectedAircraft: [],
        label: 'No Aircraft Selected'
      })
    } else {
      // Select all aircraft
      setTempSelection({
        selectedAircraft: [...availableAircraft],
        label: 'All Aircraft'
      })
    }
  }

  // Handle individual aircraft selection
  const handleAircraftToggle = (registration: string) => {
    const isSelected = tempSelection.selectedAircraft.includes(registration)
    let newSelection: string[]

    if (isSelected) {
      newSelection = tempSelection.selectedAircraft.filter(r => r !== registration)
    } else {
      newSelection = [...tempSelection.selectedAircraft, registration]
    }

    // Generate appropriate label
    let label: string
    if (newSelection.length === 0) {
      label = 'No Aircraft Selected'
    } else if (newSelection.length === availableAircraft.length) {
      label = 'All Aircraft'
    } else if (newSelection.length === 1) {
      label = newSelection[0]
    } else {
      label = `${newSelection.length} Aircraft Selected`
    }

    setTempSelection({
      selectedAircraft: newSelection,
      label
    })
  }

  const handleApply = () => {
    onAircraftSelectionChange(tempSelection)
    setIsOpen(false)
  }

  // Check if all aircraft are selected
  const allSelected = tempSelection.selectedAircraft.length === availableAircraft.length

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className={cn("flex-1 justify-start gap-2", className)}>
          <Plane className="w-4 h-4" />
          <span>{selectedAircraft.label}</span>
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg">
            <Plane className="w-5 h-5" />
            Select Aircraft
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-2">
          {/* Selected Aircraft Display - Moved to top */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Plane className="w-4 h-4 text-blue-600" />
                <p className="text-sm font-semibold text-blue-900">Selected Aircraft</p>
              </div>
              <Button onClick={handleApply} size="sm" className="h-8 px-3">
                Apply
              </Button>
            </div>
            <p className="font-bold text-blue-900 text-lg mb-1">{tempSelection.label}</p>
            <p className="text-sm text-blue-700">
              {tempSelection.selectedAircraft.length} of {availableAircraft.length} aircraft selected
            </p>
          </div>

          {/* Quick Select All/None */}
          <div>
            <h4 className="text-sm font-semibold text-gray-900 mb-4">Quick Select</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Checkbox
                  id="all-aircraft"
                  checked={allSelected}
                  onCheckedChange={handleAllAircraftToggle}
                />
                <label 
                  htmlFor="all-aircraft" 
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  All Aircraft ({availableAircraft.length})
                </label>
              </div>
            </div>
          </div>

          {/* Individual Aircraft Selection */}
          {availableAircraft.length > 0 && (
            <div>
              <h4 className="text-sm font-semibold text-gray-900 mb-4">Individual Aircraft</h4>
              <div className="max-h-64 overflow-y-auto space-y-2 border border-gray-200 rounded-lg p-3">
                {availableAircraft.map((registration) => (
                  <div key={registration} className="flex items-center space-x-3">
                    <Checkbox
                      id={`aircraft-${registration}`}
                      checked={tempSelection.selectedAircraft.includes(registration)}
                      onCheckedChange={() => handleAircraftToggle(registration)}
                    />
                    <label 
                      htmlFor={`aircraft-${registration}`}
                      className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer flex-1"
                    >
                      {registration}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* No Aircraft Available */}
          {availableAircraft.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Plane className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">No aircraft data available</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
