import { format, isSameDay, parseISO, isWithinInterval, startOfDay, endOfDay } from 'date-fns'
import { fromZonedTime, toZonedTime } from 'date-fns-tz'
import type { FlightOperation, DateRange, AircraftSelection } from '../types'

// Default airport timezone (fallback)
export const DEFAULT_AIRPORT_TIMEZONE = 'America/New_York' // GMT-4 during standard time

export interface GroupedOperations {
  date: Date
  label: string
  operations: FlightOperation[]
}

// Convert UTC time to airport timezone
export function toAirportTime(utcTime: string | Date, timezone?: string): Date {
  const utcDate = typeof utcTime === 'string' ? parseISO(utcTime) : utcTime
  const tz = timezone || DEFAULT_AIRPORT_TIMEZONE
  return toZonedTime(utcDate, tz)
}

// Convert airport timezone to UTC
export function fromAirportTime(airportTime: Date, timezone?: string): Date {
  const tz = timezone || DEFAULT_AIRPORT_TIMEZONE
  return fromZonedTime(airportTime, tz)
}

// Filter operations by date range (in airport timezone)
export function filterOperationsByDateRange(operations: FlightOperation[], dateRange: DateRange, timezone?: string): FlightOperation[] {
  const startDate = startOfDay(dateRange.from)
  const endDate = endOfDay(dateRange.to)

  return operations.filter(operation => {
    const operationAirportTime = toAirportTime(operation.time, timezone)
    return isWithinInterval(operationAirportTime, { start: startDate, end: endDate })
  })
}

// Filter operations by selected aircraft
export function filterOperationsByAircraft(operations: FlightOperation[], aircraftSelection: AircraftSelection): FlightOperation[] {
  // If no aircraft selected, return empty array
  if (aircraftSelection.selectedAircraft.length === 0) {
    return []
  }

  // If all aircraft are selected (or selection is empty), return all operations
  const allAvailableAircraft = new Set<string>()
  operations.forEach(op => {
    if (op.registration && op.registration.trim()) {
      allAvailableAircraft.add(op.registration.trim())
    }
  })

  // If selected aircraft includes all available aircraft, return all operations
  if (aircraftSelection.selectedAircraft.length === allAvailableAircraft.size &&
      aircraftSelection.selectedAircraft.every(reg => allAvailableAircraft.has(reg))) {
    return operations
  }

  // Filter by selected aircraft registrations
  const selectedSet = new Set(aircraftSelection.selectedAircraft)
  return operations.filter(operation => {
    return operation.registration && selectedSet.has(operation.registration.trim())
  })
}

// Combined filter function for both date and aircraft
export function filterOperations(
  operations: FlightOperation[],
  dateRange: DateRange,
  aircraftSelection: AircraftSelection,
  timezone?: string
): FlightOperation[] {
  // First filter by date range
  const dateFiltered = filterOperationsByDateRange(operations, dateRange, timezone)

  // Then filter by aircraft selection
  return filterOperationsByAircraft(dateFiltered, aircraftSelection)
}

// Get unique aircraft sorted by operation count (most operations first)
export function getAircraftSortedByOperationCount(operations: FlightOperation[]): string[] {
  const aircraftCounts = new Map<string, number>()

  // Count operations per aircraft
  operations.forEach(op => {
    if (op.registration && op.registration.trim()) {
      const registration = op.registration.trim()
      aircraftCounts.set(registration, (aircraftCounts.get(registration) || 0) + 1)
    }
  })

  // Sort by count (descending) then alphabetically
  return Array.from(aircraftCounts.entries())
    .sort((a, b) => {
      // First sort by count (descending)
      if (b[1] !== a[1]) {
        return b[1] - a[1]
      }
      // Then sort alphabetically
      return a[0].localeCompare(b[0])
    })
    .map(([registration]) => registration)
}

export function groupOperationsByDate(operations: FlightOperation[], timezone?: string): GroupedOperations[] {
  // Group operations by date in airport timezone
  const groups = new Map<string, FlightOperation[]>()

  operations.forEach(operation => {
    const operationAirportTime = toAirportTime(operation.time, timezone)
    const dateKey = format(operationAirportTime, 'yyyy-MM-dd')

    if (!groups.has(dateKey)) {
      groups.set(dateKey, [])
    }
    groups.get(dateKey)!.push(operation)
  })

  // Convert to array and sort by date (newest first)
  const groupedArray: GroupedOperations[] = Array.from(groups.entries())
    .map(([dateKey, ops]) => {
      const date = parseISO(dateKey + 'T00:00:00')
      return {
        date,
        label: getDateLabel(date, timezone),
        operations: ops.sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime())
      }
    })
    .sort((a, b) => b.date.getTime() - a.date.getTime())

  return groupedArray
}

export function getDateLabel(date: Date, timezone?: string): string {
  const todayInAirportTz = toAirportTime(new Date(), timezone)
  const yesterdayInAirportTz = new Date(todayInAirportTz.getTime() - 24 * 60 * 60 * 1000)

  if (isSameDay(date, todayInAirportTz)) {
    return 'Today'
  }

  if (isSameDay(date, yesterdayInAirportTz)) {
    return 'Yesterday'
  }

  // For other dates, show day and date
  return format(date, 'EEE d MMM')
}

export function formatOperationTime(timeString: string, timezone?: string): string {
  const airportTime = toAirportTime(timeString, timezone)
  return format(airportTime, 'HH:mm')
}

export function formatOperationDateTime(timeString: string, timezone?: string): string {
  const airportTime = toAirportTime(timeString, timezone)
  const todayInAirportTz = toAirportTime(new Date(), timezone)

  if (isSameDay(airportTime, todayInAirportTz)) {
    return `Today ${format(airportTime, 'HH:mm')}`
  }

  const yesterdayInAirportTz = new Date(todayInAirportTz.getTime() - 24 * 60 * 60 * 1000)
  if (isSameDay(airportTime, yesterdayInAirportTz)) {
    return `Yesterday ${format(airportTime, 'HH:mm')}`
  }

  return format(airportTime, 'MMM d HH:mm')
}
