import type { AirportTransition } from '@/types/api'

/**
 * Calculate the start and end times for an operation based on surrounding transitions
 */
export function calculateOperationTimes(
  currentTransition: AirportTransition,
  allTransitions: AirportTransition[]
): { startTime: string; endTime: string } {
  // Sort transitions by time for the same hex
  const hexTransitions = allTransitions
    .filter(t => t.hex === currentTransition.hex)
    .sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime())

  const currentIndex = hexTransitions.findIndex(t =>
    t.time === currentTransition.time &&
    t.flight_phase === currentTransition.flight_phase
  )

  const startTime = calculateStartTime(currentTransition, hexTransitions, currentIndex)
  const endTime = calculateEndTime(currentTransition, hexTransitions, currentIndex)

  return { startTime, endTime }
}

/**
 * Calculate start time for a specific transition
 */
export function calculateTransitionStartTime(
  transition: AirportTransition,
  allTransitions: AirportTransition[]
): string {
  const hexTransitions = allTransitions
    .filter(t => t.hex === transition.hex)
    .sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime())

  const currentIndex = hexTransitions.findIndex(t =>
    t.time === transition.time &&
    t.flight_phase === transition.flight_phase
  )

  return calculateStartTime(transition, hexTransitions, currentIndex)
}

/**
 * Calculate end time for a specific transition
 */
export function calculateTransitionEndTime(
  transition: AirportTransition,
  allTransitions: AirportTransition[]
): string {
  const hexTransitions = allTransitions
    .filter(t => t.hex === transition.hex)
    .sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime())

  const currentIndex = hexTransitions.findIndex(t =>
    t.time === transition.time &&
    t.flight_phase === transition.flight_phase
  )

  return calculateEndTime(transition, hexTransitions, currentIndex)
}

/**
 * Calculate start time based on previous transition
 */
function calculateStartTime(
  currentTransition: AirportTransition,
  hexTransitions: AirportTransition[],
  currentIndex: number
): string {
  const previousTransition = currentIndex > 0 ? hexTransitions[currentIndex - 1] : null

  if (!previousTransition) {
    // No previous transition, use 5 minutes back
    const currentTime = new Date(currentTransition.time)
    return new Date(currentTime.getTime() - 5 * 60 * 1000).toISOString()
  }

  // If previous transition is "appear" or "startup", use its time
  if (previousTransition.flight_phase === 'appear' || previousTransition.flight_phase === 'startup') {
    return previousTransition.time
  }

  // Otherwise, use the middle time between previous and current
  const previousTime = new Date(previousTransition.time).getTime()
  const currentTime = new Date(currentTransition.time).getTime()
  const middleTime = new Date((previousTime + currentTime) / 2)
  
  return middleTime.toISOString()
}

/**
 * Calculate end time based on next transition
 */
function calculateEndTime(
  currentTransition: AirportTransition,
  hexTransitions: AirportTransition[],
  currentIndex: number
): string {
  const nextTransition = currentIndex < hexTransitions.length - 1 ? hexTransitions[currentIndex + 1] : null

  if (!nextTransition) {
    // No next transition, use 5 minutes forward
    const currentTime = new Date(currentTransition.time)
    return new Date(currentTime.getTime() + 5 * 60 * 1000).toISOString()
  }

  // If next transition is "disappear" or "shutdown", use its time
  if (nextTransition.flight_phase === 'disappear' || nextTransition.flight_phase === 'shutdown') {
    return nextTransition.time
  }

  // Otherwise, use the middle time between current and next
  const currentTime = new Date(currentTransition.time).getTime()
  const nextTime = new Date(nextTransition.time).getTime()
  const middleTime = new Date((currentTime + nextTime) / 2)
  
  return middleTime.toISOString()
}
