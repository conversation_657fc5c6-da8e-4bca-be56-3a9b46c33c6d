import type { FlightPathSegment } from '../types'

interface TracePosition {
  lat: number
  lng: number
  alt_baro?: number
  time: string
}

/**
 * Create flight path segments based on altitude data
 * Ground segments (alt_baro = -10000) are gray, airborne segments are blue
 */
export function createFlightPathSegments(positions: TracePosition[]): FlightPathSegment[] {
  if (positions.length < 2) return []

  const segments: FlightPathSegment[] = []
  let currentSegment: { coordinates: [number, number][]; isGround: boolean } | null = null

  for (let i = 0; i < positions.length; i++) {
    const position = positions[i]
    const coordinate: [number, number] = [position.lat, position.lng]
    const isGround = position.alt_baro === -10000

    if (!currentSegment) {
      // Start new segment
      currentSegment = {
        coordinates: [coordinate],
        isGround
      }
    } else if (currentSegment.isGround === isGround) {
      // Continue current segment
      currentSegment.coordinates.push(coordinate)
    } else {
      // Ground/air status changed, finish current segment and start new one
      if (currentSegment.coordinates.length > 1) {
        segments.push({
          coordinates: currentSegment.coordinates,
          color: currentSegment.isGround ? '#6B7280' : '#3B82F6', // gray for ground, blue for air
          isGround: currentSegment.isGround
        })
      }

      // Start new segment with the transition point
      currentSegment = {
        coordinates: [currentSegment.coordinates[currentSegment.coordinates.length - 1], coordinate],
        isGround
      }
    }
  }

  // Add the final segment
  if (currentSegment && currentSegment.coordinates.length > 1) {
    segments.push({
      coordinates: currentSegment.coordinates,
      color: currentSegment.isGround ? '#6B7280' : '#3B82F6', // gray for ground, blue for air
      isGround: currentSegment.isGround
    })
  }

  return segments
}
