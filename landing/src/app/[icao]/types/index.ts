// Shared types for the airport operations interface

export interface FlightPathSegment {
  coordinates: [number, number][]
  color: string
  isGround: boolean
}

export interface FlightPath {
  coordinates: [number, number][]
  color: string
  name: string
  segments?: FlightPathSegment[] // For altitude-based styling
}

export interface FlightOperation {
  id: string
  hex: string // Aircraft hex identifier
  registration: string
  aircraftType: string
  runway: string
  time: string // ISO datetime string
  startTime: string // Calculated start time for trace fetching
  endTime: string // Calculated end time for trace fetching
  operationType: 'landing' | 'takeoff' | 'pattern' | 'taxi'
  isSelected?: boolean
  flightPath: FlightPath
}

export type SelectedOperation = FlightOperation | null

export type OperationType = FlightOperation['operationType']

// Date selection types
export interface DateRange {
  from: Date
  to: Date
  label: string
}

export type DatePreset = 'today' | 'yesterday' | 'last7days' | 'last30days' | 'custom'

// Aircraft selection types
export interface AircraftSelection {
  selectedAircraft: string[] // Array of registration numbers
  label: string // Display label like "All Aircraft" or "3 Aircraft Selected"
}
