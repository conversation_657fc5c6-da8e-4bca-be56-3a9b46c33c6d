"use client"

import { useState, useEffect } from 'react'
import { getAircraftTrace } from '@/lib/api'
import { convertTracePositionsToCoordinates } from '@/lib/h3-utils'

import type { FlightOperation } from '../types'

interface FlightTracePosition {
  time: string
  lat: number
  lng: number
  alt_baro?: number
  gs?: number
  track?: number | null
  baro_rate?: number | null
  h3_index: string
}

interface UseFlightTraceResult {
  tracePositions: FlightTracePosition[]
  loading: boolean
  error: string | null
}

export function useFlightTrace(selectedOperation: FlightOperation | null): UseFlightTraceResult {
  const [tracePositions, setTracePositions] = useState<FlightTracePosition[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!selectedOperation) {
      setTracePositions([])
      setLoading(false)
      setError(null)
      return
    }

    const fetchTrace = async () => {
      try {
        setLoading(true)
        setError(null)

        // Use the calculated start and end times from the operation
        const response = await getAircraftTrace(
          selectedOperation.hex,
          selectedOperation.startTime,
          selectedOperation.endTime
        )

        // Convert H3 indexes to coordinates
        const positionsWithCoords = convertTracePositionsToCoordinates(response.positions)

        setTracePositions(positionsWithCoords)
      } catch (err) {
        console.error('Error fetching flight trace:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch flight trace')
        setTracePositions([])
      } finally {
        setLoading(false)
      }
    }

    fetchTrace()
  }, [selectedOperation])

  return {
    tracePositions,
    loading,
    error
  }
}
