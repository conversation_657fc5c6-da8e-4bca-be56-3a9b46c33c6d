"use client"

import { useState, useEffect } from 'react'
import { getAirportByIcao } from '@/lib/api'
import type { Airport } from '@/types/api'

interface UseAirportDataResult {
  airport: Airport | null
  loading: boolean
  error: string | null
}

export function useAirportData(icao: string): UseAirportDataResult {
  const [airport, setAirport] = useState<Airport | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchAirport = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const airportData = await getAirportByIcao(icao)
        setAirport(airportData)
      } catch (err) {
        console.error('Error fetching airport data:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch airport data')
      } finally {
        setLoading(false)
      }
    }

    if (icao) {
      fetchAirport()
    }
  }, [icao])

  return {
    airport,
    loading,
    error
  }
}
