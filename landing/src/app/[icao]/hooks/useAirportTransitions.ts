"use client"

import { useState, useEffect } from 'react'
import { getAirportTransitionsExtended } from '@/lib/api'
import { calculateOperationTimes, calculateTransitionStartTime, calculateTransitionEndTime } from '../utils/operationTimes'
import type { AirportTransition } from '@/types/api'
import type { FlightOperation } from '../types'

interface UseAirportTransitionsResult {
  operations: FlightOperation[]
  loading: boolean
  error: string | null
  refetch: () => void
}

// Detect pattern operations (takeoff followed by landing within 10 minutes on same runway)
function detectPatterns(transitions: AirportTransition[]): {
  patterns: Array<{ takeoff: AirportTransition; landing: AirportTransition }>
  usedTransitions: Set<string>
} {
  const patterns: Array<{ takeoff: AirportTransition; landing: AirportTransition }> = []
  const usedTransitions = new Set<string>()

  // Group transitions by hex
  const transitionsByHex = new Map<string, AirportTransition[]>()
  transitions.forEach(transition => {
    if (!transitionsByHex.has(transition.hex)) {
      transitionsByHex.set(transition.hex, [])
    }
    transitionsByHex.get(transition.hex)!.push(transition)
  })

  // Check each aircraft for patterns
  transitionsByHex.forEach(hexTransitions => {
    // Sort by time
    const sortedTransitions = hexTransitions.sort((a, b) =>
      new Date(a.time).getTime() - new Date(b.time).getTime()
    )

    for (let i = 0; i < sortedTransitions.length - 1; i++) {
      const current = sortedTransitions[i]
      const next = sortedTransitions[i + 1]

      // Check if current is takeoff and next is landing
      if (current.flight_phase === 'takeoff' && next.flight_phase === 'landing') {
        // Check if they're on the same runway
        if (current.runway_name && next.runway_name && current.runway_name === next.runway_name) {
          // Check if they're within 10 minutes
          const timeDiff = new Date(next.time).getTime() - new Date(current.time).getTime()
          const tenMinutesInMs = 10 * 60 * 1000

          if (timeDiff <= tenMinutesInMs) {
            // Create transition IDs for tracking
            const takeoffId = `${current.hex}-${current.time}-${current.flight_phase}`
            const landingId = `${next.hex}-${next.time}-${next.flight_phase}`

            // Only add if neither transition is already used
            if (!usedTransitions.has(takeoffId) && !usedTransitions.has(landingId)) {
              patterns.push({ takeoff: current, landing: next })
              usedTransitions.add(takeoffId)
              usedTransitions.add(landingId)
            }
          }
        }
      }
    }
  })

  return { patterns, usedTransitions }
}

// Convert API transition to FlightOperation
function convertTransitionToOperation(
  transition: AirportTransition,
  allTransitions: AirportTransition[]
): FlightOperation | null {
  // Only process takeoff and landing operations for now
  if (transition.flight_phase !== 'takeoff' && transition.flight_phase !== 'landing') {
    return null
  }

  // Generate a unique ID
  const id = `${transition.hex}-${transition.time}-${transition.flight_phase}`

  // Use flight as registration, fallback to hex if no flight
  const registration = transition.flight || transition.hex.toUpperCase()

  // Calculate start and end times for trace fetching
  const { startTime, endTime } = calculateOperationTimes(transition, allTransitions)

  // Generate empty flight path (will be populated with real trace data when selected)
  const flightPath = {
    coordinates: [] as [number, number][],
    color: transition.flight_phase === 'landing' ? '#3B82F6' : '#10B981',
    name: `${registration} ${transition.flight_phase}`
  }

  return {
    id,
    hex: transition.hex,
    registration,
    aircraftType: '', // Not available in API yet
    runway: transition.runway_name || 'Unknown',
    time: transition.time,
    startTime,
    endTime,
    operationType: transition.flight_phase,
    isSelected: false,
    flightPath
  }
}

// Convert pattern to FlightOperation
function convertPatternToOperation(
  pattern: { takeoff: AirportTransition; landing: AirportTransition },
  allTransitions: AirportTransition[]
): FlightOperation {
  const { takeoff, landing } = pattern

  // Generate a unique ID for the pattern
  const id = `${takeoff.hex}-${takeoff.time}-pattern`

  // Use flight as registration, fallback to hex if no flight
  const registration = takeoff.flight || takeoff.hex.toUpperCase()

  // Calculate start time from takeoff and end time from landing
  const startTime = calculateTransitionStartTime(takeoff, allTransitions)
  const endTime = calculateTransitionEndTime(landing, allTransitions)

  // Generate empty flight path (will be populated with real trace data when selected)
  const flightPath = {
    coordinates: [] as [number, number][],
    color: '#F59E0B', // Orange color for patterns
    name: `${registration} pattern`
  }

  return {
    id,
    hex: takeoff.hex,
    registration,
    aircraftType: '', // Not available in API yet
    runway: takeoff.runway_name || 'Unknown',
    time: takeoff.time, // Use takeoff time for card display
    startTime,
    endTime,
    operationType: 'pattern',
    isSelected: false,
    flightPath
  }
}

export function useAirportTransitions(icao: string): UseAirportTransitionsResult {
  const [operations, setOperations] = useState<FlightOperation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let isInitialLoad = true

    const fetchTransitions = async () => {
      try {
        // Only show loading spinner on initial load, not on background refreshes
        if (isInitialLoad) {
          setLoading(true)
        }
        setError(null)

        // Calculate time range: from 30 days ago to now
        const endTime = new Date()
        const startTime = new Date(endTime.getTime() - 30 * 24 * 60 * 60 * 1000) // 30 days ago

        const response = await getAirportTransitionsExtended(
          icao,
          startTime.toISOString(),
          endTime.toISOString()
        )

        // Detect patterns first
        const { patterns, usedTransitions } = detectPatterns(response.transitions)

        // Log pattern detection results for debugging
        if (patterns.length > 0) {
          console.log(`Detected ${patterns.length} pattern(s):`, patterns.map(p => ({
            hex: p.takeoff.hex,
            takeoffTime: p.takeoff.time,
            landingTime: p.landing.time,
            runway: p.takeoff.runway_name,
            timeDiff: (new Date(p.landing.time).getTime() - new Date(p.takeoff.time).getTime()) / (1000 * 60) // minutes
          })))
        }

        // Convert patterns to operations
        const patternOperations = patterns.map(pattern =>
          convertPatternToOperation(pattern, response.transitions)
        )

        // Convert remaining individual transitions to operations (excluding those used in patterns)
        const individualOperations = response.transitions
          .filter(transition => {
            // Only process takeoff and landing operations
            if (transition.flight_phase !== 'takeoff' && transition.flight_phase !== 'landing') {
              return false
            }
            // Exclude transitions that are part of patterns
            const transitionId = `${transition.hex}-${transition.time}-${transition.flight_phase}`
            return !usedTransitions.has(transitionId)
          })
          .map(transition => convertTransitionToOperation(transition, response.transitions))
          .filter((op): op is FlightOperation => op !== null)

        // Combine pattern and individual operations
        const allOperations = [...patternOperations, ...individualOperations]
          .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime()) // Sort by time, newest first

        setOperations(allOperations)
      } catch (err) {
        console.error('Error fetching transitions:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch transitions')
      } finally {
        if (isInitialLoad) {
          setLoading(false)
          isInitialLoad = false
        }
      }
    }

    // Initial fetch
    fetchTransitions()

    // Set up polling every 10 seconds for background updates
    const interval = setInterval(fetchTransitions, 10000)

    return () => clearInterval(interval)
  }, [icao])

  const refetch = () => {
    // Force a refresh by clearing and re-fetching
    setLoading(true)
    setError(null)
  }

  return {
    operations,
    loading,
    error,
    refetch
  }
}
