"use client"

import React from 'react'
import { useParams } from 'next/navigation'
import { AirportHeader } from './components/AirportHeader'
import { RecentlySeenAircraft } from './components/RecentlySeenAircraft'
import { RecentOperations } from './components/RecentOperations'
import { DashboardSection } from './components/DashboardSection'
import { WeatherSection } from './components/WeatherSection'
import { useAirportData } from './hooks/useAirportData'
import { useAirportTransitions } from './hooks/useAirportTransitions'

export default function AirportHomePage() {
  const params = useParams()
  const icao = params.icao as string

  // Fetch airport data for the dynamic ICAO
  const { airport, loading: airportLoading, error: airportError } = useAirportData(icao)

  // Fetch real airport transitions data for the dynamic ICAO (last 30 days for aircraft list)
  const { operations: allOperations, loading, error } = useAirportTransitions(icao)

  // Show loading state only on initial load
  if (loading || airportLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading airport data...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (airportError || error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading data: {airportError || error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Airport Header with Picture Placeholder */}
      <AirportHeader airport={airport} />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 pb-8 sm:pb-12 space-y-6 sm:space-y-8">
        {/* Recently Seen Aircraft */}
        <RecentlySeenAircraft operations={allOperations} />

        {/* Recent Operations */}
        <RecentOperations operations={allOperations} timezone={airport?.timezone} />

        {/* Dashboard and Weather Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          <DashboardSection />
          <WeatherSection />
        </div>
      </div>
    </div>
  )
}