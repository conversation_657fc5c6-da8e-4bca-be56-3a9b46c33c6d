// Layout constants - single source of truth
export const M<PERSON><PERSON><PERSON>_DRAWER_HEIGHT = {
  COLLAPSED: 60, // 60vh - drawer starts at 60% of screen height
  EXPANDED: 100,  // 100vh - drawer expands to full screen
} as const

// Derived values
export const MO<PERSON><PERSON>_MAP_HEIGHT = 100 - MOBILE_DRAWER_HEIGHT.COLLAPSED // 40vh - map only in top 40%

// Scroll thresholds
export const SCROLL_THRESHOLD = 20 // pixels to scroll before expanding drawer
export const MIN_CONTENT_HEIGHT_FOR_SCROLL = 300 // minimum content height to enable scroll expansion
