"use client"

import React, { useState, useMemo, useEffect } from 'react'
import { useSearchParams, useParams } from 'next/navigation'
import { startOfDay, endOfDay, subDays } from 'date-fns'
import { ResponsiveLayout } from '../components/ResponsiveLayout'
import { filterOperations, toAirportTime, getAircraftSortedByOperationCount } from '../utils/dateUtils'
import { createFlightPathSegments } from '../utils/flightPathUtils'
import { useAirportTransitions } from '../hooks/useAirportTransitions'
import { useFlightTrace } from '../hooks/useFlightTrace'
import { useAirportData } from '../hooks/useAirportData'
import type { FlightOperation, SelectedOperation, DateRange, AircraftSelection } from '../types'

export default function AirportOperationsPage() {
  const searchParams = useSearchParams()
  const params = useParams()
  const icao = params.icao as string
  const [selectedOperation, setSelectedOperation] = useState<SelectedOperation>(null)

  // Fetch airport data for the dynamic ICAO
  const { airport, loading: airportLoading, error: airportError } = useAirportData(icao)

  // Fetch real airport transitions data for the dynamic ICAO
  const { operations: apiOperations, loading, error } = useAirportTransitions(icao)

  // Fetch flight trace for selected operation
  const { tracePositions, loading: traceLoading } = useFlightTrace(selectedOperation)

  // Initialize with last 30 days date range in airport timezone
  const [selectedDateRange, setSelectedDateRange] = useState<DateRange>(() => {
    const today = toAirportTime(new Date(), airport?.timezone)
    return {
      from: startOfDay(subDays(today, 29)),
      to: endOfDay(today),
      label: 'Last 30 days'
    }
  })

  // Initialize with all aircraft selected
  const [selectedAircraft, setSelectedAircraft] = useState<AircraftSelection>({
    selectedAircraft: [],
    label: 'All Aircraft'
  })

  // Update date range when airport timezone changes
  useEffect(() => {
    if (airport?.timezone) {
      const today = toAirportTime(new Date(), airport.timezone)
      setSelectedDateRange({
        from: startOfDay(subDays(today, 29)),
        to: endOfDay(today),
        label: 'Last 30 days'
      })
    }
  }, [airport?.timezone])

  // Initialize aircraft selection only once when operations first load (independent of date changes)
  useEffect(() => {
    if (apiOperations.length > 0 && selectedAircraft.selectedAircraft.length === 0) {
      const sortedAircraft = getAircraftSortedByOperationCount(apiOperations)
      setSelectedAircraft({
        selectedAircraft: sortedAircraft,
        label: 'All Aircraft'
      })
    }
  }, [apiOperations, selectedAircraft.selectedAircraft.length])

  // Handle URL parameters for filtering
  useEffect(() => {
    if (apiOperations.length === 0) return

    const aircraftParam = searchParams.get('aircraft')
    const operationParam = searchParams.get('operation')

    // Handle aircraft filter from URL
    if (aircraftParam && selectedAircraft.selectedAircraft.length > 0) {
      const decodedAircraft = decodeURIComponent(aircraftParam)
      // Check if this aircraft exists in our operations
      const aircraftExists = apiOperations.some(op => op.registration === decodedAircraft)

      if (aircraftExists) {
        setSelectedAircraft({
          selectedAircraft: [decodedAircraft],
          label: `${decodedAircraft}`
        })
      }
    }

    // Handle operation selection from URL
    if (operationParam) {
      const decodedOperationId = decodeURIComponent(operationParam)
      const operation = apiOperations.find(op => op.id === decodedOperationId)

      if (operation) {
        setSelectedOperation(operation)
      }
    }
  }, [apiOperations, searchParams, selectedAircraft.selectedAircraft.length])

  // Create airport location object for map
  const airportLocation = useMemo(() => {
    if (!airport?.location) return null

    // Extract coordinates from GeoJSON Point
    const coordinates = airport.location.coordinates as [number, number]
    return {
      lat: coordinates[1], // GeoJSON is [lng, lat]
      lng: coordinates[0],
      name: airport.name,
      icao: airport.icao
    }
  }, [airport])

  // Filter operations based on selected date range and aircraft
  const filteredOperations = useMemo(() => {
    return filterOperations(apiOperations, selectedDateRange, selectedAircraft, airport?.timezone)
  }, [apiOperations, selectedDateRange, selectedAircraft, airport?.timezone])

  // Update selected operation with real flight path when trace data is available
  const selectedOperationWithTrace = useMemo(() => {
    if (!selectedOperation) {
      return null
    }

    // If trace is loading, return operation without coordinates to avoid showing old data
    if (traceLoading) {
      return {
        ...selectedOperation,
        flightPath: {
          ...selectedOperation.flightPath,
          coordinates: [], // Clear coordinates while loading
          segments: undefined,
          name: `${selectedOperation.registration} ${selectedOperation.operationType}`
        }
      }
    }

    // If we have trace data, use it
    if (tracePositions.length > 0) {
      // Convert trace positions to flight path coordinates
      const coordinates: [number, number][] = tracePositions.map(pos => [pos.lat, pos.lng])

      // Create altitude-based segments for styling
      const segments = createFlightPathSegments(tracePositions)

      return {
        ...selectedOperation,
        flightPath: {
          ...selectedOperation.flightPath,
          coordinates,
          segments,
          name: `${selectedOperation.registration} ${selectedOperation.operationType} - Real Path`
        }
      }
    }

    // No trace data available, return operation with empty coordinates
    return {
      ...selectedOperation,
      flightPath: {
        ...selectedOperation.flightPath,
        coordinates: [],
        segments: undefined,
        name: `${selectedOperation.registration} ${selectedOperation.operationType}`
      }
    }
  }, [selectedOperation, tracePositions, traceLoading])

  const handleOperationSelect = (operation: FlightOperation) => {
    setSelectedOperation(operation)
  }

  const handleOperationDeselect = () => {
    // Set to null for no selection
    setSelectedOperation(null)
  }

  const handleDateRangeChange = (range: DateRange) => {
    setSelectedDateRange(range)
    // Clear selection when date range changes
    setSelectedOperation(null)
  }

  const handleAircraftSelectionChange = (selection: AircraftSelection) => {
    setSelectedAircraft(selection)
    // Clear selection when aircraft selection changes
    setSelectedOperation(null)
  }

  // Show loading state only on initial load (wait for both airport and operations data)
  if (loading || airportLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading airport operations...</p>
        </div>
      </div>
    )
  }

  // Show error state (prioritize airport error, then operations error)
  if (airportError || error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading data: {airportError || error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <ResponsiveLayout
      operations={filteredOperations}
      allOperations={apiOperations}
      selectedOperation={selectedOperationWithTrace}
      onOperationSelect={handleOperationSelect}
      onOperationDeselect={handleOperationDeselect}
      selectedDateRange={selectedDateRange}
      onDateRangeChange={handleDateRangeChange}
      selectedAircraft={selectedAircraft}
      onAircraftSelectionChange={handleAircraftSelectionChange}
      airportLocation={airportLocation}
      timezone={airport?.timezone}
    />
  )
}
