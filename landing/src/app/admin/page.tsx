"use client"

import React, { useState } from 'react'
import Image from 'next/image'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Upload, Image as ImageIcon, Trash2, CheckCircle, AlertCircle, Loader2, Camera } from 'lucide-react'

interface UploadState {
  loading: boolean
  success: boolean
  error: string | null
  imageUrl: string | null
}

interface CurrentImageInfo {
  exists: boolean
  url: string | null
  loading: boolean
}

interface AirportInfo {
  icao: string
  name: string | null
  confirmed: boolean
  loading: boolean
  error: string | null
}

export default function AdminPage() {
  const [icao, setIcao] = useState('')
  const [airportInfo, setAirportInfo] = useState<AirportInfo>({
    icao: '',
    name: null,
    confirmed: false,
    loading: false,
    error: null
  })
  const [currentImage, setCurrentImage] = useState<CurrentImageInfo>({
    exists: false,
    url: null,
    loading: false
  })
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [uploadState, setUploadState] = useState<UploadState>({
    loading: false,
    success: false,
    error: null,
    imageUrl: null
  })

  const confirmIcao = async () => {
    if (!icao || icao.length !== 4) {
      setAirportInfo(prev => ({ ...prev, error: 'ICAO code must be exactly 4 characters' }))
      return
    }

    setAirportInfo(prev => ({ ...prev, loading: true, error: null }))
    setCurrentImage(prev => ({ ...prev, loading: true }))

    try {
      // Check if airport exists
      const airportResponse = await fetch(`https://api.skytraces.com/api/v1/airports/${icao.toUpperCase()}`)

      if (!airportResponse.ok) {
        throw new Error('Airport not found')
      }

      const airportData = await airportResponse.json()

      // Get current image info
      const imageResponse = await fetch(`https://api.skytraces.com/api/v1/airports/${icao.toUpperCase()}/image/info`)
      const imageData = await imageResponse.json()

      setAirportInfo({
        icao: icao.toUpperCase(),
        name: airportData.name,
        confirmed: true,
        loading: false,
        error: null
      })

      setCurrentImage({
        exists: imageData.has_image,
        url: imageData.image_url,
        loading: false
      })

      // Clear any previous upload state
      setUploadState({
        loading: false,
        success: false,
        error: null,
        imageUrl: null
      })

    } catch (error) {
      setAirportInfo({
        icao: '',
        name: null,
        confirmed: false,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to confirm airport'
      })
      setCurrentImage({
        exists: false,
        url: null,
        loading: false
      })
    }
  }

  const resetAirport = () => {
    setAirportInfo({
      icao: '',
      name: null,
      confirmed: false,
      loading: false,
      error: null
    })
    setCurrentImage({
      exists: false,
      url: null,
      loading: false
    })
    setSelectedFile(null)
    setPreviewUrl(null)
    setUploadState({
      loading: false,
      success: false,
      error: null,
      imageUrl: null
    })
    setIcao('')
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setUploadState(prev => ({ ...prev, error: 'Please select an image file' }))
        return
      }

      // Validate file size (10MB)
      if (file.size > 10 * 1024 * 1024) {
        setUploadState(prev => ({ ...prev, error: 'File size must be less than 10MB' }))
        return
      }

      setSelectedFile(file)
      setUploadState(prev => ({ ...prev, error: null }))

      // Create preview URL
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
    }
  }

  const handleUpload = async () => {
    if (!airportInfo.confirmed || !selectedFile) {
      setUploadState(prev => ({ ...prev, error: 'Please confirm airport and select an image' }))
      return
    }

    setUploadState(prev => ({ ...prev, loading: true, error: null, success: false }))

    try {
      const formData = new FormData()
      formData.append('file', selectedFile)

      const response = await fetch(`https://api.skytraces.com/api/v1/airports/${airportInfo.icao}/image`, {
        method: 'POST',
        body: formData,
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.detail || 'Upload failed')
      }

      setUploadState({
        loading: false,
        success: true,
        error: null,
        imageUrl: result.image_url
      })

      // Update current image info
      setCurrentImage({
        exists: true,
        url: result.image_url,
        loading: false
      })

      // Clear form
      setSelectedFile(null)
      setPreviewUrl(null)

    } catch (error) {
      setUploadState({
        loading: false,
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
        imageUrl: null
      })
    }
  }

  const handleDelete = async () => {
    if (!airportInfo.confirmed || !currentImage.exists) {
      setUploadState(prev => ({ ...prev, error: 'No image to delete' }))
      return
    }

    setUploadState(prev => ({ ...prev, loading: true, error: null, success: false }))

    try {
      const response = await fetch(`https://api.skytraces.com/api/v1/airports/${airportInfo.icao}/image`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.detail || 'Delete failed')
      }

      setUploadState({
        loading: false,
        success: true,
        error: null,
        imageUrl: null
      })

      // Update current image info
      setCurrentImage({
        exists: false,
        url: null,
        loading: false
      })

    } catch (error) {
      setUploadState({
        loading: false,
        success: false,
        error: error instanceof Error ? error.message : 'Delete failed',
        imageUrl: null
      })
    }
  }

  const resetForm = () => {
    setSelectedFile(null)
    setPreviewUrl(null)
    setUploadState({
      loading: false,
      success: false,
      error: null,
      imageUrl: null
    })
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Airport Image Admin</h1>
          <p className="text-gray-600">Upload and manage airport images for the Skytraces platform</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ImageIcon className="w-5 h-5" />
              Airport Image Management
            </CardTitle>
            <CardDescription>
              Upload high-quality images for airports. Images will be automatically optimized for web display.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* ICAO Input and Confirmation */}
            {!airportInfo.confirmed ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="icao">Airport ICAO Code</Label>
                  <div className="flex gap-2">
                    <Input
                      id="icao"
                      type="text"
                      placeholder="e.g., 4FL5"
                      value={icao}
                      onChange={(e) => setIcao(e.target.value.toUpperCase())}
                      maxLength={4}
                      className="uppercase flex-1"
                    />
                    <Button
                      onClick={confirmIcao}
                      disabled={airportInfo.loading || icao.length !== 4}
                    >
                      {airportInfo.loading ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Checking...
                        </>
                      ) : (
                        'Confirm'
                      )}
                    </Button>
                  </div>
                </div>

                {airportInfo.error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{airportInfo.error}</AlertDescription>
                  </Alert>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {/* Confirmed Airport Info */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-green-900">
                        {airportInfo.name || airportInfo.icao}
                      </h3>
                      <p className="text-sm text-green-700">ICAO: {airportInfo.icao}</p>
                    </div>
                    <Button variant="outline" size="sm" onClick={resetAirport}>
                      Change Airport
                    </Button>
                  </div>
                </div>

                {/* Current Image Display */}
                {currentImage.loading ? (
                  <div className="text-center py-8">
                    <Loader2 className="w-8 h-8 mx-auto animate-spin mb-2" />
                    <p className="text-sm text-gray-600">Loading current image...</p>
                  </div>
                ) : currentImage.exists && currentImage.url ? (
                  <div className="space-y-2">
                    <Label>Current Airport Image</Label>
                    <div className="relative h-48 rounded-lg border overflow-hidden bg-gray-100">
                      <Image
                        src={currentImage.url}
                        alt={`Current image for ${airportInfo.icao}`}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 100vw, 50vw"
                      />
                    </div>
                    <p className="text-sm text-gray-600">
                      This image will be replaced when you upload a new one.
                    </p>
                  </div>
                ) : (
                  <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                    <Camera className="w-12 h-12 mx-auto mb-2 text-gray-400" />
                    <p className="text-gray-600">No image currently uploaded</p>
                  </div>
                )}
              </div>
            )}

            {/* File Upload - Only show when airport is confirmed */}
            {airportInfo.confirmed && (
              <div className="space-y-2">
                <Label htmlFor="file">
                  {currentImage.exists ? 'Upload New Image (will replace current)' : 'Upload Airport Image'}
                </Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                  <input
                    id="file"
                    type="file"
                    accept="image/*"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                  <label htmlFor="file" className="cursor-pointer">
                    <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                    <p className="text-sm text-gray-600">
                      Click to select an image or drag and drop
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Supports JPEG, PNG, WebP (max 10MB)
                    </p>
                  </label>
                </div>
              </div>
            )}

            {/* Preview */}
            {previewUrl && (
              <div className="space-y-2">
                <Label>Preview</Label>
                <div className="relative h-48 rounded-lg border overflow-hidden">
                  <Image
                    src={previewUrl}
                    alt="Preview"
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedFile(null)
                      setPreviewUrl(null)
                    }}
                    className="absolute top-2 right-2"
                  >
                    Remove
                  </Button>
                </div>
              </div>
            )}

            {/* Status Messages */}
            {uploadState.error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{uploadState.error}</AlertDescription>
              </Alert>
            )}

            {uploadState.success && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Operation completed successfully!
                  {uploadState.imageUrl && (
                    <div className="mt-2">
                      <a 
                        href={uploadState.imageUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline text-sm"
                      >
                        View uploaded image
                      </a>
                    </div>
                  )}
                </AlertDescription>
              </Alert>
            )}

            {/* Action Buttons - Only show when airport is confirmed */}
            {airportInfo.confirmed && (
              <div className="flex gap-3">
                <Button
                  onClick={handleUpload}
                  disabled={uploadState.loading || !selectedFile}
                  className="flex-1"
                >
                  {uploadState.loading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4 mr-2" />
                      {currentImage.exists ? 'Replace Image' : 'Upload Image'}
                    </>
                  )}
                </Button>

                {currentImage.exists && (
                  <Button
                    variant="outline"
                    onClick={handleDelete}
                    disabled={uploadState.loading}
                  >
                    {uploadState.loading ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Trash2 className="w-4 h-4 mr-2" />
                    )}
                    Delete Image
                  </Button>
                )}

                <Button
                  variant="ghost"
                  onClick={resetForm}
                  disabled={uploadState.loading}
                >
                  Reset
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
