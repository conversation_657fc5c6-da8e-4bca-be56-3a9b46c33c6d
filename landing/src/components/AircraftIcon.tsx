import L from 'leaflet';

// Aircraft icon shapes inspired by tar1090
export const aircraftShapes = {
  default: {
    path: 'M16 1c-.17 0-.67.58-.9 1.03-.6 1.21-.6 1.15-.65 5.2-.04 2.97-.08 3.77-.18 3.9-.15.17-1.82 1.1-1.98 1.1-.08 0-.1-.25-.05-.83.03-.5.01-.92-.05-1.08-.1-.25-.13-.26-.71-.26-.82 0-.86.07-.78 1.5.03.6.08 1.17.11 1.25.05.12-.02.2-.25.33l-8 4.2c-.2.2-.18.1-.19 1.29 3.9-1.2 3.71-1.21 3.93-1.21.06 0 .1 0 .13.14.08.3.28.3.28-.04 0-.25.03-.27 1.16-.6.65-.2 1.22-.35 1.28-.35.05 0 .12.04.15.17.07.3.27.27.27-.08 0-.25.01-.27.7-.47.68-.1.98-.09 1.47-.1.18 0 .22 0 .26.18.06.34.22.35.27-.01.04-.2.1-.17 1.06-.14l1.07.02.05 4.2c.05 3.84.07 4.28.26 5.09.11.49.2.99.2 1.11 0 .19-.31.43-1.93 1.5l-1.93 1.26v1.02l4.13-.95.63 1.54c.05.07.12.09.19.09s.14-.02.19-.09l.63-1.54 4.13.95V29.3l-1.93-1.27c-1.62-1.06-1.93-1.3-1.93-1.49 0-.12.09-.62.2-1.11.19-.81.2-1.25.26-5.09l.05-4.2 1.07-.02c.96-.03 1.02-.05 1.06.14.05.36.21.35.27 0 .04-.17.08-.16.26-.16.49 0 .8-.02 1.48.1.68.2.69.21.69.46 0 .35.2.38.27.08.03-.13.1-.17.15-.17.06 0 .63.15 1.28.34 1.13.34 1.16.36 1.16.61 0 .35.2.34.28.04.03-.13.07-.14.13-.14.22 0 .03 0 3.93 1.2-.01-1.18.02-1.07-.19-1.27l-8-4.21c-.23-.12-.3-.21-.25-.33.03-.08.08-.65.11-1.25.08-1.43.04-1.5-.78-1.5-.58 0-.61.01-.71.26-.06.16-.08.58-.05 1.08.04.58.03.83-.05.83-.16 0-1.83-.93-1.98-1.1-.1-.13-.14-.93-.18-3.9-.05-4.05-.05-3.99-.65-5.2C16.67 1.58 16.17 1 16 1z',
    viewBox: '-1 -2 34 34',
    size: 32
  },
  small: {
    path: 'M9.5,15.75c-.21,0-.34-.17-.41-.51l-2.88.23v-.27c0-.78,0-1.11.28-1.13L9,13.1c-.31-1.86-.55-5-.59-5.55l-.08-.09H6.08L.25,6.54v-1A.43.43,0,0,1,.67,5l3.75-.27L5,4.45V3.53H4.73V2.7a.35.35,0,0,1,.34-.35h.07c.12-.52.26-.83.54-.83s.42.31.53.83h.07a.35.35,0,0,1,.34.35v.83H6.36v1l2-.08C8.42.81,9.09.25,9.49.25s1.09.55,1.12,4.21l2,.08v-1h-.25V2.7a.35.35,0,0,1,.34-.35h.07c.12-.52.26-.83.53-.83s.42.31.54.83h.07a.35.35,0,0,1,.34.35v.83H14v.92l.57.32L18.32,5a.42.42,0,0,1,.43.46v1L13,7.46H10.71l-.08.09c0,.56-.27,3.68-.59,5.55l2.46,1c.28,0,.28.35.28,1.13v.27l-2.88-.23C9.84,15.58,9.71,15.75,9.5,15.75Z',
    viewBox: '-3 -4 25 22',
    size: 25
  },
  jet: {
    path: 'M9,17.09l-3.51.61v-.3c0-.65.11-1,.33-1.09L8.5,15a5.61,5.61,0,0,1-.28-1.32l-.53-.41-.1-.69H7.12l0-.21a7.19,7.19,0,0,1-.15-2.19L.24,9.05V8.84c0-1.1.51-1.15.61-1.15L7.8,7.18V2.88C7.8.64,8.89.3,8.93.28L9,.26l.07,0s1.13.36,1.13,2.6v4.3l7,.51c.09,0,.59.06.59,1.15v.21l-6.69,1.16a7.17,7.17,0,0,1-.15,2.19l0,.21h-.47l-.1.69-.53.41A5.61,5.61,0,0,1,9.5,15l2.74,1.28c.2.07.31.43.31,1.08v.3Z',
    viewBox: '-2 -2.4 22 22',
    size: 22
  }
};

// Get aircraft icon based on category or default
export function getAircraftShape(category?: string): typeof aircraftShapes.default {
  // Map aircraft categories to shapes
  switch (category) {
    case 'A1': // Light aircraft
    case 'A2':
      return aircraftShapes.small;
    case 'A3': // Large aircraft
    case 'A4':
    case 'A5':
    case 'A6':
    case 'A7':
      return aircraftShapes.default;
    case 'B1': // Glider
    case 'B2':
      return aircraftShapes.small;
    case 'C1': // Helicopter
    case 'C2':
    case 'C3':
      return aircraftShapes.small;
    default:
      return aircraftShapes.jet;
  }
}

// Create aircraft icon with rotation and color
export function createAircraftIcon(
  track?: number,
  altitude?: number,
  category?: string,
  selected: boolean = false
): L.DivIcon {
  const shape = getAircraftShape(category);
  const rotation = track || 0;
  
  // Color based on altitude (similar to tar1090)
  let color = '#1f77b4'; // Default blue
  if (altitude !== undefined && altitude !== null) {
    if (altitude === 0 || altitude === -1000) { // Ground
      color = '#8B4513'; // Brown for ground
    } else if (altitude < 1000) {
      color = '#ff7f0e'; // Orange for low altitude
    } else if (altitude < 10000) {
      color = '#2ca02c'; // Green for medium altitude
    } else if (altitude < 25000) {
      color = '#d62728'; // Red for high altitude
    } else {
      color = '#9467bd'; // Purple for very high altitude
    }
  }

  if (selected) {
    color = '#ffff00'; // Yellow for selected aircraft
  }

  const svgContent = `
    <svg width="${shape.size}" height="${shape.size}" viewBox="${shape.viewBox}" 
         style="transform: rotate(${rotation}deg);">
      <path d="${shape.path}" 
            fill="${color}" 
            stroke="#000000" 
            stroke-width="0.5" 
            opacity="0.9"/>
    </svg>
  `;

  return L.divIcon({
    html: svgContent,
    className: 'aircraft-icon',
    iconSize: [shape.size, shape.size],
    iconAnchor: [shape.size / 2, shape.size / 2],
  });
}

// CSS for aircraft icons (to be added to global styles)
export const aircraftIconCSS = `
.aircraft-icon {
  background: none !important;
  border: none !important;
  transition: transform 0.3s ease;
}

.aircraft-icon:hover {
  transform: scale(1.2);
  z-index: 1000;
}

.aircraft-icon svg {
  filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.3));
}
`;
