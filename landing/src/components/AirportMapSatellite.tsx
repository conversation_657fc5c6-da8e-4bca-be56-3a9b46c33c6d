'use client';

import { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMap } from 'react-leaflet';
import L from 'leaflet';
import { Airport } from '@/types/api';
import { useAircraftPositions, useMapBounds } from '@/hooks/useAircraftPositions';
import { createAircraftIcon } from '@/components/AircraftIcon';
import { usePersistentMap } from '@/hooks/use-persistent-map';
import MapControls from '@/components/MapControls';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as unknown as { _getIconUrl: unknown })._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface AirportMapSatelliteProps {
  airport: Airport;
  className?: string;
}

// Component to handle map persistence and bounds for satellite view
function MapControllerSatellite({ airport }: { airport: Airport }) {
  const map = useMap();
  const { handleMapReady, savedCenter, savedZoom } = usePersistentMap('satellite');
  const hasSetInitialView = useRef(false);

  useEffect(() => {
    // Set up map persistence
    const cleanup = handleMapReady(map);
    return cleanup;
  }, [map, handleMapReady]);

  useEffect(() => {
    if (!airport.location || hasSetInitialView.current) return;

    // If we have saved position, use it; otherwise fit to airport bounds
    if (savedCenter && savedZoom) {
      map.setView(savedCenter, savedZoom);
      hasSetInitialView.current = true;
      return;
    }

    const bounds = L.latLngBounds([]);

    // Add airport location to bounds
    if (airport.location.type === 'Point') {
      const [lng, lat] = airport.location.coordinates as number[];
      bounds.extend([lat, lng]);
    }

    // Add runway centerlines to bounds
    airport.runways.forEach(runway => {
      if (runway.centerline && runway.centerline.type === 'LineString') {
        const coordinates = runway.centerline.coordinates as number[][];
        coordinates.forEach(([lng, lat]) => {
          bounds.extend([lat, lng]);
        });
      }
    });

    // If we have valid bounds, fit the map to them with tighter padding and higher max zoom for satellite detail
    if (bounds.isValid()) {
      map.fitBounds(bounds, {
        padding: [20, 20], // Tighter padding for closer view
        maxZoom: 18 // Higher zoom level for satellite detail
      });
      hasSetInitialView.current = true;
    }
  }, [airport, map, savedCenter, savedZoom]);

  return null;
}

// Component to display aircraft positions with permanent labels
function AircraftMarkersSatellite() {
  const map = useMap();
  const bounds = useMapBounds(map);

  const { positions } = useAircraftPositions(bounds, {
    refreshInterval: 2000, // 2 seconds
    lookbackSeconds: 7,
    enabled: true
  });

  return (
    <>
      {positions.map((aircraft) => {
        if (!aircraft.h3_index) return null;

        try {
          // Import h3-js to convert H3 index to coordinates
          // eslint-disable-next-line @typescript-eslint/no-require-imports
          const h3 = require('h3-js');

          // Use cellToLatLng function which returns [lat, lng]
          const coords = h3.cellToLatLng(aircraft.h3_index);
          const [lat, lng] = coords;

          // Validate coordinates
          if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
            console.warn(`Invalid coordinates for aircraft ${aircraft.hex}: ${lat}, ${lng}`);
            return null;
          }

          const icon = createAircraftIcon(
            aircraft.track,
            aircraft.alt_baro,
            aircraft.category,
            false
          );

          // Create permanent label content
          const labelContent = `
            <div class="aircraft-label" style="
              background: rgba(0,0,0,0.8);
              color: white;
              padding: 2px 6px;
              border-radius: 4px;
              font-size: 11px;
              font-weight: 500;
              white-space: nowrap;
              pointer-events: none;
              margin-top: 20px;
              text-align: center;
              box-shadow: 0 1px 3px rgba(0,0,0,0.3);
            ">
              ${aircraft.flight || aircraft.hex.slice(-4)}<br>
              ${aircraft.alt_baro !== undefined ? `${aircraft.alt_baro}ft` : 'N/A'}
            </div>
          `;

          const labelIcon = L.divIcon({
            html: labelContent,
            className: 'aircraft-label-icon',
            iconSize: [60, 30],
            iconAnchor: [30, -10],
          });

          return (
            <div key={aircraft.hex}>
              {/* Aircraft icon */}
              <Marker
                position={[lat, lng]}
                icon={icon}
                interactive={false}
              />
              {/* Permanent label */}
              <Marker
                position={[lat, lng]}
                icon={labelIcon}
                interactive={false}
              />
            </div>
          );
        } catch (err) {
          console.error('Error converting H3 index to coordinates:', err);
          return null;
        }
      })}
    </>
  );
}

export default function AirportMapSatellite({ airport, className = '' }: AirportMapSatelliteProps) {
  const mapRef = useRef<L.Map | null>(null);

  if (!airport.location || airport.location.type !== 'Point') {
    return (
      <div className={`bg-gray-100 flex items-center justify-center ${className}`}>
        <p className="text-gray-500">Airport location not available</p>
      </div>
    );
  }

  const [lng, lat] = airport.location.coordinates as number[];

  return (
    <div className={className}>
      <MapContainer
        center={[lat, lng]}
        zoom={15} // Start with higher zoom for satellite view
        style={{ height: '100%', width: '100%' }}
        ref={mapRef}
      >
        {/* Satellite tile layer */}
        <TileLayer
          attribution='&copy; <a href="https://www.esri.com/">Esri</a>'
          url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
          maxZoom={20}
        />

        {/* Runway centerlines with labels */}
        {airport.runways.map((runway, index) => {
          if (!runway.centerline || runway.centerline.type !== 'LineString') {
            return null;
          }

          const coordinates = runway.centerline.coordinates as number[][];
          const latLngCoordinates = coordinates.map(([lng, lat]) => [lat, lng] as [number, number]);

          // Get the start position for the runway label
          const startPosition = latLngCoordinates[0];

          const runwayLabelContent = `
            <div style="
              background: rgba(255,255,255,0.95);
              color: #1e293b;
              padding: 3px 8px;
              border-radius: 4px;
              font-size: 13px;
              font-weight: 700;
              white-space: nowrap;
              pointer-events: none;
              border: 2px solid #3b82f6;
              box-shadow: 0 2px 6px rgba(0,0,0,0.3);
            ">
              ${runway.name}
            </div>
          `;

          const runwayLabelIcon = L.divIcon({
            html: runwayLabelContent,
            className: 'runway-label-icon',
            iconSize: [50, 25],
            iconAnchor: [25, 12],
          });

          return (
            <div key={index}>
              {/* Runway centerline - more prominent on satellite */}
              <Polyline
                positions={latLngCoordinates}
                color="#3b82f6"
                weight={4}
                opacity={0.9}
              />
              {/* Runway label at start */}
              <Marker
                position={startPosition}
                icon={runwayLabelIcon}
                interactive={false}
              />
            </div>
          );
        })}

        <MapControllerSatellite airport={airport} />
        <MapControls />
        <AircraftMarkersSatellite />
      </MapContainer>
    </div>
  );
}
