'use client';

import { ScrollArea } from '@/components/ui/scroll-area';
import { AirportTransition } from '@/types/api';
import { convertToAirportTime, formatTimeOnly, formatDateHeader, dateKeyToAirportTime } from '@/lib/utils';
import { PlaneTakeoff, PlaneLanding, Clock } from 'lucide-react';
import { useEffect, useRef } from 'react';

interface GroupedTransition {
  date: string;
  transitions: AirportTransition[];
}

interface TransitionsHorizontalProps {
  groupedTransitions: GroupedTransition[];
  loading: boolean;
  error: string | null;
}

export default function TransitionsHorizontal({
  groupedTransitions,
  loading,
  error
}: TransitionsHorizontalProps) {
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to the right (most recent) when new data arrives
  useEffect(() => {
    if (scrollAreaRef.current && groupedTransitions.length > 0) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollLeft = scrollContainer.scrollWidth;
      }
    }
  }, [groupedTransitions]);

  // Reverse the order so oldest is on left, newest on right
  const reversedGroups = [...groupedTransitions].reverse();

  // Create date groups with alternating backgrounds
  const createDateGroups = () => {
    return reversedGroups.map((group, groupIndex) => ({
      ...group,
      // Reverse transitions within each group so newest are on the right
      transitions: [...group.transitions].reverse(),
      isEvenGroup: groupIndex % 2 === 0
    }));
  };

  const dateGroups = createDateGroups();

  return (
    <div className="bg-slate-50">
      <div className="px-4 pt-2 pb-1">
          {error && (
            <div className="p-3 border border-red-200 bg-red-50 text-sm text-red-600 mb-4 rounded-lg">
              {error}
            </div>
          )}

          {loading && groupedTransitions.length === 0 && (
            <div className="flex space-x-3">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="flex-shrink-0 w-28 h-28 animate-pulse bg-slate-200 rounded-lg border shadow-sm" />
              ))}
            </div>
          )}

          {groupedTransitions.length === 0 && !loading && !error && (
            <div className="p-8 text-center">
              <Clock className="h-8 w-8 text-slate-300 mx-auto mb-2" />
              <p className="text-slate-500 text-sm">No transitions found</p>
            </div>
          )}

          {dateGroups.length > 0 && (
            <ScrollArea className="w-full" ref={scrollAreaRef}>
              <div className="flex">
                {dateGroups.map((group, groupIndex) => (
                <div
                  key={`group-${groupIndex}`}
                  className={`flex-shrink-0 ${
                    group.isEvenGroup ? 'bg-white' : 'bg-muted'
                  } first:rounded-l-lg last:rounded-r-lg ${
                    groupIndex > 0 ? 'ml-2' : ''
                  }`}
                >
                  {/* Date header - larger for TV readability */}
                  <div className="px-4 py-3 border-b border-slate-300">
                    <h3 className="text-sm font-bold text-slate-700 text-center whitespace-nowrap">
                      {formatDateHeader(dateKeyToAirportTime(group.date))}
                    </h3>
                  </div>

                  {/* Transitions for this date */}
                  <div className="flex p-2 space-x-2">
                    {group.transitions.map((transition, transitionIndex) => {
                      const airportTime = convertToAirportTime(transition.time);
                      const isTakeoff = transition.flight_phase === 'takeoff';

                      return (
                        <div
                          key={`transition-${groupIndex}-${transitionIndex}`}
                          className="flex-shrink-0 w-28 hover:shadow-lg transition-all duration-200 bg-white border-2 rounded-lg overflow-hidden shadow-sm"
                          style={{
                            borderColor: isTakeoff ? '#10b981' : '#3b82f6'
                          }}
                        >
                          {/* Compact vertical layout optimized for TV viewing */}
                          <div className="flex flex-col">
                            {/* Time - large font for TV readability */}
                            <div className="text-lg font-mono font-bold text-slate-900 text-center leading-tight py-1.5">
                              {formatTimeOnly(airportTime)}
                            </div>

                            {/* Type badge - larger text for TV */}
                            <div
                              className={`text-sm font-bold text-center leading-tight py-1 ${
                                isTakeoff
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-blue-100 text-blue-800'
                              }`}
                            >
                              {isTakeoff ? 'TAKEOFF' : 'LANDING'}
                            </div>

                            {/* Flight with icon - larger for TV */}
                            <div className="flex items-center justify-center gap-1.5 py-1 px-1">
                              {isTakeoff ? (
                                <PlaneTakeoff className="h-4 w-4 text-green-600 flex-shrink-0" />
                              ) : (
                                <PlaneLanding className="h-4 w-4 text-blue-600 flex-shrink-0" />
                              )}
                              <span className="font-semibold text-slate-900 text-sm truncate leading-tight">
                                {transition.flight || transition.hex.slice(-4)}
                              </span>
                            </div>

                            {/* Runway - larger text for TV readability */}
                            <div className="text-sm font-medium text-slate-700 text-center leading-tight py-1 bg-slate-100">
                              RWY {transition.runway_name}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
    </div>
  );
}
