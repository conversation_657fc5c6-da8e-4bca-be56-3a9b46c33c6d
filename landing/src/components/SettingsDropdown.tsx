'use client';

import { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Settings, Check, Save, Headphones } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useSettings, FontSize } from '@/contexts/SettingsContext';

const fontSizeOptions: { value: FontSize; label: string; description: string }[] = [
  { value: 'small', label: 'Small', description: 'Compact text for close viewing' },
  { value: 'medium', label: 'Medium', description: 'Standard text size' },
  { value: 'large', label: 'Large', description: 'Larger text for TV displays' },
];

/**
 * Settings dropdown component with font size controls and save functionality
 * Renders above maps with proper z-index layering
 */
export default function SettingsDropdown() {
  const { settings, updateFontSize, saveSettings } = useSettings();
  const [isOpen, setIsOpen] = useState(false);
  const params = useParams();
  const router = useRouter();
  const icao = params.icao as string;

  const handleFontSizeChange = (fontSize: FontSize) => {
    updateFontSize(fontSize);
  };

  const handleSave = () => {
    saveSettings();
    setIsOpen(false);
  };

  const handlePlaybackConsole = () => {
    if (icao) {
      router.push(`/playback/${icao}`);
    }
    setIsOpen(false);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 text-slate-400 hover:text-white hover:bg-slate-700"
        >
          <Settings className="h-4 w-4" />
          <span className="sr-only">Open settings</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64 z-[9999]">
        <DropdownMenuLabel>Display Settings</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <div className="px-2 py-1">
          <div className="text-sm font-medium mb-2">Font Size</div>
          {fontSizeOptions.map((option) => (
            <DropdownMenuItem
              key={option.value}
              onClick={() => handleFontSizeChange(option.value)}
              className="flex items-center justify-between cursor-pointer"
            >
              <div className="flex flex-col">
                <span className="font-medium">{option.label}</span>
                <span className="text-xs text-muted-foreground">
                  {option.description}
                </span>
              </div>
              {settings.fontSize === option.value && (
                <Check className="h-4 w-4 text-blue-600" />
              )}
            </DropdownMenuItem>
          ))}
        </div>
        
        <DropdownMenuSeparator />

        {icao && (
          <>
            <DropdownMenuItem onClick={handlePlaybackConsole} className="cursor-pointer">
              <Headphones className="h-4 w-4 mr-2" />
              Audio Playback Console
            </DropdownMenuItem>
            <DropdownMenuSeparator />
          </>
        )}

        <DropdownMenuItem onClick={handleSave} className="cursor-pointer">
          <Save className="h-4 w-4 mr-2" />
          Save Settings
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
