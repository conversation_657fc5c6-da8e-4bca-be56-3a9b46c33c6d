"use client"

import { RideCard } from "./RideCard"
import { rideCategories } from "@/data/mockRides"
import { Button } from "@/components/ui/button"
import { Clock, User, ChevronDown } from "lucide-react"

export function UberContent() {

  return (
    <>
      {/* Header */}
      <div className="px-6 pb-4 pt-2">
        <div className="flex items-center justify-between mb-4">
          <Button variant="ghost" size="sm" className="flex items-center gap-2">
            <Clock className="w-4 h-4" />
            <span>Pick up now</span>
            <ChevronDown className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm" className="flex items-center gap-2">
            <User className="w-4 h-4" />
            <span>For me</span>
            <ChevronDown className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Ride Categories and Cards */}
      <div className="px-6 pb-6">
        {rideCategories.map((category, categoryIndex) => (
          <div key={categoryIndex} className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              {category.title}
            </h2>
            <div className="space-y-3">
              {category.rides.map((ride) => (
                <RideCard
                  key={ride.id}
                  ride={ride}
                />
              ))}
            </div>
          </div>
        ))}
      </div>


    </>
  )
}
