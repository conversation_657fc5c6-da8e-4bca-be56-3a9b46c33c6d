'use client';

import { useRef } from 'react';
import { RecordingFile } from '@/types/api';
import { GripVertical } from 'lucide-react';
import { cn, timeUtils } from '@/lib/utils';
import { useTimelineInteraction } from '@/hooks/useTimelineInteraction';

interface HourlyTimelineProps {
  recordings: RecordingFile[];
  currentTime: number;
  className?: string;
}

export function HourlyTimeline({
  recordings,
  currentTime,
  className
}: HourlyTimelineProps) {
  const timelineRef = useRef<HTMLDivElement>(null);

  // Use centralized time utilities
  const { timeToSeconds, secondsToTimeShort: secondsToTime } = timeUtils;

  // Get current hour
  const currentHour = Math.floor(currentTime / 3600);
  const hourStart = currentHour * 3600;
  const hourEnd = (currentHour + 1) * 3600;

  // Filter recordings for current hour
  const hourRecordings = recordings.filter(recording => {
    const recordingStart = timeToSeconds(recording.start_time);
    const recordingEnd = recordingStart + (recording.duration_ms / 1000);
    return recordingEnd > hourStart && recordingStart < hourEnd;
  });

  // Calculate 5-minute window bounds
  const WINDOW_DURATION = 5 * 60; // 5 minutes in seconds
  const HALF_WINDOW = WINDOW_DURATION / 2;
  const windowStart = Math.max(hourStart, currentTime - HALF_WINDOW);
  const windowEnd = Math.min(hourEnd, currentTime + HALF_WINDOW);

  // Use centralized timeline interaction logic
  const {
    isDragging,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
  } = useTimelineInteraction({
    timelineRef,
    startTime: hourStart,
    duration: 3600, // 1 hour in seconds
  });

  return (
    <div className={cn("space-y-2", className)}>
      {/* Hour labels */}
      <div className="flex justify-between text-sm text-slate-600">
        <span>{secondsToTime(hourStart)}</span>
        <span>{secondsToTime(hourEnd)}</span>
      </div>

      {/* Timeline bar */}
      <div
        ref={timelineRef}
        className={cn(
          "relative h-8 bg-slate-100 rounded select-none touch-none",
          isDragging ? "cursor-grabbing" : "cursor-grab"
        )}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* 5-minute window highlight */}
        <div
          className={cn(
            "absolute top-0 bottom-0 bg-blue-100 rounded border border-blue-200 flex items-center justify-center transition-colors touch-none",
            isDragging ? "cursor-grabbing bg-blue-200" : "cursor-grab hover:bg-blue-150"
          )}
          style={{
            left: `${((windowStart - hourStart) / 3600) * 100}%`,
            width: `${((windowEnd - windowStart) / 3600) * 100}%`
          }}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
        >
          <GripVertical className="h-3 w-3 text-blue-400 opacity-60" />
        </div>

        {/* Recording markers */}
        {hourRecordings.map((recording) => {
          const startTime = timeToSeconds(recording.start_time);
          const duration = recording.duration_ms / 1000;
          const leftPercent = Math.max(0, ((startTime - hourStart) / 3600) * 100);
          const rightPercent = Math.min(100, ((startTime + duration - hourStart) / 3600) * 100);
          const widthPercent = rightPercent - leftPercent;

          if (widthPercent <= 0) return null;

          return (
            <div
              key={recording.filename}
              className="absolute top-1 bottom-1 bg-blue-500 rounded-sm"
              style={{
                left: `${leftPercent}%`,
                width: `${widthPercent}%`,
                minWidth: '1px'
              }}
            />
          );
        })}
      </div>
    </div>
  );
}
