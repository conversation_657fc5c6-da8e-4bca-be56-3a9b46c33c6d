'use client';

import { Play, Pause, SkipBack, Ski<PERSON>Forward } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { usePlayback } from '@/contexts/PlaybackContext';
import { useAudioPlayback } from '@/hooks/useAudioPlayback';

export function PlaybackControls({
  className
}: { className?: string }) {
  // Use centralized state and audio playback logic
  const { state, jumpTime } = usePlayback();
  const { isPlaying, isLoading } = state;
  const { togglePlayback } = useAudioPlayback();

  // Navigation functions
  const handleTimeJump = (seconds: number) => {
    jumpTime(seconds);
  };

  return (
    <Card className={cn("", className)}>
      <CardContent className="py-6">
        <div className="flex items-center justify-center space-x-6">
          {/* -15 seconds */}
          <Button
            variant="outline"
            size="lg"
            onClick={() => handleTimeJump(-15)}
            className="h-12 w-12 rounded-full"
          >
            <SkipBack className="h-5 w-5" />
          </Button>

          {/* Play/Pause */}
          <Button
            variant="default"
            size="lg"
            onClick={togglePlayback}
            disabled={isLoading}
            className="h-16 w-16 rounded-full"
          >
            {isLoading ? (
              <div className="h-6 w-6 animate-spin rounded-full border-2 border-white border-t-transparent" />
            ) : isPlaying ? (
              <Pause className="h-6 w-6" />
            ) : (
              <Play className="h-6 w-6 ml-1" />
            )}
          </Button>

          {/* +15 seconds */}
          <Button
            variant="outline"
            size="lg"
            onClick={() => handleTimeJump(15)}
            className="h-12 w-12 rounded-full"
          >
            <SkipForward className="h-5 w-5" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
