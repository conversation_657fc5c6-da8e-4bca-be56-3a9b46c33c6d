'use client';

import { useEffect, useRef } from 'react';
import { RecordingFile } from '@/types/api';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { cn, timeUtils } from '@/lib/utils';

interface AudioZoomedTimelineProps {
  recordings: RecordingFile[];
  currentTime: number;
  onTimeChange: (time: number) => void;
  className?: string;
}

export function AudioZoomedTimeline({ 
  recordings, 
  currentTime, 
  onTimeChange, 
  className 
}: AudioZoomedTimelineProps) {
  const timelineRef = useRef<HTMLDivElement>(null);

  // Constants for the 15-minute window
  const WINDOW_DURATION = 5 * 60; // 15 minutes in seconds
  const HALF_WINDOW = WINDOW_DURATION / 2; // 7.5 minutes

  // Use centralized time utilities
  const { timeToSeconds, secondsToTime } = timeUtils;

  // Calculate the window bounds centered on current time with smooth transitions
  const windowStart = Math.max(0, currentTime - HALF_WINDOW);
  const windowEnd = Math.min(86400, currentTime + HALF_WINDOW); // 86400 = 24 hours in seconds

  // Smooth scrolling effect when currentTime changes
  useEffect(() => {
    if (timelineRef.current) {
      // Add a subtle transition effect to the timeline container
      timelineRef.current.style.transition = 'all 0.3s ease-out';
    }
  }, [currentTime]);

  // Filter recordings that are visible in the current window
  const visibleRecordings = recordings.filter(recording => {
    const startTime = timeToSeconds(recording.start_time);
    const endTime = startTime + (recording.duration_ms / 1000);
    return endTime > windowStart && startTime < windowEnd;
  });

  // Generate minute markers for the zoomed view
  const generateMinuteMarkers = () => {
    const markers = [];
    const startMinute = Math.floor(windowStart / 60) * 60;
    const endMinute = Math.ceil(windowEnd / 60) * 60;
    
    for (let time = startMinute; time <= endMinute; time += 60) {
      if (time >= windowStart && time <= windowEnd) {
        markers.push(time);
      }
    }
    return markers;
  };

  const minuteMarkers = generateMinuteMarkers();



  return (
    <TooltipProvider>
      <Card className={cn("", className)}>
        <CardHeader className="pb-3">
          <div className="text-center">
            <div className="text-3xl font-mono font-bold text-slate-900">
              {secondsToTime(currentTime)}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Main timeline container */}
          <div
            ref={timelineRef}
            className="relative h-16 bg-slate-100 rounded-lg overflow-hidden"
          >
            {/* Background shading for context regions */}
            <div className="absolute inset-0">
              {/* Center active region */}
              <div className="absolute top-0 bottom-0 bg-white/80 border-l-2 border-r-2 border-blue-200"
                style={{
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '2px'
                }}
              />

              {/* Left context region (preceding 7.5 minutes) */}
              <div
                className="absolute top-0 bottom-0 bg-gradient-to-r from-slate-300/30 to-slate-200/20 transition-all duration-300"
                style={{
                  left: '0%',
                  width: '50%'
                }}
              />

              {/* Right context region (following 7.5 minutes) */}
              <div
                className="absolute top-0 bottom-0 bg-gradient-to-l from-slate-300/30 to-slate-200/20 transition-all duration-300"
                style={{
                  left: '50%',
                  width: '50%'
                }}
              />
            </div>

            {/* Minute grid lines */}
            <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
              {minuteMarkers.map((time) => (
                <div
                  key={time}
                  className="absolute top-0 bottom-0 border-l border-slate-300"
                  style={{ left: `${((time - windowStart) / WINDOW_DURATION) * 100}%` }}
                >
                  <span className="absolute top-1 -translate-x-1/2 text-xs text-slate-300 font-mono">
                    {secondsToTime(time).slice(0, 5)} {/* Show HH:MM */}
                  </span>
                </div>
              ))}
            </div>

            {/* Recording markers */}
            <div className="absolute top-0 left-0 w-full h-full pointer-events-none z-10">
              {visibleRecordings.map((recording) => {
                const startTime = timeToSeconds(recording.start_time);
                const duration = recording.duration_ms / 1000;
                const leftPercent = Math.max(0, ((startTime - windowStart) / WINDOW_DURATION) * 100);
                const rightPercent = Math.min(100, ((startTime + duration - windowStart) / WINDOW_DURATION) * 100);
                const widthPercent = rightPercent - leftPercent;
                
                if (widthPercent <= 0) return null;
                
                const isActive = currentTime >= startTime && currentTime < startTime + duration;
                
                return (
                  <Tooltip key={recording.filename}>
                    <TooltipTrigger asChild>
                      <div
                        className={cn(
                          "absolute top-2 bottom-2 rounded-md transition-all duration-300 pointer-events-auto cursor-pointer border border-blue-300/50",
                          isActive
                            ? "bg-blue-600 shadow-lg ring-2 ring-blue-400/50 scale-105"
                            : "bg-blue-500 hover:bg-blue-600 hover:shadow-md hover:scale-102"
                        )}
                        style={{
                          left: `${leftPercent}%`,
                          width: `${widthPercent}%`,
                          minWidth: '2px'
                        }}

                        onClick={(e) => {
                          e.stopPropagation();
                          onTimeChange(startTime);
                        }}
                      />
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-sm">
                        <p className="font-medium">{recording.filename}</p>
                        <p>Start: {secondsToTime(startTime)}</p>
                        <p>Duration: {(recording.duration_ms / 1000).toFixed(1)}s</p>
                        <p>Size: {(recording.size_bytes / 1024).toFixed(1)} KB</p>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                );
              })}
            </div>

            {/* Current time indicator */}
            <div
              className="absolute top-0 bottom-0 w-1 bg-red-500 z-20 pointer-events-none transition-all duration-200 shadow-lg"
              style={{ left: `${((currentTime - windowStart) / WINDOW_DURATION) * 100}%` }}
            >
              <div className="absolute -top-2 -left-1.5 w-4 h-4 bg-red-500 rounded-full shadow-lg border-2 border-white"></div>
              <div className="absolute -bottom-2 -left-1.5 w-4 h-4 bg-red-500 rounded-full shadow-lg border-2 border-white"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}
