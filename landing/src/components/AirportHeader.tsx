'use client';

import { useEffect, useState } from 'react';
import { Airport } from '@/types/api';
import { getCurrentAirportTime, formatTimeOnly } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { Clock } from 'lucide-react';
import SettingsDropdown from '@/components/SettingsDropdown';

interface AirportHeaderProps {
  airport: Airport | null;
  totalTransitions: number;
  aircraftCount: number;
  lastUpdated: Date | null;
  loading: boolean;
}

export default function AirportHeader({
  airport,
  totalTransitions,
  aircraftCount, // eslint-disable-line @typescript-eslint/no-unused-vars
  lastUpdated, // eslint-disable-line @typescript-eslint/no-unused-vars
  loading // eslint-disable-line @typescript-eslint/no-unused-vars
}: AirportHeaderProps) {
  const [, setCurrentTime] = useState<Date>(new Date());
  const isMobile = useIsMobile();

  // Update clock every second
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const airportTime = airport ? getCurrentAirportTime() : null;

  return (
    <div className="bg-slate-900 text-white shadow-lg border-b border-slate-700">
      <div className="flex items-center justify-between px-4 py-2">
        {/* Left Section - Company and Airport */}
        <div className="flex items-center space-x-4">
          <div className="text-blue-400 font-bold text-xl">skytraces</div>
          <div className="h-6 w-px bg-slate-600"></div>
          <div>
            <div className="flex items-center space-x-2">
              <span className="font-bold text-2xl">{airport?.icao || 'Loading...'}</span>
              {!isMobile && airport?.name && (
                <span className="text-slate-300 text-base truncate max-w-64">
                  {airport.name}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Right Section - Stats, Time, and Settings */}
        <div className="flex items-center space-x-4">
          {/* Operations Today */}
          {!isMobile && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-slate-400">Operations Today</span>
              <span className="text-lg font-bold">{totalTransitions}</span>
            </div>
          )}

          {/* Time Display */}
          <div className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-slate-400" />
            <div className="flex items-center space-x-1">
              <span className="text-lg font-mono font-bold">
                {airportTime ? formatTimeOnly(airportTime) : '--:--'}
              </span>
              <span className="text-sm text-slate-400">GMT -4</span>
            </div>
          </div>

          {/* Settings Dropdown */}
          <SettingsDropdown />
        </div>
      </div>
    </div>
  );
}
