'use client';

import { useState, useMemo } from 'react';
import { RecordingFile } from '@/types/api';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Clock } from 'lucide-react';
import { cn, timeUtils } from '@/lib/utils';

interface TimePickerListProps {
  recordings: RecordingFile[];
  currentTime: number;
  onTimeChange: (time: number) => void;
  className?: string;
}

interface TimeSlot {
  time: number;
  timeString: string;
  recordingCount: number;
  totalDuration: number;
  recordings: RecordingFile[];
}

export function TimePickerList({ 
  recordings, 
  currentTime, 
  onTimeChange, 
  className 
}: TimePickerListProps) {
  const [selectedSlot, setSelectedSlot] = useState<number | null>(null);

  // Use centralized time utilities
  const { timeToSeconds, secondsToTimeShort: secondsToTime } = timeUtils;

  // Generate 5-minute time slots with recording data
  const timeSlots = useMemo(() => {
    const slots: TimeSlot[] = [];
    
    // Generate slots for every 5 minutes (288 slots in 24 hours)
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 5) {
        const slotTime = hour * 3600 + minute * 60;
        const slotEndTime = slotTime + 300; // 5 minutes later
        
        // Find recordings that overlap with this time slot
        const slotRecordings = recordings.filter(recording => {
          const recordingStart = timeToSeconds(recording.start_time);
          const recordingEnd = recordingStart + (recording.duration_ms / 1000);
          
          // Check if recording overlaps with the 5-minute slot
          return recordingEnd > slotTime && recordingStart < slotEndTime;
        });

        const totalDuration = slotRecordings.reduce((sum, recording) => {
          const recordingStart = timeToSeconds(recording.start_time);
          const recordingEnd = recordingStart + (recording.duration_ms / 1000);
          
          // Calculate overlap duration
          const overlapStart = Math.max(slotTime, recordingStart);
          const overlapEnd = Math.min(slotEndTime, recordingEnd);
          const overlapDuration = Math.max(0, overlapEnd - overlapStart);
          
          return sum + overlapDuration;
        }, 0);

        slots.push({
          time: slotTime,
          timeString: secondsToTime(slotTime),
          recordingCount: slotRecordings.length,
          totalDuration: Math.round(totalDuration),
          recordings: slotRecordings
        });
      }
    }
    
    return slots;
  }, [recordings, timeToSeconds, secondsToTime]);

  // Find current time slot
  const currentSlotIndex = useMemo(() => {
    return timeSlots.findIndex(slot => {
      const slotEnd = slot.time + 300; // 5 minutes
      return currentTime >= slot.time && currentTime < slotEnd;
    });
  }, [timeSlots, currentTime]);

  // Handle slot selection
  const handleSlotClick = (slot: TimeSlot) => {
    setSelectedSlot(slot.time);
    onTimeChange(slot.time);
  };

  // Filter to show only slots with recordings or around current time
  const relevantSlots = useMemo(() => {
    const currentHour = Math.floor(currentTime / 3600);
    
    return timeSlots.filter(slot => {
      const slotHour = Math.floor(slot.time / 3600);
      
      // Show slots with recordings
      if (slot.recordingCount > 0) return true;
      
      // Show slots within 2 hours of current time
      if (Math.abs(slotHour - currentHour) <= 2) return true;
      
      return false;
    });
  }, [timeSlots, currentTime]);

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-lg">
          <Clock className="h-5 w-5" />
          <span>Time Navigation</span>
          <Badge variant="outline" className="text-xs">
            {relevantSlots.length} time slots
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-64">
          <div className="space-y-1">
            {relevantSlots.map((slot) => {
              const isActive = currentSlotIndex !== -1 && timeSlots[currentSlotIndex].time === slot.time;
              const isSelected = selectedSlot === slot.time;
              
              return (
                <Button
                  key={slot.time}
                  variant={isActive ? "default" : isSelected ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-between h-auto p-3 text-left",
                    isActive && "ring-2 ring-blue-400",
                    slot.recordingCount === 0 && "opacity-60"
                  )}
                  onClick={() => handleSlotClick(slot)}
                >
                  <div className="flex items-center space-x-3">
                    <span className="font-mono text-sm font-medium">
                      {slot.timeString}
                    </span>
                    {slot.recordingCount > 0 && (
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">
                          {slot.recordingCount} rec
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {slot.totalDuration}s
                        </Badge>
                      </div>
                    )}
                  </div>
                  
                  {isActive && (
                    <Badge variant="destructive" className="text-xs">
                      CURRENT
                    </Badge>
                  )}
                </Button>
              );
            })}
          </div>
        </ScrollArea>
        
        {/* Summary */}
        <div className="mt-4 pt-3 border-t text-sm text-slate-600">
          <div className="flex justify-between items-center">
            <span>Current: {secondsToTime(currentTime)}</span>
            <span>{recordings.length} total recordings</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
