"use client"

import { Card } from "@/components/ui/card"
import { RideOption } from "@/types/uber"
import { cn } from "@/lib/utils"
import { Users, Tag, Clock } from "lucide-react"

interface RideCardProps {
  ride: RideOption
  onSelect?: (ride: RideOption) => void
}

export function RideCard({ ride, onSelect }: RideCardProps) {
  return (
    <Card
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md border-2",
        ride.isSelected 
          ? "border-black bg-gray-50" 
          : "border-gray-200 hover:border-gray-300"
      )}
      onClick={() => onSelect?.(ride)}
    >
      <div className="flex items-center gap-4 p-4">
        {/* Vehicle Image */}
        <div className="flex-shrink-0">
          <div className="w-20 h-12 flex items-center justify-center text-2xl bg-gray-100 rounded-lg">
            {ride.image}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            {/* Left side - Title and details */}
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-lg text-gray-900">
                  {ride.name}
                </h3>
                <div className="flex items-center gap-1 text-gray-600">
                  <Users className="w-3 h-3" />
                  <span className="text-sm">{ride.capacity}</span>
                </div>
                {ride.features?.includes("Faster") && (
                  <div className="flex items-center gap-1 bg-green-100 text-green-800 px-2 py-0.5 rounded text-xs">
                    <span>⚡</span>
                    <span>Faster</span>
                  </div>
                )}
              </div>

              <div className="flex items-center gap-2 mb-1">
                {ride.price !== "Select time" && (
                  <div className="flex items-center gap-1">
                    <Tag className="w-3 h-3 text-gray-500" />
                    <span className="font-semibold text-gray-900">{ride.price}</span>
                  </div>
                )}
              </div>

              <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                <Clock className="w-3 h-3" />
                <span>{ride.eta}</span>
                <span>•</span>
                <span>{ride.arrivalTime}</span>
              </div>

              <p className="text-sm text-gray-600 leading-tight">
                {ride.description}
              </p>
            </div>

            {/* Right side - Pricing */}
            <div className="text-right ml-4">
              {ride.price === "Select time" ? (
                <div className="text-sm text-blue-600 font-medium">
                  {ride.price}
                </div>
              ) : (
                <>
                  {ride.originalPrice && (
                    <div className="text-sm text-gray-500 line-through">
                      {ride.originalPrice}
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}
