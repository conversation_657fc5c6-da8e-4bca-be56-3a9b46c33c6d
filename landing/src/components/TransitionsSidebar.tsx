'use client';

import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { AirportTransition } from '@/types/api';
import { convertToAirportTime, formatTimeOnly, formatDateHeader, dateKeyToAirportTime } from '@/lib/utils';
import { PlaneIcon as PlaneTakeoff, PlaneLanding, Clock } from 'lucide-react';

interface GroupedTransition {
  date: string;
  transitions: AirportTransition[];
}

interface TransitionsSidebarProps {
  groupedTransitions: GroupedTransition[];
  loading: boolean;
  error: string | null;
}

export default function TransitionsSidebar({
  groupedTransitions,
  loading,
  error
}: TransitionsSidebarProps) {
  return (
    <div className="h-full bg-white border-r border-slate-200">
      <ScrollArea className="h-full">
        <div className="p-3 space-y-2">
          {error && (
            <div className="p-3 border border-red-200 bg-red-50 rounded text-sm text-red-600">
              {error}
            </div>
          )}

          {loading && groupedTransitions.length === 0 && (
            <div className="space-y-2">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="p-2 animate-pulse">
                  <div className="h-3 bg-slate-200 rounded mb-1"></div>
                  <div className="h-2 bg-slate-200 rounded w-3/4"></div>
                </div>
              ))}
            </div>
          )}

          {groupedTransitions.length === 0 && !loading && !error && (
            <div className="p-6 text-center">
              <Clock className="h-8 w-8 text-slate-300 mx-auto mb-2" />
              <p className="text-slate-500 text-sm">No transitions found</p>
            </div>
          )}

          {groupedTransitions.map((group, groupIndex) => (
            <div key={groupIndex} className="space-y-1">
              {/* Date Header */}
              <div className="sticky top-0 bg-white py-1 z-10">
                <h3 className="text-sm font-semibold text-slate-600 flex items-center space-x-1">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>{formatDateHeader(dateKeyToAirportTime(group.date))}</span>
                </h3>
                <Separator className="mt-1" />
              </div>

              {/* Transitions for this date */}
              <div className="space-y-1">
                {group.transitions.map((transition, index) => {
                  const airportTime = convertToAirportTime(transition.time);
                  const isTakeoff = transition.flight_phase === 'takeoff';

                  return (
                    <div
                      key={index}
                      className="px-2 py-1.5 hover:bg-slate-50 transition-colors border-l-2 bg-white"
                      style={{
                        borderLeftColor: isTakeoff ? '#10b981' : '#3b82f6'
                      }}
                    >
                      <div className="flex items-center justify-between">
                        {/* Left side: Time, Icon, Type, Flight */}
                        <div className="flex items-center space-x-2 flex-1 min-w-0">
                          <div className="text-base font-mono font-bold text-slate-900 min-w-[55px]">
                            {formatTimeOnly(airportTime)}
                          </div>
                          {isTakeoff ? (
                            <PlaneTakeoff className="h-4 w-4 text-green-600 flex-shrink-0" />
                          ) : (
                            <PlaneLanding className="h-4 w-4 text-blue-600 flex-shrink-0" />
                          )}
                          <span className={`text-sm px-2 py-1 rounded flex-shrink-0 ${
                            isTakeoff
                              ? 'bg-green-100 text-green-700'
                              : 'bg-blue-100 text-blue-700'
                          }`}>
                            {isTakeoff ? 'TAKEOFF' : 'LANDING'}
                          </span>
                          <span className="font-medium text-slate-900 text-sm truncate">
                            {transition.flight || transition.hex.slice(-4)}
                          </span>
                        </div>

                        {/* Right side: Runway */}
                        <div className="text-sm text-slate-500 flex-shrink-0 ml-2 px-2 py-1 rounded bg-slate-50">
                          [ RWY {transition.runway_name} ]
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
