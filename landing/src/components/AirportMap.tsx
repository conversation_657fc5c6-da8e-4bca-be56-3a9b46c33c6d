'use client';

import { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import L from 'leaflet';
import { Airport } from '@/types/api';
import { useAircraftPositions, useMapBounds } from '@/hooks/useAircraftPositions';
import { createAircraftIcon } from '@/components/AircraftIcon';
import { createAirportIcon } from '@/lib/airportIconGenerator';
import { usePersistentMap } from '@/hooks/use-persistent-map';
import MapControls from '@/components/MapControls';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as unknown as { _getIconUrl: unknown })._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface AirportMapProps {
  airport: Airport;
  className?: string;
}

// Component to handle map persistence and bounds
function MapController({ airport }: { airport: Airport }) {
  const map = useMap();
  const { handleMapReady, savedCenter, savedZoom } = usePersistentMap('overview');
  const hasSetInitialView = useRef(false);

  useEffect(() => {
    // Set up map persistence
    const cleanup = handleMapReady(map);
    return cleanup;
  }, [map, handleMapReady]);

  useEffect(() => {
    if (!airport.location || hasSetInitialView.current) return;

    // If we have saved position, use it; otherwise fit to airport bounds
    if (savedCenter && savedZoom) {
      map.setView(savedCenter, savedZoom);
      hasSetInitialView.current = true;
      return;
    }

    const bounds = L.latLngBounds([]);

    // Add airport location to bounds
    if (airport.location.type === 'Point') {
      const [lng, lat] = airport.location.coordinates as number[];
      bounds.extend([lat, lng]);
    }

    // Add runway centerlines to bounds
    airport.runways.forEach(runway => {
      if (runway.centerline && runway.centerline.type === 'LineString') {
        const coordinates = runway.centerline.coordinates as number[][];
        coordinates.forEach(([lng, lat]) => {
          bounds.extend([lat, lng]);
        });
      }
    });

    // If we have valid bounds, fit the map to them with more padding and max zoom for better overview
    if (bounds.isValid()) {
      map.fitBounds(bounds, {
        padding: [50, 50], // Reduced padding for a tighter, zoomed-in view
        maxZoom: 13 // Allow zooming in up to level 13
      });
      hasSetInitialView.current = true;
    }
  }, [airport, map, savedCenter, savedZoom]);

  return null;
}

// Component to display aircraft positions with permanent labels
function AircraftMarkers() {
  const map = useMap();
  const bounds = useMapBounds(map);

  const { positions } = useAircraftPositions(bounds, {
    refreshInterval: 2000, // 2 seconds
    lookbackSeconds: 7,
    enabled: true
  });

  return (
    <>
      {positions.map((aircraft) => {
        if (!aircraft.h3_index) return null;

        try {
          // Import h3-js to convert H3 index to coordinates
          // eslint-disable-next-line @typescript-eslint/no-require-imports
          const h3 = require('h3-js');

          // Use cellToLatLng function which returns [lat, lng]
          const coords = h3.cellToLatLng(aircraft.h3_index);
          const [lat, lng] = coords;

          // Validate coordinates
          if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
            console.warn(`Invalid coordinates for aircraft ${aircraft.hex}: ${lat}, ${lng}`);
            return null;
          }

          const icon = createAircraftIcon(
            aircraft.track,
            aircraft.alt_baro,
            aircraft.category,
            false
          );

          // Create permanent label content
          const labelContent = `
            <div class="aircraft-label" style="
              background: rgba(0,0,0,0.8);
              color: white;
              padding: 2px 6px;
              border-radius: 4px;
              font-size: 11px;
              font-weight: 500;
              white-space: nowrap;
              pointer-events: none;
              margin-top: 20px;
              text-align: center;
              box-shadow: 0 1px 3px rgba(0,0,0,0.3);
            ">
              ${aircraft.flight || aircraft.hex.slice(-4)}<br>
              ${aircraft.alt_baro !== undefined ? `${aircraft.alt_baro}ft` : 'N/A'}
            </div>
          `;

          const labelIcon = L.divIcon({
            html: labelContent,
            className: 'aircraft-label-icon',
            iconSize: [60, 30],
            iconAnchor: [30, -10],
          });

          return (
            <div key={aircraft.hex}>
              {/* Aircraft icon */}
              <Marker
                position={[lat, lng]}
                icon={icon}
                interactive={false}
              />
              {/* Permanent label */}
              <Marker
                position={[lat, lng]}
                icon={labelIcon}
                interactive={false}
              />
            </div>
          );
        } catch (err) {
          console.error('Error converting H3 index to coordinates:', err);
          return null;
        }
      })}
    </>
  );
}

export default function AirportMap({ airport, className = '' }: AirportMapProps) {
  const mapRef = useRef<L.Map | null>(null);

  if (!airport.location || airport.location.type !== 'Point') {
    return (
      <div className={`bg-gray-100 flex items-center justify-center ${className}`}>
        <p className="text-gray-500">Airport location not available</p>
      </div>
    );
  }

  const [lng, lat] = airport.location.coordinates as number[];

  return (
    <div className={className}>
      <MapContainer
        center={[lat, lng]}
        zoom={9}
        style={{ height: '100%', width: '100%' }}
        ref={mapRef}
      >
        <TileLayer
          attribution='&copy; <a href="https://carto.com/attributions">CARTO</a>'
          url="https://{s}.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}{r}.png"
          subdomains="abcd"
          maxZoom={20}
        />

        {/* Airport icon at airport location */}
        {(() => {
          const airportIcon = createAirportIcon(airport, 50);
          if (!airportIcon) {
            return null;
          }

          return (
            <Marker
              position={[lat, lng]}
              icon={airportIcon}
              interactive={false}
            />
          );
        })()}

        <MapController airport={airport} />
        <MapControls />
        <AircraftMarkers />
      </MapContainer>
    </div>
  );
}
