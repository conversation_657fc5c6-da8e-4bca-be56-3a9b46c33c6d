"use client"

import { ReactNode, useState, useRef } from 'react'
import { useIsMobile } from '@/hooks/use-mobile'
import dynamic from 'next/dynamic'

// Dynamically import the map component to avoid SSR issues
const LeafletMap = dynamic(() => import('./LeafletMap'), {
  ssr: false,
  loading: () => <div className="bg-slate-100 flex items-center justify-center h-full">Loading map...</div>
})

interface MapWithDrawerProps {
  children: ReactNode
  mapCenter?: [number, number]
  mapZoom?: number
}

export function MapWithDrawer({
  children,
  mapCenter = [37.7749, -122.4194], // Default to San Francisco
  mapZoom = 13
}: MapWithDrawerProps) {
  const isMobile = useIsMobile()

  if (isMobile) {
    return <MobileMapDrawer mapCenter={mapCenter} mapZoom={mapZoom}>{children}</MobileMapDrawer>
  }

  return <DesktopMapSidebar mapCenter={mapCenter} mapZoom={mapZoom}>{children}</DesktopMapSidebar>
}

function DesktopMapSidebar({
  children,
  mapCenter,
  mapZoom
}: MapWithDrawerProps) {
  return (
    <div className="h-screen w-full flex overflow-hidden">
      {/* Left side - Content with proper scrolling */}
      <div className="flex-1 bg-white overflow-y-auto">
        {children}
      </div>

      {/* Right side - Map */}
      <div className="flex-1">
        <LeafletMap
          className="h-full w-full"
          center={mapCenter}
          zoom={mapZoom}
        />
      </div>
    </div>
  )
}

function MobileMapDrawer({
  children,
  mapCenter,
  mapZoom
}: MapWithDrawerProps) {
  const [drawerHeight, setDrawerHeight] = useState(50) // Start at 50% of screen
  const drawerRef = useRef<HTMLDivElement>(null)
  const isDragging = useRef(false)
  const startY = useRef(0)
  const startHeight = useRef(50)

  const handleTouchStart = (e: React.TouchEvent) => {
    isDragging.current = true
    startY.current = e.touches[0].clientY
    startHeight.current = drawerHeight
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging.current) return

    const currentY = e.touches[0].clientY
    const deltaY = startY.current - currentY
    const screenHeight = window.innerHeight
    const deltaPercent = (deltaY / screenHeight) * 100

    const newHeight = Math.min(100, Math.max(50, startHeight.current + deltaPercent))
    setDrawerHeight(newHeight)
  }

  const handleTouchEnd = () => {
    isDragging.current = false
    // Snap to 50% or 100% based on current position
    if (drawerHeight < 75) {
      setDrawerHeight(50)
    } else {
      setDrawerHeight(100)
    }
  }

  return (
    <div className="relative h-screen w-full overflow-hidden bg-gray-100">
      {/* Map Background - Fixed, only renders in top 50% */}
      <div className="absolute inset-0 z-10">
        <div className="h-1/2 w-full">
          <LeafletMap
            className="h-full w-full"
            center={mapCenter}
            zoom={mapZoom}
          />
        </div>
      </div>

      {/* Mobile Drawer - positioned from bottom */}
      <div
        ref={drawerRef}
        className="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-2xl z-20 transition-all duration-300 ease-out"
        style={{
          height: `${drawerHeight}%`,
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Drawer Handle */}
        <div className="flex justify-center pt-3 pb-2 cursor-grab active:cursor-grabbing">
          <div className="w-12 h-1 bg-gray-300 rounded-full" />
        </div>

        {/* Scrollable Content */}
        <div className="h-full overflow-y-auto pb-safe">
          {children}
        </div>
      </div>
    </div>
  )
}

export default MapWithDrawer
