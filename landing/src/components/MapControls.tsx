'use client';

import { useEffect } from 'react';
import { useMap } from 'react-leaflet';
import { panControl } from '@/lib/leafletControls';

/**
 * Map controls component that adds pan controls to Leaflet maps
 * Controls are positioned at bottom-left and only visible on hover
 */
export default function MapControls() {
  const map = useMap();

  useEffect(() => {
    // Add pan control to bottom left to avoid conflict with zoom controls
    const panCtrl = panControl({ position: 'bottomleft' });
    map.addControl(panCtrl);

    // Cleanup
    return () => {
      map.removeControl(panCtrl);
    };
  }, [map]);

  return null;
}
