'use client';

import { useMemo } from 'react';
import { RecordingFile } from '@/types/api';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Clock } from 'lucide-react';
import { cn, timeUtils } from '@/lib/utils';

interface HourSelectorProps {
  recordings: RecordingFile[];
  currentTime: number;
  onTimeChange: (time: number) => void;
  className?: string;
}

interface HourSlot {
  hour: number;
  startTime: number;
  endTime: number;
  timeString: string;
  recordingSeconds: number;
  recordingCount: number;
}

export function HourSelector({
  recordings,
  currentTime,
  onTimeChange,
  className
}: HourSelectorProps) {
  // Use centralized time utilities
  const { timeToSeconds } = timeUtils;

  // Generate 24 hourly slots with recording data
  const hourSlots = useMemo(() => {
    const slots: HourSlot[] = [];

    for (let hour = 0; hour < 24; hour++) {
      const startTime = hour * 3600;
      const endTime = (hour + 1) * 3600;

      // Find recordings that overlap with this hour
      const hourRecordings = recordings.filter(recording => {
        const recordingStart = timeToSeconds(recording.start_time);
        const recordingEnd = recordingStart + (recording.duration_ms / 1000);

        // Check if recording overlaps with the hour
        return recordingEnd > startTime && recordingStart < endTime;
      });

      // Calculate total recording seconds in this hour
      const totalSeconds = hourRecordings.reduce((sum, recording) => {
        const recordingStart = timeToSeconds(recording.start_time);
        const recordingEnd = recordingStart + (recording.duration_ms / 1000);

        // Calculate overlap duration
        const overlapStart = Math.max(startTime, recordingStart);
        const overlapEnd = Math.min(endTime, recordingEnd);
        const overlapDuration = Math.max(0, overlapEnd - overlapStart);

        return sum + overlapDuration;
      }, 0);

      slots.push({
        hour,
        startTime,
        endTime,
        timeString: `${hour.toString().padStart(2, '0')}:00 - ${((hour + 1) % 24).toString().padStart(2, '0')}:00`,
        recordingSeconds: Math.round(totalSeconds),
        recordingCount: hourRecordings.length
      });
    }

    return slots;
  }, [recordings, timeToSeconds]);

  // Find current hour
  const currentHour = Math.floor(currentTime / 3600);
  const currentSlot = hourSlots[currentHour];

  // Handle hour selection
  const handleHourSelect = (slot: HourSlot) => {
    onTimeChange(slot.startTime);
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "justify-start text-left font-normal w-[200px]",
            className
          )}
        >
          <Clock className="mr-2 h-4 w-4" />
          {currentSlot ? currentSlot.timeString : "Select Hour"}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0">
        <div className="p-4 border-b">
          <h4 className="font-medium text-sm">Select Time Period</h4>
          <p className="text-xs text-muted-foreground">
            {recordings.length} total recordings
          </p>
        </div>
        <ScrollArea className="h-64">
          <div className="p-2 space-y-1">
            {hourSlots.map((slot) => {
              const isActive = currentHour === slot.hour;
              
              return (
                <Button
                  key={slot.hour}
                  variant={isActive ? "default" : "ghost"}
                  className={cn(
                    "w-full justify-between h-auto p-3 text-left",
                    isActive && "ring-2 ring-blue-400",
                    slot.recordingCount === 0 && "opacity-60"
                  )}
                  onClick={() => handleHourSelect(slot)}
                >
                  <div className="flex items-center space-x-3">
                    <span className="font-mono text-sm font-medium">
                      {slot.timeString}
                    </span>
                    {slot.recordingCount > 0 && (
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">
                          {slot.recordingCount} rec
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {slot.recordingSeconds}s
                        </Badge>
                      </div>
                    )}
                  </div>
                </Button>
              );
            })}
          </div>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
