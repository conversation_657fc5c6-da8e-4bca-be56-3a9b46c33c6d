import { useState, useEffect, useCallback, useRef } from 'react';
import { getAircraftPositionsInBbox } from '@/lib/api';
import { AircraftPosition, AircraftPositionsAtTimeResponse } from '@/types/api';

interface BoundingBox {
  north: number;
  east: number;
  south: number;
  west: number;
}

interface UseAircraftPositionsOptions {
  refreshInterval?: number; // in milliseconds, default 2000 (2 seconds)
  lookbackSeconds?: number; // default 7 seconds
  enabled?: boolean; // default true
}

interface UseAircraftPositionsReturn {
  positions: AircraftPosition[];
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  queryInfo: AircraftPositionsAtTimeResponse['query_info'] | null;
  refreshPositions: () => Promise<void>;
}

export function useAircraftPositions(
  bbox: BoundingBox | null,
  options: UseAircraftPositionsOptions = {}
): UseAircraftPositionsReturn {
  const {
    refreshInterval = 2000,
    lookbackSeconds = 7,
    enabled = true
  } = options;

  const [positions, setPositions] = useState<AircraftPosition[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [queryInfo, setQueryInfo] = useState<AircraftPositionsAtTimeResponse['query_info'] | null>(null);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const fetchPositions = useCallback(async () => {
    if (!bbox || !enabled) return;

    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this request
    abortControllerRef.current = new AbortController();

    try {
      setError(null);
      if (positions.length === 0) {
        setLoading(true);
      }

      console.log('Fetching aircraft positions with bbox:', {
        north: bbox.north,
        east: bbox.east,
        south: bbox.south,
        west: bbox.west,
        lookbackSeconds
      });

      // Validate bounding box
      if (bbox.south >= bbox.north) {
        console.error('Invalid bounding box: south >= north', bbox);
        setError('Invalid bounding box: south must be less than north');
        setLoading(false);
        return;
      }

      if (bbox.west >= bbox.east) {
        console.error('Invalid bounding box: west >= east', bbox);
        setError('Invalid bounding box: west must be less than east');
        setLoading(false);
        return;
      }

      const response = await getAircraftPositionsInBbox(
        bbox.north,
        bbox.east,
        bbox.south,
        bbox.west,
        undefined, // Use current time
        lookbackSeconds
      );

      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      console.log('Aircraft positions response:', {
        count: response.positions.length,
        queryInfo: response.query_info,
        samplePosition: response.positions[0]
      });

      setPositions(response.positions);
      setQueryInfo(response.query_info);
      setLastUpdated(new Date());
      setLoading(false);
    } catch (err) {
      // Don't set error if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      console.error('Failed to fetch aircraft positions:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch aircraft positions');
      setLoading(false);
    }
  }, [bbox, enabled, lookbackSeconds, positions.length]);

  // Set up automatic refresh
  useEffect(() => {
    if (!bbox || !enabled) {
      // Clear interval if bbox is null or disabled
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Initial fetch
    fetchPositions();

    // Set up interval for periodic refresh
    intervalRef.current = setInterval(fetchPositions, refreshInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchPositions, refreshInterval, bbox, enabled]);

  // Manual refresh function
  const refreshPositions = useCallback(async () => {
    await fetchPositions();
  }, [fetchPositions]);

  return {
    positions,
    loading,
    error,
    lastUpdated,
    queryInfo,
    refreshPositions,
  };
}

// Helper hook to get bounding box from Leaflet map
export function useMapBounds(map: L.Map | null): BoundingBox | null {
  const [bounds, setBounds] = useState<BoundingBox | null>(null);

  useEffect(() => {
    if (!map) return;

    const updateBounds = () => {
      const leafletBounds = map.getBounds();
      const newBounds = {
        north: leafletBounds.getNorth(),
        east: leafletBounds.getEast(),
        south: leafletBounds.getSouth(),
        west: leafletBounds.getWest(),
      };

      console.log('Map bounds updated:', newBounds);

      // Validate bounds before setting
      if (newBounds.south >= newBounds.north) {
        console.error('Invalid map bounds: south >= north', newBounds);
        return;
      }

      setBounds(newBounds);
    };

    // Initial bounds
    updateBounds();

    // Listen for map events
    map.on('moveend', updateBounds);
    map.on('zoomend', updateBounds);

    return () => {
      map.off('moveend', updateBounds);
      map.off('zoomend', updateBounds);
    };
  }, [map]);

  return bounds;
}
