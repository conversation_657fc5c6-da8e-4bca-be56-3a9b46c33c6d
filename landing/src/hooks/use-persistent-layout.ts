'use client';

import { useCallback, useEffect, useRef } from 'react';
import { ImperativePanelGroupHandle } from 'react-resizable-panels';
import { useSettings } from '@/contexts/SettingsContext';

/**
 * Hook for managing persistent resizable panel layout
 * Automatically saves and restores panel sizes
 */
export function usePersistentLayout() {
  const { settings, updateLayout } = useSettings();
  const panelGroupRef = useRef<ImperativePanelGroupHandle>(null);

  // Load saved panel sizes on mount
  useEffect(() => {
    if (settings.layout.resizablePanelSizes && panelGroupRef.current) {
      try {
        panelGroupRef.current.setLayout(settings.layout.resizablePanelSizes);
      } catch (error) {
        console.warn('Failed to restore panel layout:', error);
      }
    }
  }, [settings.layout.resizablePanelSizes]);

  // Handle panel layout changes
  const handleLayoutChange = useCallback((sizes: number[]) => {
    // Validate sizes array
    if (Array.isArray(sizes) && sizes.length > 0 && sizes.every(size => typeof size === 'number' && !isNaN(size))) {
      updateLayout({ resizablePanelSizes: sizes });
    }
  }, [updateLayout]);

  return {
    panelGroupRef,
    handleLayoutChange,
    defaultSizes: settings.layout.resizablePanelSizes || [50, 50]
  };
}
