'use client';

import { useEffect } from 'react';
import { format } from 'date-fns';
import { getRecordings } from '@/lib/api';
import { usePlayback } from '@/contexts/PlaybackContext';

export function useRecordings(station: string) {
  const { state, dispatch } = usePlayback();
  const { selectedDate } = state;

  // Fetch recordings when date or station changes
  useEffect(() => {
    if (!station || !selectedDate) return;

    const fetchRecordings = async () => {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });
      
      try {
        const dateString = format(selectedDate, 'yyyyMMdd');
        const data = await getRecordings(station, dateString);
        dispatch({ type: 'SET_RECORDINGS', payload: data });
      } catch (err) {
        console.error('Failed to fetch recordings:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch recordings';
        dispatch({ type: 'SET_ERROR', payload: errorMessage });
      }
    };

    fetchRecordings();
  }, [station, selectedDate, dispatch]);

  return {
    recordings: state.recordings,
    loading: state.loading,
    error: state.error,
  };
}
