'use client';

import { useCallback, useRef } from 'react';
import { useSettings } from '@/contexts/SettingsContext';
import L from 'leaflet';

/**
 * Hook for managing persistent map state (center and zoom)
 * Automatically saves and restores map position and zoom level
 * @param mapType - Type of map ('overview' or 'satellite')
 */
export function usePersistentMap(mapType: 'overview' | 'satellite') {
  const { settings, updateLayout } = useSettings();
  const mapRef = useRef<L.Map | null>(null);
  const isInitialized = useRef(false);

  const centerKey = mapType === 'overview' ? 'mapCenter' : 'satelliteMapCenter';
  const zoomKey = mapType === 'overview' ? 'mapZoom' : 'satelliteMapZoom';

  // Handle map events to save state
  const handleMapReady = useCallback((map: L.Map) => {
    mapRef.current = map;

    // Restore saved position and zoom if available
    const savedCenter = settings.layout[centerKey];
    const savedZoom = settings.layout[zoomKey];

    if (savedCenter && savedZoom && !isInitialized.current) {
      try {
        map.setView(savedCenter, savedZoom);
        isInitialized.current = true;
      } catch (error) {
        console.warn(`Failed to restore ${mapType} map state:`, error);
      }
    }

    // Save map state on move and zoom
    const saveMapState = () => {
      try {
        const center = map.getCenter();
        const zoom = map.getZoom();

        // Validate coordinates
        if (isNaN(center.lat) || isNaN(center.lng) || isNaN(zoom)) {
          console.warn('Invalid map state, skipping save');
          return;
        }

        updateLayout({
          [centerKey]: [center.lat, center.lng],
          [zoomKey]: zoom
        });
      } catch (error) {
        console.warn(`Failed to save ${mapType} map state:`, error);
      }
    };

    // Debounce the save function to avoid too frequent updates
    let saveTimeout: NodeJS.Timeout;
    const debouncedSave = () => {
      clearTimeout(saveTimeout);
      saveTimeout = setTimeout(saveMapState, 500);
    };

    map.on('moveend', debouncedSave);
    map.on('zoomend', debouncedSave);

    return () => {
      map.off('moveend', debouncedSave);
      map.off('zoomend', debouncedSave);
      clearTimeout(saveTimeout);
    };
  }, [settings.layout, centerKey, zoomKey, updateLayout, mapType]);

  return {
    handleMapReady,
    savedCenter: settings.layout[centerKey],
    savedZoom: settings.layout[zoomKey]
  };
}
