'use client';

import { useRef, useEffect, useCallback } from 'react';
import { usePlayback } from '@/contexts/PlaybackContext';
import { timeUtils } from '@/lib/utils';

export function useAudioPlayback() {
  const { state, dispatch, findRecordingAtTime, findNearbyRecordings } = usePlayback();
  const { currentTime, isPlaying, currentRecording, volume, isMuted } = state;

  // Refs
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const timeUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const preloadAudioRefs = useRef<Map<string, HTMLAudioElement>>(new Map());
  const lastPreloadTime = useRef<number>(-1);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const loadRecordingRef = useRef<string | null>(null);
  const lastUserTimeRef = useRef<number>(currentTime);

  // Audio event handlers
  const handleAudioEnded = useCallback(() => {
    // Audio ended, continuing playback...
  }, []);

  const handleAudioError = useCallback((e: Event) => {
    const audio = e.target as HTMLAudioElement;
    const error = audio.error;

    if (error) {
      console.error('Audio error:', {
        code: error.code,
        message: error.message,
        src: audio.src
      });
    }

    dispatch({ type: 'SET_IS_PLAYING', payload: false });
  }, [dispatch]);

  // Initialize audio element
  useEffect(() => {
    if (!audioRef.current) {
      audioRef.current = new Audio();
      audioRef.current.volume = volume / 100;
      audioRef.current.crossOrigin = 'anonymous';
      audioRef.current.preload = 'none';

      // Check WebM support
      const canPlayWebM = audioRef.current.canPlayType('video/webm; codecs="opus"');
      if (canPlayWebM === '') {
        console.warn('WebM format may not be supported by this browser');
      }

      audioRef.current.addEventListener('ended', handleAudioEnded);
      audioRef.current.addEventListener('error', handleAudioError);
    }

    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener('ended', handleAudioEnded);
        audioRef.current.removeEventListener('error', handleAudioError);
      }
    };
  }, [handleAudioEnded, handleAudioError, volume]);

  // Update audio volume
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume / 100;
    }
  }, [volume, isMuted]);

  // Preload nearby recordings
  useEffect(() => {
    // Only update preloading every 10 seconds to avoid excessive updates
    if (Math.abs(currentTime - lastPreloadTime.current) < 10) {
      return;
    }

    lastPreloadTime.current = currentTime;
    const nearbyRecordings = findNearbyRecordings(currentTime);

    // Clean up old preloaded recordings
    const currentFilenames = new Set(nearbyRecordings.map(r => r.filename));
    preloadAudioRefs.current.forEach((audio, filename) => {
      if (!currentFilenames.has(filename)) {
        audio.src = '';
        preloadAudioRefs.current.delete(filename);
      }
    });

    // Preload new recordings
    nearbyRecordings.forEach(recording => {
      if (!preloadAudioRefs.current.has(recording.filename)) {
        const preloadAudio = new Audio();
        preloadAudio.src = recording.download_url;
        preloadAudio.preload = 'auto';
        preloadAudio.volume = 0;
        preloadAudioRefs.current.set(recording.filename, preloadAudio);
      }
    });
  }, [currentTime, findNearbyRecordings]);

  // Load audio for specific time
  const loadAudioForTime = useCallback(async (targetTime: number) => {
    const recordingAtTime = findRecordingAtTime(targetTime);

    // If no recording change needed, just update position
    if (recordingAtTime?.filename === currentRecording?.filename && audioRef.current && currentRecording) {
      const offsetInRecording = timeUtils.getOffsetInRecording(targetTime, currentRecording);

      if (Math.abs(audioRef.current.currentTime - offsetInRecording) > 1) {
        audioRef.current.currentTime = Math.min(offsetInRecording, audioRef.current.duration || 0);
      }
      return;
    }

    // Recording changed - load new audio
    if (audioRef.current) {
      audioRef.current.pause();
    }

    dispatch({ type: 'SET_CURRENT_RECORDING', payload: recordingAtTime });

    if (!recordingAtTime || !audioRef.current) {
      dispatch({ type: 'SET_AUDIO_LOADING', payload: false });
      return;
    }

    // Prevent duplicate loads
    if (loadRecordingRef.current === recordingAtTime.filename) {
      return;
    }

    loadRecordingRef.current = recordingAtTime.filename;

    // Check if audio is already ready
    const audio = audioRef.current;
    if (audio && audio.src === recordingAtTime.download_url && audio.readyState >= 2) {
      const offsetInRecording = timeUtils.getOffsetInRecording(targetTime, recordingAtTime);
      audio.currentTime = Math.min(offsetInRecording, audio.duration || 0);

      if (isPlaying) {
        audio.play().catch((error) => {
          console.error('Audio playback failed:', error);
        });
      }

      loadRecordingRef.current = null;
      return;
    }

    // Delay loading state to prevent brief flashes
    loadingTimeoutRef.current = setTimeout(() => {
      dispatch({ type: 'SET_AUDIO_LOADING', payload: true });
    }, 100);

    try {
      const audio = audioRef.current;
      audio.src = recordingAtTime.download_url;

      await new Promise<void>((resolve, reject) => {
        const handleCanPlay = () => {
          audio.removeEventListener('canplay', handleCanPlay);
          audio.removeEventListener('error', handleError);
          resolve();
        };

        const handleError = () => {
          audio.removeEventListener('canplay', handleCanPlay);
          audio.removeEventListener('error', handleError);
          reject(new Error('Audio load failed'));
        };

        audio.addEventListener('canplay', handleCanPlay);
        audio.addEventListener('error', handleError);
        audio.load();
      });

      // Wait a bit more for seeking to be ready
      await new Promise(resolve => setTimeout(resolve, 100));

      // Set position
      const offsetInRecording = timeUtils.getOffsetInRecording(targetTime, recordingAtTime);
      audio.currentTime = Math.min(offsetInRecording, audio.duration || 0);

      // Clear loading timeout and set loading to false
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }
      dispatch({ type: 'SET_AUDIO_LOADING', payload: false });

      // Play if we should be playing
      if (isPlaying) {
        await audio.play();
      }
    } catch (error) {
      console.error('Audio loading failed:', error);
      // Clear loading timeout and set loading to false
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }
      dispatch({ type: 'SET_AUDIO_LOADING', payload: false });
    } finally {
      loadRecordingRef.current = null;
    }
  }, [currentRecording, findRecordingAtTime, isPlaying, dispatch]);

  // Main playback loop
  useEffect(() => {
    if (!isPlaying) {
      if (timeUpdateIntervalRef.current) {
        clearInterval(timeUpdateIntervalRef.current);
        timeUpdateIntervalRef.current = null;
      }
      return;
    }

    timeUpdateIntervalRef.current = setInterval(() => {
      let newTime = currentTime;

      if (currentRecording && audioRef.current && !audioRef.current.paused && !state.isLoading) {
        // Audio is playing - sync with audio position
        const recordingStartTime = timeUtils.timeToSeconds(currentRecording.start_time);
        const recordingEndTime = recordingStartTime + (currentRecording.duration_ms / 1000);
        const audioTime = recordingStartTime + audioRef.current.currentTime;

        // Check if audio has ended
        if (audioRef.current.ended || audioTime >= recordingEndTime) {
          // Move past the end of this recording to continue timeline
          newTime = recordingEndTime + 0.1;
        } else {
          newTime = audioTime;
        }
      } else {
        // Silent playback or no audio - advance timeline
        newTime = currentTime + 1;
      }

      // Update time
      dispatch({ type: 'SET_CURRENT_TIME', payload: newTime });

      // Load audio for new time if needed
      const recordingAtNewTime = findRecordingAtTime(newTime);
      if (recordingAtNewTime?.filename !== currentRecording?.filename) {
        loadAudioForTime(newTime);
      }
    }, 1000);

    return () => {
      if (timeUpdateIntervalRef.current) {
        clearInterval(timeUpdateIntervalRef.current);
        timeUpdateIntervalRef.current = null;
      }
    };
  }, [isPlaying, currentRecording, currentTime, state.isLoading, dispatch, findRecordingAtTime, loadAudioForTime]);

  // Handle user-initiated time changes
  useEffect(() => {
    const timeDiff = Math.abs(currentTime - lastUserTimeRef.current);

    if (timeDiff > 3) {
      // User navigated - load audio for new position
      loadAudioForTime(currentTime);
    }

    lastUserTimeRef.current = currentTime;
  }, [currentTime, loadAudioForTime]);

  // Play/pause functionality
  const togglePlayback = useCallback(async () => {
    if (state.isLoading) return;

    if (isPlaying) {
      // Pause
      if (audioRef.current) {
        audioRef.current.pause();
      }
      dispatch({ type: 'SET_IS_PLAYING', payload: false });
    } else {
      // Play
      dispatch({ type: 'SET_IS_PLAYING', payload: true });
      // Load audio for current position if needed
      await loadAudioForTime(currentTime);
    }
  }, [state.isLoading, isPlaying, currentTime, loadAudioForTime, dispatch]);

  // Cleanup
  useEffect(() => {
    const preloadRefs = preloadAudioRefs.current;
    return () => {
      if (timeUpdateIntervalRef.current) {
        clearInterval(timeUpdateIntervalRef.current);
      }
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
      // Clean up preloaded audio
      preloadRefs.forEach(audio => {
        audio.src = '';
      });
      preloadRefs.clear();
    };
  }, []);

  return {
    togglePlayback,
    loadAudioForTime,
  };
}
