'use client';

import { useState, useCallback, useEffect } from 'react';
import { usePlayback } from '@/contexts/PlaybackContext';

interface UseTimelineInteractionProps {
  timelineRef: React.RefObject<HTMLDivElement | null>;
  startTime: number;
  duration: number; // in seconds
}

export function useTimelineInteraction({ 
  timelineRef, 
  startTime, 
  duration 
}: UseTimelineInteractionProps) {
  const { setCurrentTime } = usePlayback();
  const [isDragging, setIsDragging] = useState(false);

  // Calculate time from mouse/touch position
  const calculateTimeFromPosition = useCallback((clientX: number): number => {
    if (!timelineRef.current) return startTime;

    const rect = timelineRef.current.getBoundingClientRect();
    const clickX = clientX - rect.left;
    const percentage = Math.max(0, Math.min(1, clickX / rect.width));
    return startTime + (percentage * duration);
  }, [timelineRef, startTime, duration]);

  // Handle timeline interaction (click or drag)
  const handleTimelineInteraction = useCallback((clientX: number) => {
    const newTime = calculateTimeFromPosition(clientX);
    setCurrentTime(newTime);
  }, [calculateTimeFromPosition, setCurrentTime]);

  // Mouse event handlers
  const handleMouseDown = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(true);
    handleTimelineInteraction(event.clientX);
  }, [handleTimelineInteraction]);

  const handleMouseMove = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging) {
      handleTimelineInteraction(event.clientX);
    }
  }, [isDragging, handleTimelineInteraction]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Touch event handlers
  const handleTouchStart = useCallback((event: React.TouchEvent<HTMLDivElement>) => {
    setIsDragging(true);
    const touch = event.touches[0];
    handleTimelineInteraction(touch.clientX);
  }, [handleTimelineInteraction]);

  const handleTouchMove = useCallback((event: React.TouchEvent<HTMLDivElement>) => {
    if (isDragging) {
      event.preventDefault(); // Prevent scrolling
      const touch = event.touches[0];
      handleTimelineInteraction(touch.clientX);
    }
  }, [isDragging, handleTimelineInteraction]);

  const handleTouchEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Global mouse and touch events for smooth dragging
  useEffect(() => {
    if (isDragging) {
      const handleGlobalMouseMove = (event: MouseEvent) => {
        handleTimelineInteraction(event.clientX);
      };

      const handleGlobalTouchMove = (event: TouchEvent) => {
        event.preventDefault(); // Prevent scrolling
        if (event.touches.length > 0) {
          handleTimelineInteraction(event.touches[0].clientX);
        }
      };

      const handleGlobalEnd = () => {
        setIsDragging(false);
      };

      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalEnd);
      document.addEventListener('touchmove', handleGlobalTouchMove, { passive: false });
      document.addEventListener('touchend', handleGlobalEnd);

      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove);
        document.removeEventListener('mouseup', handleGlobalEnd);
        document.removeEventListener('touchmove', handleGlobalTouchMove);
        document.removeEventListener('touchend', handleGlobalEnd);
      };
    }
  }, [isDragging, handleTimelineInteraction]);

  return {
    isDragging,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
  };
}
