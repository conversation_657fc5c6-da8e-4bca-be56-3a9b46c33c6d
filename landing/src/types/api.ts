// API response types based on the TarmacTrack API

export interface Geometry {
  type: string;
  coordinates: number[] | number[][];
}

export interface Runway {
  name: string;
  length_ft?: number;
  width_ft?: number;
  surface: string;
  centerline?: Geometry;
  heading_deg?: number;
}

export interface Airport {
  icao: string;
  iata?: string;
  name?: string;
  location?: Geometry;
  elevation_ft?: number;
  timezone?: string;
  image_url?: string;
  runways: Runway[];
}

export interface AirportTransition {
  time: string; // ISO datetime string
  hex: string;
  flight_phase: 'startup' | 'ground' | 'takeoff' | 'landing' | 'appear' | 'shutdown' | 'disappear' | 'airborne';
  runway_name: string | null;
  flight?: string | null;
  alt_baro?: number;
  alt_geom?: number;
  gs?: number;
  track?: number;
  baro_rate?: number;
  squawk?: number | null;
  h3_index?: string;
  h3_res4?: string;
}

export interface AirportTransitionsResponse {
  airport_icao: string;
  start_time: string;
  end_time: string;
  total_transitions: number;
  transitions: AirportTransition[];
}

// Aircraft position types
export interface AircraftPosition {
  time: string; // ISO datetime string
  hex: string;
  alt_baro?: number;
  alt_geom?: number;
  gs?: number;
  track?: number;
  baro_rate?: number;
  squawk?: number;
  flight?: string;
  h3_index?: string;
  h3_res4?: string;
  category?: string;
}

export interface AircraftPositionsAtTimeResponse {
  positions: AircraftPosition[];
  query_info: {
    bounding_box?: {
      north: number;
      east: number;
      south: number;
      west: number;
    };
    h3_cells_count: number;
    h3_resolution?: number;
    estimated_search_area_km2?: number;
    reference_time: string;
    lookback_seconds: number;
    results_count: number;
  };
}

// Aircraft trace types
export interface AircraftTracePosition {
  time: string; // ISO datetime string
  alt_baro?: number;
  gs?: number;
  track?: number | null;
  baro_rate?: number | null;
  h3_index: string;
}

export interface AircraftTraceResponse {
  hex: string;
  positions: AircraftTracePosition[];
  query_info: {
    hex: string;
    start_time: string;
    end_time: string;
    time_range_hours: number;
    results_count: number;
  };
}

// Recordings types
export interface RecordingFile {
  filename: string;
  start_time: string; // ISO datetime string
  duration_ms: number;
  size_bytes: number;
  download_url: string;
}

export interface RecordingsListResponse {
  station: string;
  date: string; // YYYYMMDD format
  total_recordings: number;
  total_duration_ms: number;
  total_size_bytes: number;
  recordings: RecordingFile[];
}
