.PHONY: help build up down logs api webapp test clean webapp-shell prod-up dev-up

# Default target
help:
	@echo "Available targets:"
	@echo "  build       - Build all Docker containers"
	@echo "  up          - Start all services"
	@echo "  down        - Stop all services"
	@echo "  api         - Start only the API service"
	@echo "  webapp      - Start only the webapp service"
	@echo "  logs        - Show logs for all services"
	@echo "  logs-api    - Show logs for API service"
	@echo "  logs-app    - Show logs for webapp service"
	@echo "  test        - Run API tests"
	@echo "  shell-api   - Open shell in API container"
	@echo "  webapp-shell - Open shell in webapp container"
	@echo "  prod-up     - Start services in production mode"
	@echo "  dev-up      - Start services in development mode"
	@echo "  clean       - Clean up Docker containers and images"
	@echo "  setup       - Complete setup (build + up)"

# Docker commands
build:
	docker-compose build

up:
	docker-compose up -d

down:
	docker-compose down

api:
	docker-compose up -d api

webapp:
	docker-compose up -d webapp

logs:
	docker-compose logs -f

logs-api:
	docker-compose logs -f api

logs-app:
	docker-compose logs -f webapp

# Development commands
test:
	cd api && python -m pytest

shell-api:
	docker-compose exec api /bin/bash

# API specific commands
api-dev:
	cd api && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

api-install:
	cd api && pip install -r requirements.txt

# Webapp specific commands
webapp-dev:
	cd webapp && npm run dev

webapp-install:
	cd webapp && npm install

webapp-build:
	cd webapp && npm run build

webapp-shell:
	docker-compose exec webapp /bin/sh

# Production deployment
prod-up:
	docker-compose -f docker-compose.yml up -d

# Development with hot reload
dev-up:
	docker-compose up -d

# Cleanup
clean:
	docker-compose down -v
	docker system prune -f

# Complete setup
setup: build up
	@echo "Setting up TarmacTrack..."
	@echo "API will be available at http://localhost:8001"
	@echo "Webapp will be available at http://localhost:3000"
	@echo "API docs available at http://localhost:8001/docs"
	@echo ""
	@echo "For development with hot reload, use: make dev-up"
	@echo "For production deployment, use: make prod-up" 