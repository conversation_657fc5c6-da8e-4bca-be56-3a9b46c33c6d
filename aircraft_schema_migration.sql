-- =============================================================================
-- Aircraft Table Schema Migration
-- Adding TAR1090 database fields for enhanced aircraft information
-- =============================================================================

-- First, create new enums for the additional fields
CREATE TYPE public.wake_turbulence_category_enum AS ENUM (
    'L',  -- Light (< 7,000 kg)
    'M',  -- Medium (7,000 - 136,000 kg)
    'H',  -- Heavy (> 136,000 kg)
    'J'   -- Super (A380, AN-225)
);

-- Add new columns to the aircraft table
ALTER TABLE public.aircraft 
ADD COLUMN icao_type_designator VARCHAR(4),           -- e.g., "A320", "B738", "C172"
ADD COLUMN type_description VARCHAR(10),              -- e.g., "L2J", "H1T", "L1P"
ADD COLUMN wake_turbulence_category wake_turbulence_category_enum,
ADD COLUMN aircraft_description TEXT,                 -- Full aircraft name/description
ADD COLUMN operator TEXT,                             -- Owner/Operator name
ADD COLUMN year_built INTEGER,                        -- Year manufactured
ADD COLUMN is_military BOOLEAN DEFAULT FALSE,         -- Military aircraft flag
ADD COLUMN is_interesting BOOLEAN DEFAULT FALSE,      -- Special/interesting aircraft flag
ADD COLUMN is_pia BOOLEAN DEFAULT FALSE,              -- Privacy ICAO Address flag
ADD COLUMN is_ladd BOOLEAN DEFAULT FALSE,             -- Limiting Aircraft Data Displayed flag
ADD COLUMN country_code VARCHAR(2),                   -- Country derived from ICAO hex
ADD COLUMN database_source VARCHAR(50),               -- Source of the data (e.g., "tar1090", "manual")
ADD COLUMN last_updated TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP;

-- Create indexes for performance
CREATE INDEX idx_aircraft_icao_type ON public.aircraft (icao_type_designator);
CREATE INDEX idx_aircraft_operator ON public.aircraft (operator);
CREATE INDEX idx_aircraft_military ON public.aircraft (is_military);
CREATE INDEX idx_aircraft_country ON public.aircraft (country_code);
CREATE INDEX idx_aircraft_wake_category ON public.aircraft (wake_turbulence_category);
CREATE INDEX idx_aircraft_last_updated ON public.aircraft (last_updated);

-- Add comments for documentation
COMMENT ON COLUMN public.aircraft.icao_type_designator IS 'ICAO aircraft type designator (e.g., A320, B738, C172)';
COMMENT ON COLUMN public.aircraft.type_description IS 'ICAO type description code (e.g., L2J = Land plane, 2 engines, Jet)';
COMMENT ON COLUMN public.aircraft.wake_turbulence_category IS 'Wake turbulence category: L=Light, M=Medium, H=Heavy, J=Super';
COMMENT ON COLUMN public.aircraft.aircraft_description IS 'Full aircraft description/model name';
COMMENT ON COLUMN public.aircraft.operator IS 'Aircraft owner/operator name';
COMMENT ON COLUMN public.aircraft.year_built IS 'Year the aircraft was manufactured';
COMMENT ON COLUMN public.aircraft.is_military IS 'True if this is a military aircraft';
COMMENT ON COLUMN public.aircraft.is_interesting IS 'True if this is a special/interesting aircraft (VIP, special livery, etc.)';
COMMENT ON COLUMN public.aircraft.is_pia IS 'True if Privacy ICAO Address (blocked registration)';
COMMENT ON COLUMN public.aircraft.is_ladd IS 'True if Limiting Aircraft Data Displayed';
COMMENT ON COLUMN public.aircraft.country_code IS 'Country code derived from ICAO hex ranges';
COMMENT ON COLUMN public.aircraft.database_source IS 'Source of the aircraft data (tar1090, manual, etc.)';

-- Create a function to update the last_updated timestamp
CREATE OR REPLACE FUNCTION update_aircraft_last_updated()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_updated = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update last_updated
CREATE TRIGGER trg_aircraft_update_timestamp
    BEFORE UPDATE ON public.aircraft
    FOR EACH ROW
    EXECUTE FUNCTION update_aircraft_last_updated();

-- Create a view for enhanced aircraft information
CREATE OR REPLACE VIEW public.aircraft_enhanced AS
SELECT 
    a.hex,
    a.category,
    a.registration,
    a.icao_type_designator,
    a.type_description,
    a.wake_turbulence_category,
    a.aircraft_description,
    a.operator,
    a.year_built,
    a.is_military,
    a.is_interesting,
    a.is_pia,
    a.is_ladd,
    a.country_code,
    a.database_source,
    a.last_updated,
    a.created_at,
    -- Derived fields for convenience
    CASE 
        WHEN a.type_description IS NOT NULL THEN
            CASE 
                WHEN a.type_description LIKE 'H%' THEN 'Helicopter'
                WHEN a.type_description LIKE 'L1P%' THEN 'Single Engine Piston'
                WHEN a.type_description LIKE 'L1T%' THEN 'Single Engine Turboprop'
                WHEN a.type_description LIKE 'L1J%' THEN 'Single Engine Jet'
                WHEN a.type_description LIKE 'L2P%' THEN 'Twin Engine Piston'
                WHEN a.type_description LIKE 'L2T%' THEN 'Twin Engine Turboprop'
                WHEN a.type_description LIKE 'L2J%' THEN 'Twin Engine Jet'
                WHEN a.type_description LIKE 'L3J%' THEN 'Three Engine Jet'
                WHEN a.type_description LIKE 'L4J%' THEN 'Four Engine Jet'
                ELSE 'Other'
            END
        ELSE NULL
    END AS aircraft_class,
    -- Icon suggestion based on TAR1090 logic
    CASE 
        WHEN a.icao_type_designator IN ('A320', 'A321', 'A319', 'B737', 'B738', 'B739', 'B77W', 'B788', 'B789') THEN 'airliner'
        WHEN a.type_description LIKE 'H%' THEN 'helicopter'
        WHEN a.type_description LIKE 'L1P%' OR a.icao_type_designator LIKE 'C1%' THEN 'cessna'
        WHEN a.is_military = true THEN 'military'
        WHEN a.category = 'A7' THEN 'helicopter'
        WHEN a.category IN ('A1', 'A2') THEN 'light'
        WHEN a.category = 'A3' THEN 'airliner'
        WHEN a.category IN ('A4', 'A5') THEN 'heavy'
        ELSE 'unknown'
    END AS suggested_icon
FROM public.aircraft a;

-- Grant permissions (adjust as needed for your setup)
GRANT SELECT ON public.aircraft_enhanced TO PUBLIC;

-- Create a function to parse TAR1090 flags
CREATE OR REPLACE FUNCTION parse_tar1090_flags(flags_string VARCHAR(2))
RETURNS TABLE(
    is_military BOOLEAN,
    is_interesting BOOLEAN
) AS $$
BEGIN
    IF flags_string IS NULL OR LENGTH(flags_string) != 2 THEN
        RETURN QUERY SELECT FALSE, FALSE;
        RETURN;
    END IF;
    
    RETURN QUERY SELECT 
        SUBSTRING(flags_string, 1, 1) = '1' AS is_military,
        SUBSTRING(flags_string, 2, 1) = '1' AS is_interesting;
END;
$$ LANGUAGE plpgsql;

-- Create a function to insert/update aircraft from TAR1090 data
CREATE OR REPLACE FUNCTION upsert_aircraft_from_tar1090(
    p_hex TEXT,
    p_registration TEXT,
    p_icao_type TEXT,
    p_flags VARCHAR(2),
    p_description TEXT DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    v_military BOOLEAN;
    v_interesting BOOLEAN;
BEGIN
    -- Parse flags
    SELECT is_military, is_interesting 
    INTO v_military, v_interesting
    FROM parse_tar1090_flags(p_flags);
    
    -- Upsert aircraft record
    INSERT INTO public.aircraft (
        hex,
        registration,
        icao_type_designator,
        aircraft_description,
        is_military,
        is_interesting,
        database_source,
        last_updated
    ) VALUES (
        p_hex,
        NULLIF(p_registration, ''),
        NULLIF(p_icao_type, ''),
        NULLIF(p_description, ''),
        v_military,
        v_interesting,
        'tar1090',
        CURRENT_TIMESTAMP
    )
    ON CONFLICT (hex) DO UPDATE SET
        registration = COALESCE(EXCLUDED.registration, aircraft.registration),
        icao_type_designator = COALESCE(EXCLUDED.icao_type_designator, aircraft.icao_type_designator),
        aircraft_description = COALESCE(EXCLUDED.aircraft_description, aircraft.aircraft_description),
        is_military = EXCLUDED.is_military,
        is_interesting = EXCLUDED.is_interesting,
        database_source = EXCLUDED.database_source,
        last_updated = EXCLUDED.last_updated;
END;
$$ LANGUAGE plpgsql;

-- Example usage of the upsert function:
-- SELECT upsert_aircraft_from_tar1090('A12345', 'N123AA', 'A320', '00', 'Airbus A320-200');

COMMENT ON FUNCTION upsert_aircraft_from_tar1090 IS 'Insert or update aircraft data from TAR1090 database format';
COMMENT ON FUNCTION parse_tar1090_flags IS 'Parse TAR1090 2-character flag string into boolean fields';
COMMENT ON VIEW public.aircraft_enhanced IS 'Enhanced aircraft view with derived fields and icon suggestions';
