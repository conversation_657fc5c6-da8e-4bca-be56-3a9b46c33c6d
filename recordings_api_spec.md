# Recordings API Specification

This document provides all the information needed to create an API that can fetch recording overviews and specific files from the Cloudflare R2 storage used by the AWOS system.

## Storage Configuration

### Cloudflare R2 Credentials
- **Access Key ID**: `c53df2e89e72fe035e7db5a899c9b4de`
- **Secret Access Key**: `e08273037508b98ce57dc581b58db3b2c0bb1ddbbe1d32d28211cb1c83552ef6`
- **Endpoint URL**: `https://a80f24e552c9303eea94ad9ba226353d.r2.cloudflarestorage.com`
- **Bucket Name**: `recordings`
- **Region**: `auto`

### Storage Structure
```
recordings/
├── {STATION_NAME}/
│   ├── {YYYY}/
│   │   ├── {MM}/
│   │   │   ├── {DD}/
│   │   │   │   ├── {YYYYMMDD_HHMMSS_mmm_dDDDDDD}.webm
│   │   │   │   ├── {YYYYMMDD_HHMMSS_mmm_dDDDDDD}.webm
│   │   │   │   └── ...
│   │   │   └── {DD}/
│   │   │       └── ...
│   │   └── {MM}/
│   │       └── ...
│   └── {YYYY}/
│       └── ...
└── {OTHER_STATION}/
    └── ...
```

### Filename Format
Recordings use the format: `YYYYMMDD_HHMMSS_mmm_dDDDDDD.webm`

Where:
- `YYYYMMDD`: Date (e.g., 20250723)
- `HHMMSS`: Time in 24-hour format (e.g., 143052)
- `mmm`: Milliseconds (000-999)
- `dDDDDDD`: Duration in milliseconds, prefixed with 'd' (e.g., d15750 = 15.75 seconds)
- `.webm`: Audio format (WebM container with Opus codec, optimized for Apple device compatibility)

Example: `20250723_031946_192_d002950.webm`
- Recorded on July 23, 2025 at 03:19:46.192
- Duration: 2.95 seconds (2950 milliseconds)

**Real Examples from Storage**:
- `20250723_031946_192_d002950.webm` - 3.0s duration, ~20.7 KB
- `20250723_034231_742_d008425.webm` - 8.4s duration, ~11.8 KB
- `20250723_043121_078_d004832.webm` - 4.8s duration, ~7.1 KB

## API Endpoints to Implement

### 1. List Recordings for Date and Station

**Endpoint**: `GET /api/recordings/{station}/{date}`

**Parameters**:
- `station` (path): Station identifier (e.g., "4FL5_122900")
- `date` (path): Date in YYYYMMDD format (e.g., "20250723") - will be converted to YYYY/MM/DD for S3 path

**Query Parameters**:
- `format` (optional): Response format ("json" or "csv", default: "json")
- `timezone` (optional): Timezone for display (default: "UTC")

**Response** (JSON):
```json
{
  "station": "4FL5_122900",
  "date": "20250723",
  "timezone": "UTC",
  "total_recordings": 16,
  "total_duration_seconds": 54.1,
  "total_size_bytes": 96384,
  "recordings": [
    {
      "filename": "20250723_031946_192_d002950.webm",
      "start_time": "2025-07-23T03:19:46.192Z",
      "duration_ms": 2950,
      "duration_seconds": 2.95,
      "size_bytes": 21215,
      "download_url": "/api/recordings/4FL5_122900/20250723/20250723_031946_192_d002950.webm"
    },
    {
      "filename": "20250723_034231_742_d008425.webm",
      "start_time": "2025-07-23T03:42:31.742Z",
      "duration_ms": 8425,
      "duration_seconds": 8.425,
      "size_bytes": 12083,
      "download_url": "/api/recordings/4FL5_122900/20250723/20250723_034231_742_d008425.webm"
    }
  ]
}
```

### 2. Download Specific Recording

**Endpoint**: `GET /api/recordings/{station}/{date}/{filename}`

**Parameters**:
- `station` (path): Station identifier
- `date` (path): Date in YYYYMMDD format
- `filename` (path): Recording filename

**Response**: Binary audio file (WebM format with Opus codec)

**Headers**:
- `Content-Type: video/webm`
- `Content-Disposition: attachment; filename="{filename}"`
- `Content-Length: {size_bytes}`

### 3. List Available Stations

**Endpoint**: `GET /api/stations`

**Response**:
```json
{
  "stations": [
    {
      "name": "4FL5_122900",
      "last_recording": "2025-07-23T18:45:30.000Z",
      "total_recordings": 1250
    }
  ]
}
```

### 4. List Available Dates for Station

**Endpoint**: `GET /api/recordings/{station}/dates`

**Parameters**:
- `station` (path): Station identifier

**Query Parameters**:
- `from_date` (optional): Start date (YYYYMMDD)
- `to_date` (optional): End date (YYYYMMDD)

**Response**:
```json
{
  "station": "4FL5_122900",
  "dates": [
    {
      "date": "20250723",
      "recording_count": 16,
      "total_duration_seconds": 54.1,
      "total_size_bytes": 96384
    }
  ]
}
```

## Implementation Details

### S3 Operations Required

1. **ListObjects** (Class A operation): Used to list recordings for a date/station
   - Prefix: `{station}/{YYYY}/{MM}/{DD}/` (e.g., "4FL5_122900/2025/07/23/")
   - Max 1000 objects per request (sufficient for daily recordings)
   - Note: Input date YYYYMMDD must be converted to YYYY/MM/DD format for S3 path

2. **GetObject** (Class B operation): Used to download specific recordings
   - Key: `{station}/{YYYY}/{MM}/{DD}/{filename}` (e.g., "4FL5_122900/2025/07/23/20250723_031946_192_d002950.webm")

### Error Handling

**HTTP Status Codes**:
- `200`: Success
- `404`: Station, date, or file not found
- `400`: Invalid date format or parameters
- `500`: Internal server error (S3 connection issues)

**Error Response Format**:
```json
{
  "error": "not_found",
  "message": "No recordings found for station 4FL5_122900 on 20250723",
  "details": {
    "station": "4FL5_122900",
    "date": "20250723"
  }
}
```

### Performance Considerations

1. **Caching**: Implement caching for station lists and date listings
2. **Pagination**: For stations with >1000 recordings per day, implement pagination
3. **Compression**: Enable gzip compression for JSON responses
4. **CDN**: Consider CloudFlare CDN for file downloads

### Security Considerations

1. **Rate Limiting**: Implement rate limiting to prevent abuse
2. **Authentication**: Consider API key authentication for production use
3. **CORS**: Configure appropriate CORS headers for web clients
4. **Input Validation**: Validate all input parameters

### Example Implementation (Python/FastAPI)

```python
import boto3
from fastapi import FastAPI, HTTPException
from datetime import datetime
import re

app = FastAPI()

# S3 client configuration
s3_client = boto3.client(
    's3',
    aws_access_key_id="c53df2e89e72fe035e7db5a899c9b4de",
    aws_secret_access_key="e08273037508b98ce57dc581b58db3b2c0bb1ddbbe1d32d28211cb1c83552ef6",
    endpoint_url="https://a80f24e552c9303eea94ad9ba226353d.r2.cloudflarestorage.com",
    region_name="auto"
)

@app.get("/api/recordings/{station}/{date}")
async def list_recordings(station: str, date: str):
    # Convert YYYYMMDD to YYYY/MM/DD format for S3 path
    if len(date) == 8:
        formatted_date = f"{date[:4]}/{date[4:6]}/{date[6:8]}"
    else:
        formatted_date = date

    prefix = f"{station}/{formatted_date}/"
    # Implementation here
    pass

@app.get("/api/recordings/{station}/{date}/{filename}")
async def download_recording(station: str, date: str, filename: str):
    # Convert date format for S3 key
    if len(date) == 8:
        formatted_date = f"{date[:4]}/{date[4:6]}/{date[6:8]}"
    else:
        formatted_date = date

    key = f"{station}/{formatted_date}/{filename}"
    # Implementation here
    pass
```

### Testing

Use the provided `fetch_recordings.py` script to test the storage structure and validate the API implementation:

```bash
python fetch_recordings.py 2025/07/23 4FL5_122900
```

This will help verify that the API correctly parses filenames and returns the expected data structure.
