# Production docker-compose for Coolify
services:
  # FastAPI Backend
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    environment:
      # TimescaleDB Cloud connection - replace with your actual connection string
      #- DATABASE_URL=postgresql+asyncpg://tsdbadmin:<EMAIL>:35750/tsdb?ssl=require
      - DATABASE_URL=postgresql+asyncpg://tsdbadmin:<EMAIL>:34087/tsdb?ssl=require
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - ENVIRONMENT=production
      # Coolify will generate free domains - these will be auto-populated
      - ALLOWED_ORIGINS=["*"]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # # Next.js Frontend
  # webapp:
  #   build:
  #     context: ./webapp
  #     dockerfile: Dockerfile
  #     target: runner
  #     args:
  #       - NEXT_PUBLIC_API_URL=http://zockk8wkcks448kc4kc4ccw8.************.sslip.io
  #   environment:
  #     - NODE_ENV=production
  #     # This will be updated to use Coolify's generated API domain
  #     - NEXT_PUBLIC_API_URL=http://zockk8wkcks448kc4kc4ccw8.************.sslip.io
  #   depends_on:
  #     - api
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:3000"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3 