-- Ensure PostGIS is enabled
CREATE EXTENSION IF NOT EXISTS postgis;

-- Enable the core H3 extension
CREATE EXTENSION IF NOT EXISTS h3;


CREATE EXTENSION IF NOT EXISTS postgis_raster;

-- Enable the PostGIS integration for H3
CREATE EXTENSION IF NOT EXISTS h3_postgis;

CREATE TYPE public.aircraft_category_enum AS ENUM (
    'A0',
    'A1',
    'A2',
    'A3',
    'A4',
    'A5',
    'A6',
    'A7',
    'B0',
    'B1',
    'B2',
    'B3',
    'B4',
    'B5',
    'B6',
    'B7',
    'C0',
    'C1',
    'C2',
    'C3',
    'C4',
    'C5',
    'C6',
    'C7',
    'D0',
    'D1',
    'D2',
    'D3',
    'D4',
    'D5',
    'D6',
    'D7',
    'UNKNOWN'
);

CREATE TYPE public.runway_surface_enum AS ENUM (
    'ASPHALT',
    'CONCRETE',
    'GRASS',
    'GRAVEL',
    'DIRT',
    'WATER',
    'UNKNOWN',
    'OTHER'
);

CREATE TYPE public.flight_phase_enum AS ENUM (
    'ground',
    'takeoff',
    'landing',
    'airborne'
);

CREATE FUNCTION public.detect_takeoff_landing() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    prev_time timestamptz;
    prev_phase public.flight_phase_enum;
BEGIN
    -- Only query if we need to (when inserting airborne or ground)
    IF NEW.flight_phase = 'airborne' OR NEW.flight_phase = 'ground' THEN
        -- Select only what we need instead of entire record
        SELECT "time", flight_phase INTO prev_time, prev_phase
        FROM aircraft_positions 
        WHERE hex = NEW.hex 
          AND "time" > (NEW.time - interval '11 seconds')
          AND "time" < NEW.time
        ORDER BY "time" DESC 
        LIMIT 1;
        
        -- If previous record exists, check for state transitions
        IF FOUND THEN
            -- Detect takeoff: ground → airborne
            IF NEW.flight_phase = 'airborne' AND prev_phase = 'ground' THEN
                NEW.flight_phase := 'takeoff';
            
            -- Detect landing: airborne → ground
            ELSIF NEW.flight_phase = 'ground' AND prev_phase = 'airborne' THEN
                -- Update the previous record to be 'landing' instead of 'airborne'
                UPDATE aircraft_positions 
                SET flight_phase = 'landing'
                WHERE hex = NEW.hex AND "time" = prev_time;
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;

CREATE TABLE public.aircraft (
	hex text NOT NULL,
	category public."aircraft_category_enum" NULL,
	registration text NULL,
	CONSTRAINT aircraft_pkey PRIMARY KEY (hex),
	CONSTRAINT aircraft_registration_key UNIQUE (registration)
);

CREATE TABLE public.airports (
	icao varchar(4) NOT NULL,
	iata varchar(3) NULL,
	"name" varchar(255) NULL,
	"location" public.geography(point, 4326) NULL,
	elevation_ft int4 NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT airports_pkey PRIMARY KEY (icao)
);

CREATE TABLE public.runways (
	airport_icao varchar(4) NOT NULL,
	"name" varchar(10) NOT NULL,
	length_ft int4 NULL,
	width_ft int4 NULL,
	surface public."runway_surface_enum" NOT NULL,
	centerline public.geography(linestring, 4326) NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT runways_pkey PRIMARY KEY (airport_icao, name)
);


-- public.runways foreign keys

ALTER TABLE public.runways ADD CONSTRAINT runways_airport_icao_fkey FOREIGN KEY (airport_icao) REFERENCES public.airports(icao) ON DELETE CASCADE ON UPDATE CASCADE;

CREATE TABLE public.aircraft_pos (
	"time" timestamptz NOT NULL,
	hex text NOT NULL,
	alt_baro int4 NULL,
	alt_geom int4 NULL,
	gs int2 NULL,
	ias int2 NULL,
	track int2 NULL,
	baro_rate int2 NULL,
	squawk int2 NULL,
	flight text NULL,
	h3_index public.h3index NULL,
	flight_phase public."flight_phase_enum" NULL,
	h3_res4 public.h3index GENERATED ALWAYS AS (
CASE
    WHEN h3_index IS NOT NULL THEN h3_cell_to_parent(h3_index, 4)
    ELSE NULL::h3index
END) STORED NULL,
	CONSTRAINT unique_hex_time_pos2 UNIQUE (hex, "time")
);
CREATE INDEX aircraft_pos2_flight_phase_idx ON public.aircraft_pos USING btree (flight_phase);
CREATE INDEX aircraft_pos2_h3_res4_idx ON public.aircraft_pos USING btree (h3_res4);
CREATE INDEX aircraft_pos2_time_idx ON public.aircraft_pos USING btree ("time" DESC);

-- Table Triggers

create trigger ts_insert_blocker before
insert
    on
    public.aircraft_pos for each row execute function _timescaledb_functions.insert_blocker();
create trigger trg_detect_flight_phase_unified before
insert
    on
    public.aircraft_pos for each row execute function detect_takeoff_landing_unified();
create trigger trg_derive_track_data before
insert
    on
    public.aircraft_pos for each row execute function derive_track_data();




    CREATE FUNCTION public.derive_track_data() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    prev_time timestamptz;
    prev_h3_index public.h3index;
    prev_track int2;
    prev_point point;
    current_point point;
    prev_lat float8;
    prev_lon float8;
    current_lat float8;
    current_lon float8;
    grid_distance int4;
    calculated_track float8;
BEGIN
    -- This function will be called BEFORE INSERT.
    -- NEW refers to the row that is about to be inserted.

    -- Only process if track is NULL and h3_index is available
    IF NEW.track IS NULL AND NEW.h3_index IS NOT NULL THEN
        
        RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - Track is NULL, attempting to derive.', NEW.hex, NEW.time;

        -- Get the most recent position data for this aircraft
        SELECT "time", h3_index, track
        INTO prev_time, prev_h3_index, prev_track
        FROM public.aircraft_pos
        WHERE hex = NEW.hex
          AND "time" < NEW."time" -- Strictly older records
          AND "time" >= (NEW."time" - INTERVAL '30 seconds') -- Within 30-second window
          AND h3_index IS NOT NULL -- Must have h3_index for grid distance calculation
        ORDER BY "time" DESC
        LIMIT 1;

        -- If a previous record was found
        IF FOUND THEN
            RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - Found previous record at time:%.', NEW.hex, NEW.time, prev_time;
            
            -- Calculate grid distance using h3_grid_distance function
            grid_distance := h3_grid_distance(prev_h3_index, NEW.h3_index);
            
            RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - Grid distance: %.', NEW.hex, NEW.time, grid_distance;
            
            -- If grid distance is at least 2, calculate track based on bearing
            IF grid_distance >= 2 THEN
                -- Get lat/lon coordinates from h3 indices using h3_cell_to_lat_lng
                prev_point := h3_cell_to_lat_lng(prev_h3_index);
                current_point := h3_cell_to_lat_lng(NEW.h3_index);
                
                -- Extract lat/lon from PostgreSQL point type (x=longitude, y=latitude)
                prev_lat := prev_point[1];  -- y component = latitude
                prev_lon := prev_point[0];  -- x component = longitude
                current_lat := current_point[1];  -- y component = latitude
                current_lon := current_point[0];  -- x component = longitude
                
                -- Calculate bearing in degrees using atan2
                -- Formula: θ = atan2(sin(Δlong).cos(lat2), cos(lat1).sin(lat2) − sin(lat1).cos(lat2).cos(Δlong))
                -- Convert result from radians to degrees and normalize to 0-360 range
                calculated_track := degrees(
                    atan2(
                        sin(radians(current_lon - prev_lon)) * cos(radians(current_lat)),
                        cos(radians(prev_lat)) * sin(radians(current_lat)) - 
                        sin(radians(prev_lat)) * cos(radians(current_lat)) * cos(radians(current_lon - prev_lon))
                    )
                );
                
                -- Normalize to 0-360 range
                IF calculated_track < 0 THEN
                    calculated_track := calculated_track + 360;
                END IF;
                
                -- Round to nearest integer for track field (int2)
                NEW.track := round(calculated_track)::int2;
                
                RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - Calculated track: % degrees.', NEW.hex, NEW.time, NEW.track;
                
            -- If grid distance is less than 2, copy previous track
            ELSIF prev_track IS NOT NULL THEN
                NEW.track := prev_track;
                RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - Copied previous track: % degrees.', NEW.hex, NEW.time, NEW.track;
            ELSE
                RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - Previous track is NULL, cannot copy.', NEW.hex, NEW.time;
            END IF;
            
        ELSE
            RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - No previous record found in 30-second window.', NEW.hex, NEW.time;
        END IF;
        
    ELSIF NEW.track IS NOT NULL THEN
        RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - Track data provided: % degrees.', NEW.hex, NEW.time, NEW.track;
    ELSE
        RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - No h3_index available, cannot derive track.', NEW.hex, NEW.time;
    END IF;

    RETURN NEW; -- Return the (potentially modified) row
END;
$$;

CREATE FUNCTION public.detect_takeoff_landing_unified() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    prev_time timestamptz;
    prev_phase public.flight_phase_enum;
BEGIN
    -- This function will be called BEFORE INSERT.
    -- NEW refers to the row that is about to be inserted.

    -- Only query if the new row's flight_phase needs evaluation
    IF NEW.flight_phase = 'airborne' OR NEW.flight_phase = 'ground' THEN

        RAISE NOTICE '[UNIFIED_TRIGGER] For NEW hex:%, time:%, incoming_phase:% - Evaluating.', NEW.hex, NEW.time, NEW.flight_phase;

        -- Select the time and flight_phase of the single most recent record *prior* to the NEW record
        SELECT "time", flight_phase
        INTO prev_time, prev_phase
        FROM public.aircraft_pos -- Ensure this is your current table name
        WHERE hex = NEW.hex
          AND "time" < NEW."time" -- Strictly older records
          AND "time" >= (NEW."time" - INTERVAL '11 seconds') -- Within the 11-second window
        ORDER BY "time" DESC
        LIMIT 1;

        -- If a previous record was found within the window
        IF FOUND THEN
            RAISE NOTICE '[UNIFIED_TRIGGER] For NEW hex:%, time:% - Found previous record (time:%, phase:%).', NEW.hex, NEW.time, prev_time, prev_phase;

            -- Detect takeoff: current is 'airborne', previous was 'ground'
            IF NEW.flight_phase = 'airborne' AND prev_phase = 'ground' THEN
                RAISE NOTICE '[UNIFIED_TRIGGER] For NEW hex:%, time:% - TAKEOFF detected. Changing NEW.flight_phase to ''takeoff''.', NEW.hex, NEW.time;
                NEW.flight_phase := 'takeoff'; -- Modify the NEW row being inserted

            -- Detect landing: current is 'ground', previous was 'airborne'
            ELSIF NEW.flight_phase = 'ground' AND prev_phase = 'airborne' THEN
                RAISE NOTICE '[UNIFIED_TRIGGER] For NEW hex:%, time:% - LANDING detected. Updating previous row (time:%) to ''landing''.', NEW.hex, NEW.time, prev_time;
                -- Update the PREVIOUS record to be 'landing'
                UPDATE public.aircraft_pos -- Ensure this is your current table name
                SET flight_phase = 'landing'
                WHERE hex = NEW.hex AND "time" = prev_time; -- Target the specific previous record
            ELSE
                 RAISE NOTICE '[UNIFIED_TRIGGER] For NEW hex:%, time:% - No takeoff/landing condition met with previous phase ''%''.', NEW.hex, NEW.time, prev_phase;
            END IF;
        ELSE
            RAISE NOTICE '[UNIFIED_TRIGGER] For NEW hex:%, time:% - NO previous record found in window.', NEW.hex, NEW.time;
        END IF;
    END IF;

    RETURN NEW; -- Crucial for a BEFORE trigger, return the (potentially modified) row
END;
$$;