# Airport Images Implementation - Deployment Guide

This guide walks you through deploying the airport images feature for your Skytraces application.

## 🎯 Overview

The implementation adds:
- **Database**: `image_url` column in airports table
- **API**: Image upload, delete, and info endpoints
- **Storage**: Backblaze B2 integration with image processing
- **Frontend**: Admin interface and updated airport display
- **Processing**: Automatic image optimization (resize, WebP/JPEG conversion)

## 📋 Prerequisites

1. **Backblaze B2 Account** with credentials:
   - Key ID: `0056deeab75af970000000003`
   - Application Key: `K005cayC+sNRkiHzX8y2gTmpuNWAdIQ`
   - Bucket: `skytraces`

2. **Database Access** to run migration scripts

3. **API Deployment** access to update environment variables

## 🚀 Deployment Steps

### Step 1: Database Migration

Run the database migration to add the `image_url` column:

```sql
-- Connect to your database and run:
\i scripts/database/add_missing_airport_columns.sql
```

Or manually:
```sql
ALTER TABLE airports ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) NULL;
ALTER TABLE airports ADD COLUMN IF NOT EXISTS image_url VARCHAR(500) NULL;
```

### Step 2: Update API Environment Variables

Add these environment variables to your API deployment:

```bash
B2_KEY_ID=0056deeab75af970000000003
B2_APPLICATION_KEY=K005cayC+sNRkiHzX8y2gTmpuNWAdIQ
B2_BUCKET_NAME=skytraces
```

### Step 3: Deploy API Changes

The API now includes these new dependencies in `requirements.txt`:
- `pillow>=10.0.0` (image processing)
- `b2sdk>=1.24.0` (Backblaze B2 integration)
- `python-multipart>=0.0.6` (file upload support)

Deploy the updated API with the new code.

### Step 4: Deploy Frontend Changes

Deploy the updated landing webapp with:
- Updated `Airport` type to include `image_url`
- New `/admin` page for image management
- Updated `AirportHeader` component to display images

### Step 5: Test the Implementation

1. **Test API endpoints**:
   ```bash
   python scripts/test_airport_images.py
   ```

2. **Test admin interface**:
   - Visit `/admin` page
   - Upload an image for airport `4FL5`
   - Verify the upload succeeds

3. **Test airport display**:
   - Visit `/4FL5` page
   - Verify the uploaded image displays instead of placeholder

## 🔧 API Endpoints

### Get Airport (Updated)
```
GET /api/v1/airports/{icao}
```
Now returns `image_url` field.

### Upload Airport Image
```
POST /api/v1/airports/{icao}/image
Content-Type: multipart/form-data

file: [image file]
```

### Delete Airport Image
```
DELETE /api/v1/airports/{icao}/image
```

### Get Image Info
```
GET /api/v1/airports/{icao}/image/info
```

## 🖼️ Image Processing Features

- **Automatic resizing**: Max 1200x600px maintaining aspect ratio
- **Format optimization**: WebP (preferred) or JPEG fallback
- **Quality optimization**: 85% quality with optimization enabled
- **Background handling**: Transparent images get white background
- **Size validation**: 10MB maximum file size
- **Type validation**: Only image files accepted

## 🗂️ Storage Structure

Images are stored in Backblaze B2 with this structure:
```
skytraces/
└── airports/
    └── {ICAO}/
        └── image.webp (or image.jpg)
```

Example: `skytraces/airports/4FL5/image.webp`

## 🔍 Testing Checklist

- [ ] Database migration completed
- [ ] API environment variables set
- [ ] API deployment successful
- [ ] Frontend deployment successful
- [ ] Can access `/admin` page
- [ ] Can upload image via admin interface
- [ ] Image appears in Backblaze B2 bucket
- [ ] Airport API returns `image_url`
- [ ] Airport page displays uploaded image
- [ ] Can delete image via admin interface
- [ ] Placeholder shows when no image

## 🐛 Troubleshooting

### API Issues
- **B2 authentication fails**: Check environment variables
- **Image upload fails**: Check file size (<10MB) and type (image/*)
- **Processing fails**: Check Pillow installation

### Frontend Issues
- **Admin page not loading**: Check route deployment
- **Image not displaying**: Check CORS settings and image URL
- **Upload button disabled**: Check ICAO format (4 characters)

### Storage Issues
- **B2 bucket access**: Verify CORS settings allow your domain
- **Image not found**: Check bucket name and file path structure

## 📝 Next Steps

1. **Test with 4FL5**: Upload a high-quality image of the airport
2. **Monitor performance**: Check image load times and optimization
3. **Scale to other airports**: Use admin interface for more airports
4. **Consider CDN**: Backblaze B2 supports CDN integration for faster delivery

## 🔒 Security Notes

- Images are publicly accessible via Backblaze B2 URLs
- Admin interface has no authentication (add if needed)
- File uploads are validated for type and size
- B2 credentials should be kept secure in environment variables

## 📊 Monitoring

Monitor these metrics:
- Image upload success rate
- Image processing time
- Storage costs in Backblaze B2
- Page load times with images
- User engagement with visual airport pages
