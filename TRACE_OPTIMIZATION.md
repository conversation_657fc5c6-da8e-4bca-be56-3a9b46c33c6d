# Aircraft Trace Payload Optimization

## Overview

This document describes the optimizations made to the aircraft trace functionality to significantly reduce payload size while maintaining full geographic trail information.

## Changes Made

### Backend Optimizations

#### 1. Simplified Trace Schema (`api/app/schemas/aircraft.py`)
- **Before**: `AircraftTracePoint` included 9 fields (time, alt_baro, alt_geom, gs, ias, track, baro_rate, h3_index, flight_phase)
- **After**: `AircraftTracePoint` only includes `h3_index` (required string field)
- **Impact**: ~90% reduction in trace data payload size

#### 2. Updated Service Layer (`api/app/services/aircraft_service.py`)
- Modified `_get_aircraft_trace()` to only fetch `h3_index` from database
- Added null checking to ensure only valid H3 cells are included in traces
- Updated default lookback time from 11 to 40 seconds for longer trails
- **Impact**: Reduced database query complexity and response payload

#### 3. Updated API Router (`api/app/routers/aircraft.py`)
- Changed default `lookback_seconds` from 11 to 40 seconds
- Updated API documentation to reflect payload optimization
- **Impact**: Longer trails by default with optimal payload size

### Frontend Optimizations

#### 1. Updated Type Definitions (`webapp/src/types/api.ts`)
- Simplified `AircraftTracePoint` interface to only include `h3_index: string`
- **Impact**: Type safety for optimized payload structure

#### 2. Updated Configuration (`webapp/src/lib/aircraft-config.ts`)
- Changed `DEFAULT_LOOKBACK_SECONDS` from 11 to 40 seconds
- **Impact**: Consistent longer trails across frontend and backend

#### 3. Component Compatibility
- `AircraftTrails.tsx`: Already compatible (was accessing `h3_index` properly)
- `AircraftMarkers.tsx`: Trace count display continues to work correctly
- **Impact**: No breaking changes to visualization components

## Performance Benefits

### Payload Size Reduction
- **Before**: Each trace point contained ~150-200 bytes of data
- **After**: Each trace point contains ~15-20 bytes of data  
- **Total Reduction**: ~90% smaller trace payloads

### Example Impact
For an aircraft with 50 trace points:
- **Before**: ~7,500-10,000 bytes per aircraft trace
- **After**: ~750-1,000 bytes per aircraft trace
- **Savings**: ~6,750-9,000 bytes per aircraft

With 100 aircraft on screen, this saves approximately 675KB-900KB per API response.

### Longer Trails
- **Before**: 11-second lookback window
- **After**: 40-second lookback window
- **Benefit**: ~3.6x longer historical trails for better flight path visualization

## Data Integrity

### What's Preserved
- ✅ Full geographic position history (H3 cells)
- ✅ Chronological ordering (oldest first)
- ✅ Trail visualization accuracy
- ✅ Current position metadata (altitude, speed, etc.)

### What's Optimized
- ❌ Historical altitude data in traces (available in current position)
- ❌ Historical speed data in traces (available in current position) 
- ❌ Historical timestamps in traces (ordering preserved)
- ❌ Duplicate aircraft metadata in each trace point

## Backward Compatibility

- ✅ All existing trail visualization functionality preserved
- ✅ Current aircraft position data unchanged
- ✅ API response structure unchanged (only trace point schema simplified)
- ✅ No breaking changes to frontend components

## Technical Details

### Database Query Optimization
```sql
-- Before: Selected 9 fields per trace point
SELECT time, alt_baro, alt_geom, gs, ias, track, baro_rate, h3_index, flight_phase
FROM aircraft_pos WHERE...

-- After: Only selects geographic position
SELECT h3_index FROM aircraft_pos WHERE...
```

### Response Size Comparison
```json
// Before (per trace point)
{
  "time": "2024-01-01T12:00:00Z",
  "alt_baro": 35000,
  "alt_geom": 35100,
  "gs": 450,
  "ias": 280,
  "track": 270,
  "baro_rate": 0,
  "h3_index": "8428309ffffffff",
  "flight_phase": "airborne"
}

// After (per trace point)
{
  "h3_index": "8428309ffffffff"
}
```

## Next Steps

1. **Monitor Performance**: Track API response times and payload sizes in production
2. **User Feedback**: Observe if longer trails (40s vs 11s) provide better insights
3. **Future Enhancements**: Consider adding optional detail levels for trace data
4. **Analytics**: Measure actual bandwidth savings in production environment

## Configuration

All settings can be adjusted in:
- **Backend**: `api/app/routers/aircraft.py` - `lookback_seconds` parameter
- **Frontend**: `webapp/src/lib/aircraft-config.ts` - `DEFAULT_LOOKBACK_SECONDS`
- **Trace Length**: `max_trace_points` parameter (0-200, default: 50)

## Rollback Plan

If needed, the previous trace schema can be restored by:
1. Reverting changes to `AircraftTracePoint` schema
2. Updating `_get_aircraft_trace()` to fetch all fields
3. Adjusting frontend types accordingly

The database schema remains unchanged, so rollback is non-destructive. 