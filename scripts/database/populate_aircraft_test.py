#!/usr/bin/env python3
"""
Test version of the fixed population script that processes only a few files.
"""

import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional
import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection string for TimescaleDB
DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?sslmode=require"

# Path to your exported TAR1090 database files
DATABASE_PATH = Path(__file__).parent / 'database'

def parse_tar1090_flags(flags_string: str) -> tuple[bool, bool]:
    """Parse TAR1090 2-character flag string into boolean fields."""
    if not flags_string or len(flags_string) != 2:
        return False, False
    
    is_military = flags_string[0] == '1'
    is_interesting = flags_string[1] == '1'
    return is_military, is_interesting

def get_country_from_hex(hex_code: str) -> Optional[str]:
    """Get country code from ICAO hex code based on known ranges."""
    if not hex_code:
        return None
    
    hex_upper = hex_code.upper()
    first_char = hex_upper[0]
    
    country_mapping = {
        '0': 'US', '1': 'US', '2': 'US', '4': 'US',
        '3': 'DE', 'A': 'US', 'B': 'US', 'C': 'CA', 
        'D': 'DE', 'E': 'GB', 'F': 'FR'
    }
    
    if hex_upper.startswith('3D'):
        return 'DE'
    elif hex_upper.startswith('C0'):
        return 'CA'
    elif hex_upper.startswith('40'):
        return 'GB'
    
    return country_mapping.get(first_char)

def process_aircraft_file(file_path: Path) -> list[Dict[str, Any]]:
    """Process a single aircraft database file and return aircraft records."""
    aircraft_records = []
    
    try:
        if file_path.stat().st_size == 0:
            logger.warning(f"File {file_path.name} is empty, skipping")
            return aircraft_records
            
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not data or not isinstance(data, dict):
            logger.warning(f"File {file_path.name} contains no valid aircraft data")
            return aircraft_records
        
        file_prefix = file_path.stem.upper()
        
        for hex_suffix, aircraft_data in data.items():
            if hex_suffix == 'children':
                continue
            
            try:
                full_hex_code = (file_prefix + hex_suffix).lower()
                
                if not isinstance(aircraft_data, dict):
                    continue
                
                registration = aircraft_data.get('r', '').strip() if aircraft_data.get('r') else ''
                icao_type = aircraft_data.get('t', '').strip() if aircraft_data.get('t') else ''
                flags = aircraft_data.get('f', '00') if aircraft_data.get('f') else '00'
                description = aircraft_data.get('desc', '').strip() if aircraft_data.get('desc') else ''
                
                if not hex_suffix or len(hex_suffix) < 1:
                    continue
                
                is_military, is_interesting = parse_tar1090_flags(flags)
                country_code = get_country_from_hex(full_hex_code)
                
                aircraft_record = {
                    'hex': full_hex_code,
                    'registration': registration if registration and registration != 'DEFAULT' else None,
                    'icao_type_designator': icao_type if icao_type else None,
                    'type_description': None,
                    'wake_turbulence_category': None,
                    'aircraft_description': description if description else None,
                    'is_military': is_military,
                    'is_interesting': is_interesting,
                    'country_code': country_code,
                    'database_source': 'tar1090'
                }
                
                aircraft_records.append(aircraft_record)
                
            except Exception as e:
                logger.warning(f"Error processing aircraft {hex_suffix} in {file_path.name}: {e}")
                continue
            
    except Exception as e:
        logger.error(f"Unexpected error processing file {file_path.name}: {e}")
    
    return aircraft_records

def insert_single_aircraft(cursor, aircraft_record: Dict[str, Any]) -> bool:
    """Insert a single aircraft record into the database."""
    insert_sql = """
    INSERT INTO aircraft (
        hex, registration, icao_type_designator, type_description, 
        wake_turbulence_category, aircraft_description, is_military, 
        is_interesting, country_code, database_source, last_updated, created_at
    ) VALUES (
        %(hex)s, %(registration)s, %(icao_type_designator)s, %(type_description)s,
        %(wake_turbulence_category)s, %(aircraft_description)s, %(is_military)s,
        %(is_interesting)s, %(country_code)s, %(database_source)s, 
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    ON CONFLICT (hex) DO NOTHING
    """
    
    try:
        cursor.execute(insert_sql, aircraft_record)
        return cursor.rowcount > 0
    except Exception as e:
        logger.warning(f"Failed to insert aircraft {aircraft_record.get('hex', 'unknown')}: {e}")
        return False

def main():
    """Test the fixed population script with a few files."""
    if not DATABASE_PATH.exists():
        logger.error(f"Database path does not exist: {DATABASE_PATH}")
        sys.exit(1)
    
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        logger.info("Connected to database")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        sys.exit(1)
    
    try:
        total_processed = 0
        total_inserted = 0
        commit_frequency = 5  # Commit every 5 inserts for testing
        insert_count = 0
        
        # Process only first 2 JSON files for testing
        json_files = list(DATABASE_PATH.glob('*.json'))
        json_files = [f for f in json_files if f.name != 'icao_aircraft_types.json']
        test_files = json_files[:2]  # Only first 2 files
        
        logger.info(f"Testing with {len(test_files)} files: {[f.name for f in test_files]}")
        
        for i, file_path in enumerate(test_files):
            logger.info(f"Processing file {i+1}/{len(test_files)}: {file_path.name}")
            
            aircraft_records = process_aircraft_file(file_path)
            
            if aircraft_records:
                logger.info(f"Found {len(aircraft_records)} aircraft in {file_path.name}")
                total_processed += len(aircraft_records)
                
                # Insert aircraft one by one
                file_inserted = 0
                for j, aircraft_record in enumerate(aircraft_records):
                    if j >= 20:  # Limit to first 20 aircraft per file for testing
                        break
                        
                    if insert_single_aircraft(cursor, aircraft_record):
                        file_inserted += 1
                        total_inserted += 1
                        insert_count += 1
                        
                        # Commit every commit_frequency inserts
                        if insert_count % commit_frequency == 0:
                            conn.commit()
                            logger.info(f"✅ Committed {commit_frequency} inserts. Total: {total_inserted}")
                
                logger.info(f"✅ Inserted {file_inserted} aircraft from {file_path.name}")
            else:
                logger.info(f"No aircraft found in {file_path.name}")
        
        # Final commit
        conn.commit()
        logger.info(f"✅ Final commit completed")
        
        logger.info(f"🎉 Test complete! Processed {total_processed} aircraft, inserted {total_inserted}")
        
        # Show sample results
        cursor.execute("SELECT hex, registration, icao_type_designator, country_code FROM aircraft WHERE database_source = 'tar1090' ORDER BY created_at DESC LIMIT 10")
        results = cursor.fetchall()
        logger.info("Sample inserted aircraft:")
        for row in results:
            logger.info(f"  {row['hex']}: {row['registration']} ({row['icao_type_designator']}) - {row['country_code']}")
        
    except Exception as e:
        logger.error(f"Error during processing: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    main()
