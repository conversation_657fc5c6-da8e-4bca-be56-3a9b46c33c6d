#!/usr/bin/env python3
"""
Quick database optimization - just the essential commands.
"""

import psycopg2
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?sslmode=require"

def main():
    logger.info("🚀 Quick database optimization...")
    
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        # Essential optimizations only
        logger.info("Dropping registration constraint...")
        cursor.execute("ALTER TABLE aircraft DROP CONSTRAINT IF EXISTS aircraft_registration_key")
        
        logger.info("Dropping redundant index...")
        cursor.execute("DROP INDEX IF EXISTS ix_aircraft_hex")
        
        logger.info("Dropping trigger...")
        cursor.execute("DROP TRIGGER IF EXISTS trg_aircraft_update_timestamp ON aircraft")
        
        conn.commit()
        logger.info("✅ Optimization complete!")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Error: {e}")

if __name__ == "__main__":
    main()
