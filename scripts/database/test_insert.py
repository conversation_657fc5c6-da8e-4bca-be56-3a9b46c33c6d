#!/usr/bin/env python3
"""
Simple test script to verify database connection and insert functionality.
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection string for TimescaleDB
DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?sslmode=require"

def test_connection():
    """Test basic database connection."""
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        logger.info("✅ Database connection successful")
        
        # Test basic query
        cursor.execute("SELECT version()")
        version = cursor.fetchone()
        logger.info(f"Database version: {version['version']}")
        
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False

def test_schema():
    """Test if the aircraft table schema is correct."""
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check if aircraft table exists and get its structure
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'aircraft' 
            ORDER BY ordinal_position
        """)
        
        columns = cursor.fetchall()
        if not columns:
            logger.error("❌ Aircraft table not found")
            return False
        
        logger.info("✅ Aircraft table found with columns:")
        for col in columns:
            logger.info(f"  - {col['column_name']}: {col['data_type']} (nullable: {col['is_nullable']})")
        
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"❌ Schema check failed: {e}")
        return False

def test_simple_insert():
    """Test inserting a single aircraft record."""
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        # Test data
        test_aircraft = {
            'hex': 'test001',
            'registration': 'N-TEST1',
            'icao_type_designator': 'A320',
            'type_description': 'L2J',
            'wake_turbulence_category': 'M',
            'aircraft_description': 'Test Aircraft',
            'operator': 'Test Airlines',
            'year_built': 2020,
            'is_military': False,
            'is_interesting': False,
            'is_pia': False,
            'is_ladd': False,
            'country_code': 'US',
            'database_source': 'test'
        }
        
        # Insert query
        insert_sql = """
        INSERT INTO aircraft (
            hex, registration, icao_type_designator, type_description, 
            wake_turbulence_category, aircraft_description, operator, year_built,
            is_military, is_interesting, is_pia, is_ladd, 
            country_code, database_source, last_updated, created_at
        ) VALUES (
            %(hex)s, %(registration)s, %(icao_type_designator)s, %(type_description)s,
            %(wake_turbulence_category)s, %(aircraft_description)s, %(operator)s, %(year_built)s,
            %(is_military)s, %(is_interesting)s, %(is_pia)s, %(is_ladd)s,
            %(country_code)s, %(database_source)s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
        """
        
        logger.info("Attempting to insert test aircraft...")
        cursor.execute(insert_sql, test_aircraft)
        conn.commit()
        logger.info("✅ Test insert successful")
        
        # Verify the insert
        cursor.execute("SELECT * FROM aircraft WHERE hex = %s", (test_aircraft['hex'],))
        result = cursor.fetchone()
        if result:
            logger.info(f"✅ Test aircraft found in database: {result[0]} - {result[2]}")
        else:
            logger.error("❌ Test aircraft not found after insert")
        
        # Clean up test data
        cursor.execute("DELETE FROM aircraft WHERE hex = %s", (test_aircraft['hex'],))
        conn.commit()
        logger.info("✅ Test data cleaned up")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Test insert failed: {e}")
        try:
            conn.rollback()
            cursor.close()
            conn.close()
        except:
            pass
        return False

def test_batch_insert():
    """Test inserting multiple aircraft records in a batch."""
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        # Test data - multiple aircraft
        test_aircraft = [
            {
                'hex': 'test002',
                'registration': 'N-TEST2',
                'icao_type_designator': 'B738',
                'type_description': 'L2J',
                'wake_turbulence_category': 'M',
                'aircraft_description': 'Test Boeing 737',
                'operator': 'Test Airways',
                'year_built': 2019,
                'is_military': False,
                'is_interesting': False,
                'is_pia': False,
                'is_ladd': False,
                'country_code': 'US',
                'database_source': 'test'
            },
            {
                'hex': 'test003',
                'registration': 'N-TEST3',
                'icao_type_designator': 'C172',
                'type_description': 'L1P',
                'wake_turbulence_category': 'L',
                'aircraft_description': 'Test Cessna 172',
                'operator': 'Test Flight School',
                'year_built': 2018,
                'is_military': False,
                'is_interesting': True,
                'is_pia': False,
                'is_ladd': False,
                'country_code': 'US',
                'database_source': 'test'
            }
        ]
        
        # Batch insert query
        insert_sql = """
        INSERT INTO aircraft (
            hex, registration, icao_type_designator, type_description, 
            wake_turbulence_category, aircraft_description, operator, year_built,
            is_military, is_interesting, is_pia, is_ladd, 
            country_code, database_source, last_updated, created_at
        ) VALUES (
            %(hex)s, %(registration)s, %(icao_type_designator)s, %(type_description)s,
            %(wake_turbulence_category)s, %(aircraft_description)s, %(operator)s, %(year_built)s,
            %(is_military)s, %(is_interesting)s, %(is_pia)s, %(is_ladd)s,
            %(country_code)s, %(database_source)s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
        """
        
        logger.info(f"Attempting to batch insert {len(test_aircraft)} aircraft...")
        cursor.executemany(insert_sql, test_aircraft)
        conn.commit()
        logger.info("✅ Batch insert successful")
        
        # Verify the inserts
        cursor.execute("SELECT COUNT(*) FROM aircraft WHERE database_source = 'test'")
        count = cursor.fetchone()[0]
        logger.info(f"✅ Found {count} test aircraft in database")
        
        # Clean up test data
        cursor.execute("DELETE FROM aircraft WHERE database_source = 'test'")
        deleted_count = cursor.rowcount
        conn.commit()
        logger.info(f"✅ Cleaned up {deleted_count} test records")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Batch insert failed: {e}")
        try:
            conn.rollback()
            cursor.close()
            conn.close()
        except:
            pass
        return False

def test_upsert():
    """Test UPSERT functionality (INSERT ... ON CONFLICT)."""
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        test_hex = 'test004'
        
        # First insert
        insert_sql = """
        INSERT INTO aircraft (
            hex, registration, icao_type_designator, database_source, 
            last_updated, created_at
        ) VALUES (
            %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
        """
        
        logger.info("Testing initial insert...")
        cursor.execute(insert_sql, (test_hex, 'N-TEST4', 'A320', 'test'))
        conn.commit()
        logger.info("✅ Initial insert successful")
        
        # Test UPSERT (update existing record)
        upsert_sql = """
        INSERT INTO aircraft (
            hex, registration, icao_type_designator, operator, database_source,
            last_updated, created_at
        ) VALUES (
            %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
        ON CONFLICT (hex) DO UPDATE SET
            registration = COALESCE(EXCLUDED.registration, aircraft.registration),
            icao_type_designator = COALESCE(EXCLUDED.icao_type_designator, aircraft.icao_type_designator),
            operator = COALESCE(EXCLUDED.operator, aircraft.operator),
            database_source = EXCLUDED.database_source,
            last_updated = EXCLUDED.last_updated
        """
        
        logger.info("Testing UPSERT (update existing)...")
        cursor.execute(upsert_sql, (test_hex, 'N-TEST4-UPD', 'B738', 'Updated Airlines', 'test'))
        conn.commit()
        logger.info("✅ UPSERT successful")
        
        # Verify the update
        cursor.execute("SELECT hex, registration, icao_type_designator, operator FROM aircraft WHERE hex = %s", (test_hex,))
        result = cursor.fetchone()
        if result:
            logger.info(f"✅ Updated aircraft: {result[0]} - {result[1]} ({result[2]}) - {result[3]}")
        
        # Clean up
        cursor.execute("DELETE FROM aircraft WHERE hex = %s", (test_hex,))
        conn.commit()
        logger.info("✅ Test data cleaned up")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ UPSERT test failed: {e}")
        try:
            conn.rollback()
            cursor.close()
            conn.close()
        except:
            pass
        return False

def main():
    """Run all database tests."""
    logger.info("🧪 Starting database tests...")
    
    tests = [
        ("Connection Test", test_connection),
        ("Schema Test", test_schema),
        ("Simple Insert Test", test_simple_insert),
        ("Batch Insert Test", test_batch_insert),
        ("UPSERT Test", test_upsert)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status}: {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        logger.info("🎉 All tests passed! Database is ready for population.")
    else:
        logger.info("⚠️  Some tests failed. Please fix issues before running population script.")

if __name__ == "__main__":
    main()
