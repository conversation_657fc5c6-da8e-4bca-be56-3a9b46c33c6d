#!/usr/bin/env python3
"""
Performance comparison test for different database population strategies.
Tests with a small subset to measure performance improvements.
"""

import json
import io
import time
from pathlib import Path
import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection string for TimescaleDB
DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?sslmode=require"

def create_test_data(count: int = 1000):
    """Create test aircraft data that matches schema constraints."""
    test_records = []
    for i in range(count):
        test_records.append({
            'hex': f'test{i:06d}',
            'registration': f'N-T{i:04d}' if i % 3 == 0 else None,  # Some nulls, shorter
            'icao_type_designator': 'B738' if i % 2 == 0 else 'A320',  # 4 chars max
            'type_description': 'B737-800' if i % 2 == 0 else 'A320',  # 10 chars max
            'wake_turbulence_category': 'M',
            'aircraft_description': f'Test Aircraft {i}',
            'is_military': i % 10 == 0,
            'is_interesting': i % 20 == 0,
            'country_code': 'US',  # 2 chars max
            'database_source': 'perf_test'  # 50 chars max
        })
    return test_records

def test_individual_inserts(cursor, records, sample_size=50):
    """Test individual INSERT statements."""
    logger.info(f"Testing individual inserts with {sample_size} records...")

    start_time = time.time()

    for i, record in enumerate(records[:sample_size]):
        test_record = record.copy()
        test_record['hex'] = f'indiv{i:06d}'  # Unique hex
        test_record['registration'] = f'I-{i:05d}' if test_record['registration'] else None  # Unique registration
        cursor.execute("""
            INSERT INTO aircraft (
                hex, registration, icao_type_designator, type_description,
                wake_turbulence_category, aircraft_description,
                is_military, is_interesting, country_code, database_source,
                last_updated, created_at
            ) VALUES (
                %(hex)s, %(registration)s, %(icao_type_designator)s, %(type_description)s,
                %(wake_turbulence_category)s, %(aircraft_description)s,
                %(is_military)s, %(is_interesting)s, %(country_code)s, %(database_source)s,
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            )
        """, test_record)

    end_time = time.time()
    duration = end_time - start_time

    logger.info(f"Individual inserts: {duration:.2f}s for {sample_size} records")
    logger.info(f"Rate: {sample_size / duration:.1f} records/second")

    return duration, sample_size

def test_executemany(cursor, records, sample_size=200):
    """Test executemany batch insert."""
    logger.info(f"Testing executemany with {sample_size} records...")

    # Prepare records
    batch_records = []
    for i, record in enumerate(records[:sample_size]):
        test_record = record.copy()
        test_record['hex'] = f'batch{i:06d}'  # Unique hex
        test_record['registration'] = f'B-{i:05d}' if test_record['registration'] else None  # Unique registration
        batch_records.append(test_record)

    start_time = time.time()

    cursor.executemany("""
        INSERT INTO aircraft (
            hex, registration, icao_type_designator, type_description,
            wake_turbulence_category, aircraft_description,
            is_military, is_interesting, country_code, database_source,
            last_updated, created_at
        ) VALUES (
            %(hex)s, %(registration)s, %(icao_type_designator)s, %(type_description)s,
            %(wake_turbulence_category)s, %(aircraft_description)s,
            %(is_military)s, %(is_interesting)s, %(country_code)s, %(database_source)s,
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
    """, batch_records)

    end_time = time.time()
    duration = end_time - start_time

    logger.info(f"Executemany: {duration:.2f}s for {sample_size} records")
    logger.info(f"Rate: {sample_size / duration:.1f} records/second")

    return duration, sample_size

def test_copy_from_basic(cursor, records, sample_size=500):
    """Test basic COPY FROM."""
    logger.info(f"Testing basic COPY FROM with {sample_size} records...")
    
    start_time = time.time()
    
    # Prepare data for COPY
    copy_data = io.StringIO()
    for i, record in enumerate(records[:sample_size]):
        hex_code = f'copy{i:06d}'
        registration = f'C-{i:05d}' if record['registration'] else '\\N'
        values = [
            hex_code,
            registration,
            record['icao_type_designator'] or '\\N',
            record['type_description'] or '\\N',
            record['wake_turbulence_category'] or '\\N',
            record['aircraft_description'] or '\\N',
            '\\N',  # operator
            '\\N',  # year_built
            str(record['is_military']).lower(),
            str(record['is_interesting']).lower(),
            'false',  # is_pia
            'false',  # is_ladd
            record['country_code'] or '\\N',
            record['database_source'],
            'NOW()',  # last_updated
            'NOW()'   # created_at
        ]
        copy_data.write('\t'.join(values) + '\n')
    
    copy_data.seek(0)
    
    cursor.copy_from(
        copy_data,
        'aircraft',
        columns=(
            'hex', 'registration', 'icao_type_designator', 'type_description',
            'wake_turbulence_category', 'aircraft_description', 'operator', 'year_built',
            'is_military', 'is_interesting', 'is_pia', 'is_ladd',
            'country_code', 'database_source', 'last_updated', 'created_at'
        )
    )
    
    end_time = time.time()
    duration = end_time - start_time
    
    logger.info(f"Basic COPY FROM: {duration:.2f}s for {sample_size} records")
    logger.info(f"Rate: {sample_size / duration:.1f} records/second")
    
    return duration, sample_size

def test_copy_from_optimized(cursor, records, sample_size=1000):
    """Test optimized COPY FROM with database optimizations."""
    logger.info(f"Testing OPTIMIZED COPY FROM with {sample_size} records...")
    
    # Apply optimizations
    cursor.execute("SET synchronous_commit = off")
    cursor.execute("SET fsync = off")
    cursor.execute("SET work_mem = '256MB'")
    
    start_time = time.time()
    
    # Prepare data for COPY
    copy_data = io.StringIO()
    for i, record in enumerate(records[:sample_size]):
        hex_code = f'optim{i:06d}'
        registration = f'O-{i:05d}' if record['registration'] else '\\N'
        values = [
            hex_code,
            registration,
            record['icao_type_designator'] or '\\N',
            record['type_description'] or '\\N',
            record['wake_turbulence_category'] or '\\N',
            record['aircraft_description'] or '\\N',
            '\\N',  # operator
            '\\N',  # year_built
            str(record['is_military']).lower(),
            str(record['is_interesting']).lower(),
            'false',  # is_pia
            'false',  # is_ladd
            record['country_code'] or '\\N',
            record['database_source'],
            'NOW()',  # last_updated
            'NOW()'   # created_at
        ]
        copy_data.write('\t'.join(values) + '\n')
    
    copy_data.seek(0)
    
    cursor.copy_from(
        copy_data,
        'aircraft',
        columns=(
            'hex', 'registration', 'icao_type_designator', 'type_description',
            'wake_turbulence_category', 'aircraft_description', 'operator', 'year_built',
            'is_military', 'is_interesting', 'is_pia', 'is_ladd',
            'country_code', 'database_source', 'last_updated', 'created_at'
        )
    )
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Restore settings
    cursor.execute("SET synchronous_commit = on")
    cursor.execute("SET fsync = on")
    
    logger.info(f"Optimized COPY FROM: {duration:.2f}s for {sample_size} records")
    logger.info(f"Rate: {sample_size / duration:.1f} records/second")
    
    return duration, sample_size

def cleanup_test_data(cursor):
    """Clean up all test data."""
    cursor.execute("DELETE FROM aircraft WHERE database_source = 'perf_test'")
    deleted = cursor.rowcount
    logger.info(f"Cleaned up {deleted} test records")

def main():
    """Run performance comparison tests."""
    logger.info("🧪 Starting performance comparison tests...")
    
    # Create test data
    test_records = create_test_data(1000)
    logger.info(f"Created {len(test_records)} test records")
    
    # Connect to database
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        logger.info("Connected to database")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        return
    
    results = []
    
    try:
        # Test 1: Individual inserts
        duration, count = test_individual_inserts(cursor, test_records)
        results.append(("Individual INSERTs", duration, count))
        conn.commit()
        
        # Test 2: executemany
        duration, count = test_executemany(cursor, test_records)
        results.append(("executemany", duration, count))
        conn.commit()
        
        # Test 3: Basic COPY FROM
        duration, count = test_copy_from_basic(cursor, test_records)
        results.append(("Basic COPY FROM", duration, count))
        conn.commit()
        
        # Test 4: Optimized COPY FROM
        duration, count = test_copy_from_optimized(cursor, test_records)
        results.append(("Optimized COPY FROM", duration, count))
        conn.commit()
        
        # Clean up
        cleanup_test_data(cursor)
        conn.commit()
        
        # Results summary
        logger.info("\n" + "="*80)
        logger.info("PERFORMANCE COMPARISON RESULTS")
        logger.info("="*80)
        
        for method, duration, count in results:
            rate = count / duration
            logger.info(f"{method:20s}: {rate:8.1f} records/sec ({duration:.2f}s for {count} records)")
        
        # Calculate improvements
        if len(results) >= 2:
            baseline_rate = results[0][2] / results[0][1]  # Individual inserts
            best_rate = max(r[2] / r[1] for r in results)
            improvement = best_rate / baseline_rate
            logger.info(f"\nBest improvement: {improvement:.1f}x faster than individual inserts")
        
        logger.info("\n🎯 RECOMMENDATIONS:")
        logger.info("1. Use COPY FROM for bulk inserts (10-100x faster)")
        logger.info("2. Apply database optimizations during bulk operations")
        logger.info("3. Use large batch sizes (10k-50k records)")
        logger.info("4. Minimize commits during bulk operations")
        logger.info("5. Drop indexes/constraints during bulk insert, recreate after")
        
    except Exception as e:
        logger.error(f"Error during performance tests: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    main()
