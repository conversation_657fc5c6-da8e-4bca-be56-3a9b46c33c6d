#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to populate the aircraft database with TAR1090 data.
This script reads the exported TAR1090 database files and populates your PostgreSQL database.
"""

import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional
import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection string for TimescaleDB
DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?sslmode=require"

# Path to your exported TAR1090 database files
DATABASE_PATH = Path(__file__).parent / 'database'

def parse_tar1090_flags(flags_string: str) -> tuple[bool, bool]:
    """Parse TAR1090 2-character flag string into boolean fields."""
    if not flags_string or len(flags_string) != 2:
        return False, False
    
    is_military = flags_string[0] == '1'
    is_interesting = flags_string[1] == '1'
    return is_military, is_interesting

def get_wake_turbulence_category(type_description: str) -> Optional[str]:
    """Derive wake turbulence category from type description if not provided."""
    if not type_description:
        return None
    
    # Basic mapping based on aircraft type description
    if type_description.startswith('H'):  # Helicopter
        return 'L'
    elif 'L1' in type_description:  # Single engine
        return 'L'
    elif 'L2' in type_description:  # Twin engine
        return 'M'
    elif 'L3' in type_description or 'L4' in type_description:  # 3-4 engines
        return 'H'
    
    return None

def get_country_from_hex(hex_code: str) -> Optional[str]:
    """Get country code from ICAO hex code based on known ranges."""
    if not hex_code:
        return None

    # Basic country mapping based on ICAO hex ranges
    hex_upper = hex_code.upper()
    first_char = hex_upper[0]

    # More comprehensive country mapping based on ICAO hex ranges
    country_mapping = {
        '0': 'US',  # United States (some ranges)
        '1': 'US',  # United States
        '2': 'US',  # United States
        '3': 'DE',  # Germany (3D0000-3FFFFF)
        '4': 'US',  # United States
        '5': 'AF',  # Various African countries
        '6': 'AF',  # Various African countries
        '7': 'AF',  # Various African countries
        '8': 'AF',  # Various African countries
        '9': 'AF',  # Various African countries
        'A': 'US',  # United States
        'B': 'US',  # United States
        'C': 'CA',  # Canada
        'D': 'DE',  # Germany
        'E': 'GB',  # United Kingdom
        'F': 'FR',  # France
    }

    # Special cases for more specific ranges
    if hex_upper.startswith('3D'):
        return 'DE'  # Germany
    elif hex_upper.startswith('C0'):
        return 'CA'  # Canada
    elif hex_upper.startswith('40'):
        return 'GB'  # United Kingdom

    return country_mapping.get(first_char)

def load_type_cache(database_path: Path) -> Dict[str, tuple]:
    """Load the ICAO aircraft types cache."""
    type_cache_file = database_path / 'icao_aircraft_types.json'

    if not type_cache_file.exists():
        logger.warning(f"Type cache file not found: {type_cache_file}")
        return {}

    try:
        with open(type_cache_file, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)

        # Convert the raw data to the expected format
        # The file contains: {"A320": {"desc": "L2J", "wtc": "M"}, ...}
        # We need: {"A320": (None, "L2J", "M"), ...}
        type_cache = {}
        for icao_type, type_info in raw_data.items():
            if isinstance(type_info, dict):
                desc = type_info.get('desc', '')
                wtc = type_info.get('wtc', '')
                # Format: (long_name, type_description, wake_category)
                type_cache[icao_type.upper()] = (None, desc, wtc)

        logger.info(f"Loaded {len(type_cache)} aircraft types from cache")
        return type_cache

    except Exception as e:
        logger.error(f"Error loading type cache: {e}")
        return {}

def process_aircraft_file(file_path: Path, type_cache: Dict[str, tuple]) -> list[Dict[str, Any]]:
    """Process a single aircraft database file and return aircraft records."""
    aircraft_records = []

    try:
        # Check if file is empty
        if file_path.stat().st_size == 0:
            logger.warning(f"File {file_path.name} is empty, skipping")
            return aircraft_records

        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Check if data is empty or not a dict
        if not data or not isinstance(data, dict):
            logger.warning(f"File {file_path.name} contains no valid aircraft data")
            return aircraft_records

        # Extract the prefix from the filename (remove .json extension)
        file_prefix = file_path.stem.upper()

        for hex_suffix, aircraft_data in data.items():
            if hex_suffix == 'children':  # Skip the children array
                continue

            try:
                # Construct the full ICAO hex code by combining file prefix with hex suffix
                full_hex_code = (file_prefix + hex_suffix).lower()

                # Validate aircraft_data structure
                if not isinstance(aircraft_data, dict):
                    logger.warning(f"Invalid aircraft data for hex {full_hex_code} in {file_path.name}")
                    continue

                # Extract basic fields with better error handling
                registration = aircraft_data.get('r', '').strip() if aircraft_data.get('r') else ''
                icao_type = aircraft_data.get('t', '').strip() if aircraft_data.get('t') else ''
                flags = aircraft_data.get('f', '00') if aircraft_data.get('f') else '00'
                description = aircraft_data.get('desc', '').strip() if aircraft_data.get('desc') else ''

                # Validate hex code
                if not hex_suffix or len(hex_suffix) < 1:
                    logger.warning(f"Invalid hex suffix '{hex_suffix}' in {file_path.name}")
                    continue

                # Parse flags
                is_military, is_interesting = parse_tar1090_flags(flags)

                # Get type information from cache
                type_info = type_cache.get(icao_type.upper(), [None, None, None]) if icao_type else [None, None, None]
                type_long = type_info[0] if len(type_info) > 0 and type_info[0] else None
                type_description = type_info[1] if len(type_info) > 1 and type_info[1] else None
                wake_category = type_info[2] if len(type_info) > 2 and type_info[2] else None

                # Validate wake category - only allow valid enum values
                valid_wake_categories = {'L', 'M', 'H', 'J'}
                if wake_category not in valid_wake_categories:
                    wake_category = None

                # If no wake category from cache, try to derive it
                if not wake_category and type_description:
                    derived_wake = get_wake_turbulence_category(type_description)
                    if derived_wake in valid_wake_categories:
                        wake_category = derived_wake
                    else:
                        wake_category = None

                # Get country from hex
                country_code = get_country_from_hex(full_hex_code)

                aircraft_record = {
                    'hex': full_hex_code,
                    'registration': registration if registration and registration != 'DEFAULT' else None,
                    'icao_type_designator': icao_type if icao_type else None,
                    'type_description': type_description,
                    'wake_turbulence_category': wake_category,
                    'aircraft_description': description if description else type_long,
                    'is_military': is_military,
                    'is_interesting': is_interesting,
                    'country_code': country_code,
                    'database_source': 'tar1090'
                }

                aircraft_records.append(aircraft_record)

            except Exception as e:
                logger.warning(f"Error processing aircraft {full_hex_code} in {file_path.name}: {e}")
                continue

    except json.JSONDecodeError as e:
        logger.error(f"JSON decode error in file {file_path.name}: {e}")
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
    except Exception as e:
        logger.error(f"Unexpected error processing file {file_path.name}: {e}")

    return aircraft_records

def insert_single_aircraft(cursor, conn, aircraft_record: Dict[str, Any]) -> bool:
    """Insert a single aircraft record into the database with proper transaction handling."""

    # First, try to insert with registration
    insert_sql = """
    INSERT INTO aircraft (
        hex, registration, icao_type_designator, type_description,
        wake_turbulence_category, aircraft_description, is_military,
        is_interesting, country_code, database_source, last_updated, created_at
    ) VALUES (
        %(hex)s, %(registration)s, %(icao_type_designator)s, %(type_description)s,
        %(wake_turbulence_category)s, %(aircraft_description)s, %(is_military)s,
        %(is_interesting)s, %(country_code)s, %(database_source)s,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    ON CONFLICT (hex) DO NOTHING
    """

    try:
        cursor.execute(insert_sql, aircraft_record)
        return cursor.rowcount > 0
    except Exception as e:
        # If registration conflict, try without registration
        if "aircraft_registration_key" in str(e):
            conn.rollback()
            try:
                # Create a copy without registration to avoid conflicts
                record_without_reg = aircraft_record.copy()
                record_without_reg['registration'] = None
                cursor.execute(insert_sql, record_without_reg)
                return cursor.rowcount > 0
            except Exception as e2:
                conn.rollback()
                logger.warning(f"Failed to insert aircraft {aircraft_record.get('hex', 'unknown')} even without registration: {e2}")
                return False
        else:
            conn.rollback()
            logger.warning(f"Failed to insert aircraft {aircraft_record.get('hex', 'unknown')}: {e}")
            return False

def main():
    """Main function to populate the aircraft database."""
    if not DATABASE_PATH.exists():
        logger.error(f"Database path does not exist: {DATABASE_PATH}")
        sys.exit(1)
    
    # Load type cache
    logger.info("Loading ICAO aircraft types cache...")
    type_cache = load_type_cache(DATABASE_PATH)
    logger.info(f"Loaded {len(type_cache)} aircraft types")
    
    # Connect to database
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        logger.info("Connected to database")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        sys.exit(1)
    
    try:
        total_processed = 0
        total_inserted = 0
        commit_frequency = 10  # Commit every 10 inserts for TimescaleDB
        insert_count = 0
        
        # Process all JSON files in the database directory
        json_files = list(DATABASE_PATH.glob('*.json'))
        json_files = [f for f in json_files if f.name != 'icao_aircraft_types.json']

        logger.info(f"Found {len(json_files)} database files to process")

        # Log some sample file names for debugging
        if json_files:
            sample_files = json_files[:5]
            logger.info(f"Sample files: {[f.name for f in sample_files]}")
        
        for i, file_path in enumerate(json_files):
            logger.info(f"Processing file {i+1}/{len(json_files)}: {file_path.name}")

            aircraft_records = process_aircraft_file(file_path, type_cache)

            if aircraft_records:
                logger.info(f"Found {len(aircraft_records)} aircraft in {file_path.name}")
                total_processed += len(aircraft_records)

                # Insert aircraft one by one for TimescaleDB compatibility
                file_inserted = 0
                for aircraft_record in aircraft_records:
                    if insert_single_aircraft(cursor, conn, aircraft_record):
                        file_inserted += 1
                        total_inserted += 1
                        insert_count += 1

                        # Commit every commit_frequency inserts
                        if insert_count % commit_frequency == 0:
                            conn.commit()
                            logger.info(f"✅ Committed {commit_frequency} inserts. Total: {total_inserted}")

                logger.info(f"✅ Inserted {file_inserted}/{len(aircraft_records)} aircraft from {file_path.name}")

                # Progress update every 10 files
                if (i + 1) % 10 == 0:
                    logger.info(f"📊 Progress: {i+1}/{len(json_files)} files, {total_inserted} aircraft inserted")
            else:
                logger.info(f"No aircraft found in {file_path.name}")

        # Final commit for any remaining inserts
        conn.commit()
        logger.info(f"✅ Final commit completed")
        
        logger.info(f"Processing complete! Processed {total_processed} aircraft, inserted/updated {total_inserted}")
        
    except Exception as e:
        logger.error(f"Error during processing: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    main()
