-- Migration: Add missing columns to airports table
-- Date: 2025-07-11
-- Description: Add timezone and image_url columns to match the current model

-- Add timezone column if it doesn't exist (from model but missing in schema)
ALTER TABLE airports ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) NULL;

-- Add image_url column for airport pictures
ALTER TABLE airports ADD COLUMN IF NOT EXISTS image_url VARCHAR(500) NULL;

-- Add comments to document the columns
COMMENT ON COLUMN airports.timezone IS 'Airport timezone (IANA timezone identifier)';
COMMENT ON COLUMN airports.image_url IS 'URL to airport image stored in cloud storage (Backblaze B2)';

-- Verify the columns were added
SELECT column_name, data_type, is_nullable, character_maximum_length 
FROM information_schema.columns 
WHERE table_name = 'airports' AND column_name IN ('timezone', 'image_url')
ORDER BY column_name;
