#!/bin/bash

# Setup script for aircraft database population
# This script prepares the environment and copies database files

set -e

echo "🚀 Setting up aircraft database population environment..."

# Create database directory if it doesn't exist
if [ ! -d "database" ]; then
    echo "📁 Creating database directory..."
    mkdir -p database
fi


# Install Python dependencies
echo "🐍 Installing Python dependencies..."
if command -v pip &> /dev/null; then
    pip install -r requirements.txt
    echo "   Dependencies installed successfully"
else
    echo "❌ pip not found. Please install Python dependencies manually:"
    echo "   pip install psycopg2-binary"
    exit 1
fi

# Verify database files
echo "🔍 Verifying database files..."
if [ -f "database/icao_aircraft_types.json" ]; then
    echo "   ✅ ICAO aircraft types file found"
else
    echo "   ❌ ICAO aircraft types file missing"
    exit 1
fi

# Count aircraft data files
AIRCRAFT_FILES=$(find database/ -name "*.json" ! -name "icao_aircraft_types.json" | wc -l)
echo "   ✅ Found $AIRCRAFT_FILES aircraft data files"

echo ""
echo "🎉 Setup complete! You can now run the population script:"
echo "   python populate_aircraft_database.py"
echo ""
echo "📊 Expected processing:"
echo "   - Aircraft types: ~8,500 entries"
echo "   - Aircraft data: ~200,000+ entries"
echo "   - Processing time: 5-10 minutes"
echo ""
