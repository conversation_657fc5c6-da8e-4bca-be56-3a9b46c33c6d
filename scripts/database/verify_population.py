#!/usr/bin/env python3
"""
Verification script for aircraft database population.
This script checks the results of the population process and provides statistics.
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection string for TimescaleDB
DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?sslmode=require"

def run_query(cursor, query, description):
    """Run a query and display results."""
    try:
        cursor.execute(query)
        results = cursor.fetchall()
        
        print(f"\n📊 {description}")
        print("=" * 50)
        
        if not results:
            print("No results found")
            return
        
        # Print column headers
        if results:
            headers = list(results[0].keys())
            header_line = " | ".join(f"{h:>15}" for h in headers)
            print(header_line)
            print("-" * len(header_line))
            
            # Print rows
            for row in results:
                row_line = " | ".join(f"{str(row[h]):>15}" for h in headers)
                print(row_line)
                
    except Exception as e:
        logger.error(f"Error running query: {e}")

def main():
    """Main verification function."""
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        logger.info("Connected to database")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        return
    
    try:
        print("🔍 Aircraft Database Population Verification")
        print("=" * 60)
        
        # Total aircraft count
        run_query(cursor, 
                 "SELECT COUNT(*) as total_aircraft FROM aircraft",
                 "Total Aircraft Count")
        
        # Count by data source
        run_query(cursor,
                 """SELECT database_source, COUNT(*) as count 
                    FROM aircraft 
                    GROUP BY database_source 
                    ORDER BY count DESC""",
                 "Aircraft by Data Source")
        
        # Data completeness
        run_query(cursor,
                 """SELECT 
                        COUNT(*) as total,
                        COUNT(registration) as with_registration,
                        COUNT(icao_type_designator) as with_type,
                        COUNT(operator) as with_operator,
                        COUNT(country_code) as with_country,
                        COUNT(wake_turbulence_category) as with_wake_cat
                    FROM aircraft""",
                 "Data Completeness")
        
        # Top countries
        run_query(cursor,
                 """SELECT country_code, COUNT(*) as count
                    FROM aircraft 
                    WHERE country_code IS NOT NULL
                    GROUP BY country_code 
                    ORDER BY count DESC 
                    LIMIT 10""",
                 "Top 10 Countries by Aircraft Count")
        
        # Military vs civilian
        run_query(cursor,
                 """SELECT 
                        CASE WHEN is_military THEN 'Military' ELSE 'Civilian' END as type,
                        COUNT(*) as count
                    FROM aircraft
                    GROUP BY is_military
                    ORDER BY count DESC""",
                 "Military vs Civilian Aircraft")
        
        # Wake turbulence categories
        run_query(cursor,
                 """SELECT 
                        wake_turbulence_category,
                        COUNT(*) as count
                    FROM aircraft
                    WHERE wake_turbulence_category IS NOT NULL
                    GROUP BY wake_turbulence_category
                    ORDER BY count DESC""",
                 "Wake Turbulence Categories")
        
        # Top aircraft types
        run_query(cursor,
                 """SELECT 
                        icao_type_designator,
                        COUNT(*) as count
                    FROM aircraft
                    WHERE icao_type_designator IS NOT NULL
                    GROUP BY icao_type_designator
                    ORDER BY count DESC
                    LIMIT 15""",
                 "Top 15 Aircraft Types")
        
        # Recent updates
        run_query(cursor,
                 """SELECT 
                        DATE(last_updated) as update_date,
                        COUNT(*) as count
                    FROM aircraft
                    WHERE last_updated IS NOT NULL
                    GROUP BY DATE(last_updated)
                    ORDER BY update_date DESC
                    LIMIT 5""",
                 "Recent Update Activity")
        
        # Sample enhanced records
        run_query(cursor,
                 """SELECT 
                        hex,
                        registration,
                        icao_type_designator,
                        operator,
                        country_code,
                        is_military
                    FROM aircraft
                    WHERE icao_type_designator IS NOT NULL 
                    AND operator IS NOT NULL
                    ORDER BY RANDOM()
                    LIMIT 5""",
                 "Sample Enhanced Aircraft Records")
        
        print("\n✅ Verification complete!")
        print("\n💡 Tips:")
        print("   - High data completeness indicates successful population")
        print("   - Check that country codes and types look reasonable")
        print("   - Military aircraft should be a small percentage of total")
        print("   - Recent update dates should match when you ran the script")
        
    except Exception as e:
        logger.error(f"Error during verification: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    main()
