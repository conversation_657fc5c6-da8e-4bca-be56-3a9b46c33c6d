#!/usr/bin/env python3
"""
Script to optimize database for bulk insert by dropping constraints and indexes.
This prepares the database for maximum insert performance.
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection string for TimescaleDB
DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?sslmode=require"

def check_current_constraints_and_indexes(cursor):
    """Check current constraints and indexes on aircraft table."""
    logger.info("🔍 Checking current constraints and indexes...")
    
    # Check constraints
    cursor.execute("""
        SELECT 
            conname,
            contype,
            pg_get_constraintdef(oid) as definition
        FROM pg_constraint 
        WHERE conrelid = 'aircraft'::regclass
        ORDER BY conname
    """)
    
    constraints = cursor.fetchall()
    logger.info("Current constraints:")
    for con in constraints:
        constraint_type = {
            'p': 'PRIMARY KEY',
            'u': 'UNIQUE',
            'f': 'FOREIGN KEY',
            'c': 'CHECK'
        }.get(con['contype'], con['contype'])
        logger.info(f"  - {con['conname']} ({constraint_type}): {con['definition']}")
    
    # Check indexes
    cursor.execute("""
        SELECT 
            indexname,
            indexdef
        FROM pg_indexes 
        WHERE tablename = 'aircraft'
        ORDER BY indexname
    """)
    
    indexes = cursor.fetchall()
    logger.info("Current indexes:")
    for idx in indexes:
        logger.info(f"  - {idx['indexname']}: {idx['indexdef']}")
    
    # Check triggers
    cursor.execute("""
        SELECT 
            trigger_name,
            event_manipulation,
            action_timing,
            action_statement
        FROM information_schema.triggers 
        WHERE event_object_table = 'aircraft'
        ORDER BY trigger_name
    """)
    
    triggers = cursor.fetchall()
    logger.info("Current triggers:")
    for trigger in triggers:
        logger.info(f"  - {trigger['trigger_name']}: {trigger['action_timing']} {trigger['event_manipulation']}")

def drop_constraints_and_indexes(cursor):
    """Drop unnecessary constraints and indexes for bulk insert optimization."""
    logger.info("🗑️  Dropping constraints and indexes for bulk insert optimization...")
    
    operations = []
    
    try:
        # 1. Drop the registration unique constraint
        logger.info("Dropping registration unique constraint...")
        cursor.execute("ALTER TABLE aircraft DROP CONSTRAINT IF EXISTS aircraft_registration_key")
        operations.append("Dropped registration unique constraint")
        
        # 2. Drop redundant index (ix_aircraft_hex is redundant with primary key)
        logger.info("Dropping redundant hex index...")
        cursor.execute("DROP INDEX IF EXISTS ix_aircraft_hex")
        operations.append("Dropped redundant ix_aircraft_hex index")
        
        # 3. Drop the update trigger to avoid overhead
        logger.info("Dropping update trigger...")
        cursor.execute("DROP TRIGGER IF EXISTS trg_aircraft_update_timestamp ON aircraft")
        operations.append("Dropped update trigger")
        
        # 4. Drop any other non-essential indexes that might exist
        logger.info("Dropping other non-essential indexes...")
        non_essential_indexes = [
            'idx_aircraft_icao_type',
            'idx_aircraft_operator', 
            'idx_aircraft_military',
            'idx_aircraft_country',
            'idx_aircraft_wake_category',
            'idx_aircraft_last_updated',
            'idx_aircraft_database_source'
        ]
        
        for index_name in non_essential_indexes:
            cursor.execute(f"DROP INDEX IF EXISTS {index_name}")
            operations.append(f"Dropped index {index_name} (if existed)")
        
        logger.info("✅ Successfully dropped constraints and indexes")
        for op in operations:
            logger.info(f"  ✓ {op}")
            
    except Exception as e:
        logger.error(f"Error dropping constraints/indexes: {e}")
        raise

def set_bulk_insert_optimizations(cursor):
    """Set database parameters for optimal bulk insert performance."""
    logger.info("⚙️  Setting bulk insert optimization parameters...")
    
    try:
        # Memory settings
        cursor.execute("SET maintenance_work_mem = '2GB'")
        cursor.execute("SET work_mem = '1GB'")
        
        # WAL and checkpoint settings
        cursor.execute("SET wal_buffers = '64MB'")
        cursor.execute("SET checkpoint_completion_target = 0.9")
        
        # Disable synchronous operations for speed (UNSAFE but fast)
        cursor.execute("SET synchronous_commit = off")
        cursor.execute("SET fsync = off")
        cursor.execute("SET full_page_writes = off")
        
        # Commit batching
        cursor.execute("SET commit_delay = 100000")  # 100ms delay
        cursor.execute("SET commit_siblings = 10")
        
        logger.info("✅ Database optimized for bulk insert")
        logger.info("⚠️  WARNING: Database is now in UNSAFE mode for maximum speed!")
        logger.info("⚠️  Make sure to run restore script after bulk insert!")
        
    except Exception as e:
        logger.error(f"Error setting optimizations: {e}")
        raise

def main():
    """Main function to optimize database for bulk insert."""
    logger.info("🚀 Starting database optimization for bulk insert...")
    
    # Connect to database
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        logger.info("Connected to database")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        return
    
    try:
        # Show current state
        check_current_constraints_and_indexes(cursor)
        
        # Ask for confirmation
        print("\n" + "="*70)
        print("⚠️  WARNING: This will drop constraints, indexes, and triggers!")
        print("⚠️  This makes the database UNSAFE but much faster for bulk inserts.")
        print("⚠️  You MUST run the restore script after bulk insert is complete.")
        print("="*70)
        
        response = input("\nDo you want to proceed? (yes/no): ").strip().lower()
        if response not in ['yes', 'y']:
            logger.info("Operation cancelled by user")
            return
        
        # Perform optimizations
        drop_constraints_and_indexes(cursor)
        set_bulk_insert_optimizations(cursor)
        
        # Commit changes
        conn.commit()
        
        logger.info("\n" + "="*70)
        logger.info("✅ DATABASE OPTIMIZATION COMPLETE")
        logger.info("="*70)
        logger.info("Your database is now optimized for bulk insert operations.")
        logger.info("Performance should be 10-100x faster for bulk inserts.")
        logger.info("")
        logger.info("🎯 NEXT STEPS:")
        logger.info("1. Run your bulk insert script (populate_aircraft_ultra_optimized.py)")
        logger.info("2. After bulk insert, run restore_database_safety.py")
        logger.info("3. Verify data integrity after restoration")
        logger.info("")
        logger.info("⚠️  IMPORTANT: Database is in UNSAFE mode until restored!")
        
    except Exception as e:
        logger.error(f"Error during optimization: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    main()
