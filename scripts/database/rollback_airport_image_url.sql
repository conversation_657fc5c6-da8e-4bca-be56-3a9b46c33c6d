-- Rollback: Remove image_url column from airports table
-- Date: 2025-07-11
-- Description: Remove airport image URL support (CAUTION: This will lose data!)

-- Remove image_url column from airports table
ALTER TABLE airports DROP COLUMN IF EXISTS image_url;

-- Verify the column was removed
SELECT column_name, data_type, is_nullable, character_maximum_length 
FROM information_schema.columns 
WHERE table_name = 'airports' AND column_name = 'image_url';
