#!/usr/bin/env python3
"""
Test inserting aircraft one at a time to see if that works better.
"""

import json
import sys
from pathlib import Path
import psycopg2
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection string for TimescaleDB
DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?sslmode=require"

# Path to your exported TAR1090 database files
DATABASE_PATH = Path(__file__).parent / 'database'

def insert_single_aircraft(cursor, aircraft_record):
    """Insert a single aircraft record."""
    insert_sql = """
    INSERT INTO aircraft (
        hex, registration, icao_type_designator, aircraft_description,
        is_military, is_interesting, country_code, database_source, 
        last_updated, created_at
    ) VALUES (
        %(hex)s, %(registration)s, %(icao_type_designator)s, %(aircraft_description)s,
        %(is_military)s, %(is_interesting)s, %(country_code)s, %(database_source)s,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    ON CONFLICT (hex) DO NOTHING
    """
    
    try:
        cursor.execute(insert_sql, aircraft_record)
        return cursor.rowcount > 0
    except Exception as e:
        logger.error(f"Error inserting aircraft {aircraft_record.get('hex', 'unknown')}: {e}")
        return False

def main():
    """Test inserting a few aircraft one at a time."""
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        logger.info("Connected to database")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        sys.exit(1)
    
    try:
        # Get one small file
        test_file = DATABASE_PATH / 'A60.json'
        if not test_file.exists():
            logger.error(f"Test file not found: {test_file}")
            return
        
        logger.info(f"Processing test file: {test_file.name}")
        
        with open(test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        file_prefix = test_file.stem.upper()
        count = 0
        inserted = 0
        
        for hex_suffix, aircraft_data in data.items():
            if hex_suffix == 'children':
                continue
            
            if count >= 5:  # Only process first 5 aircraft
                break
            
            try:
                full_hex_code = (file_prefix + hex_suffix).lower()
                registration = aircraft_data.get('r', '').strip() if aircraft_data.get('r') else None
                icao_type = aircraft_data.get('t', '').strip() if aircraft_data.get('t') else None
                description = aircraft_data.get('desc', '').strip() if aircraft_data.get('desc') else None
                flags = aircraft_data.get('f', '00')
                
                is_military = flags[0] == '1' if len(flags) >= 1 else False
                is_interesting = flags[1] == '1' if len(flags) >= 2 else False
                
                # Simple country mapping
                country_code = 'US' if full_hex_code[0] in '0124AB' else 'DE' if full_hex_code[0] in '3D' else 'CA' if full_hex_code[0] == 'C' else None
                
                aircraft_record = {
                    'hex': full_hex_code,
                    'registration': registration if registration and registration != 'DEFAULT' else None,
                    'icao_type_designator': icao_type,
                    'aircraft_description': description,
                    'is_military': is_military,
                    'is_interesting': is_interesting,
                    'country_code': country_code,
                    'database_source': 'test'
                }
                
                logger.info(f"Inserting aircraft {count+1}: {full_hex_code} - {registration}")
                
                if insert_single_aircraft(cursor, aircraft_record):
                    inserted += 1
                    conn.commit()
                    logger.info(f"✅ Successfully inserted {full_hex_code}")
                else:
                    logger.warning(f"❌ Failed to insert {full_hex_code}")
                
                count += 1
                
            except Exception as e:
                logger.error(f"Error processing aircraft {hex_suffix}: {e}")
                continue
        
        logger.info(f"🎉 Test complete! Processed {count} aircraft, inserted {inserted}")
        
        # Verify results
        cursor.execute("SELECT hex, registration, icao_type_designator, country_code FROM aircraft WHERE database_source = 'test'")
        results = cursor.fetchall()
        logger.info(f"Found {len(results)} test aircraft in database:")
        for row in results:
            logger.info(f"  {row[0]}: {row[1]} ({row[2]}) - {row[3]}")
        
        # Clean up
        cursor.execute("DELETE FROM aircraft WHERE database_source = 'test'")
        deleted = cursor.rowcount
        conn.commit()
        logger.info(f"Cleaned up {deleted} test records")
        
    except Exception as e:
        logger.error(f"Error during processing: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    main()
