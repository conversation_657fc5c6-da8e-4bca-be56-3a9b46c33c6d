# Aircraft Database Population Script

This directory contains the script and documentation for populating your aircraft table with comprehensive data from the TAR1090 aircraft database.

## Overview

The `populate_aircraft_database.py` script processes the exported TAR1090 database files and populates your PostgreSQL aircraft table with enhanced information including:

- **Aircraft Type Data**: ICAO type designators (A320, B738, etc.)
- **Operational Information**: Operator names, manufacturing years
- **Classification Flags**: Military status, interesting aircraft markers
- **Geographic Data**: Country codes derived from ICAO hex ranges
- **Wake Turbulence Categories**: L/M/H/J classifications

## Directory Structure

```
scripts/database/
├── README.md                    # This documentation
├── populate_aircraft_database.py # Main population script
└── database/                    # TAR1090 database files (you need to copy these)
    ├── icao_aircraft_types.json # Aircraft type mappings
    ├── 0.json                   # Aircraft data files
    ├── A.json
    ├── ...
    └── [other hex prefix files]
```

## Prerequisites

### 1. Database Schema

Ensure your aircraft table has been updated with the enhanced schema. Your current schema includes:

```sql
CREATE TABLE public.aircraft (
    hex text NOT NULL,
    category public."aircraft_category_enum" NULL,
    registration text NULL,
    icao_type_designator varchar(4) NULL,
    type_description varchar(10) NULL,
    wake_turbulence_category public.wake_turbulence_category_enum NULL,
    aircraft_description text NULL,
    "operator" text NULL,
    year_built int4 NULL,
    is_military bool DEFAULT false NULL,
    is_interesting bool DEFAULT false NULL,
    is_pia bool DEFAULT false NULL,
    is_ladd bool DEFAULT false NULL,
    country_code varchar(2) NULL,
    database_source varchar(50) NULL,
    last_updated timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT aircraft_pkey PRIMARY KEY (hex),
    CONSTRAINT aircraft_registration_key UNIQUE (registration)
);
```

### 2. Python Dependencies

Install required Python packages:

```bash
pip install psycopg2-binary
```

### 3. Database Files

Copy your exported TAR1090 database files to the `database/` subdirectory:

```bash
# Copy all JSON files from your context/database folder
cp /path/to/your/context/database/*.json scripts/database/database/
```

## Configuration

The script is pre-configured for your TimescaleDB instance:

```python
DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?ssl=require"
```

If you need to change the connection, edit the `DATABASE_URL` variable in the script.

## Usage

### Basic Usage

```bash
# Navigate to the scripts directory
cd scripts/database

# Run the population script
python populate_aircraft_database.py
```

### Expected Output

```
2024-01-15 10:30:00,123 - INFO - Loading ICAO aircraft types cache...
2024-01-15 10:30:01,456 - INFO - Loaded 8547 aircraft types
2024-01-15 10:30:01,789 - INFO - Connected to database
2024-01-15 10:30:02,012 - INFO - Found 387 database files to process
2024-01-15 10:30:02,345 - INFO - Processing file 1/387: 0.json
2024-01-15 10:30:03,678 - INFO - Inserted batch of 1000 aircraft. Total: 1000
...
2024-01-15 10:35:45,123 - INFO - Processing complete! Processed 245,678 aircraft, inserted/updated 245,678
```

## How It Works

### 1. Data Loading Process

1. **Type Cache Loading**: Loads `icao_aircraft_types.json` for aircraft type mappings
2. **File Discovery**: Finds all JSON files in the database directory
3. **Hierarchical Processing**: Processes files in the TAR1090 hierarchical structure
4. **Batch Processing**: Inserts data in batches of 1000 for performance

### 2. Data Mapping

The script maps TAR1090 data fields to your database schema:

| TAR1090 Field | Database Column | Description |
|---------------|-----------------|-------------|
| `r` | `registration` | Aircraft tail number |
| `t` | `icao_type_designator` | ICAO type code (A320, B738) |
| `f` | `is_military`, `is_interesting` | Flag parsing |
| `desc` | `aircraft_description` | Full aircraft description |

### 3. Enhanced Data Derivation

The script adds intelligent enhancements:

- **Country Codes**: Derived from ICAO hex ranges
- **Wake Turbulence**: Mapped from type descriptions
- **Type Information**: Enriched from the type cache
- **Timestamps**: Automatic creation and update tracking

### 4. Conflict Resolution

Uses PostgreSQL UPSERT logic:
- **New aircraft**: Inserts with all available data
- **Existing aircraft**: Updates only non-null fields, preserves existing data
- **Timestamps**: Always updates `last_updated` field

## Data Quality Features

### Flag Parsing

TAR1090 uses 2-character flag strings:
- Position 0: `1` = Military, `0` = Civilian
- Position 1: `1` = Interesting, `0` = Normal

Examples:
- `"00"` = Civilian, normal aircraft
- `"10"` = Military aircraft
- `"01"` = Civilian, interesting aircraft

### Country Detection

ICAO hex ranges map to countries:
- `0-4, A-B`: United States
- `C`: Canada  
- `D`: Germany
- `E`: United Kingdom
- `F`: France
- `3-9`: Various African countries

### Type Description Codes

ICAO type descriptions follow patterns:
- `H`: Helicopter
- `L1P`: Land plane, 1 engine, Piston
- `L2J`: Land plane, 2 engines, Jet
- `L2T`: Land plane, 2 engines, Turboprop

## Performance Characteristics

- **Batch Size**: 1000 records per database transaction
- **Memory Usage**: Processes files individually to minimize memory footprint
- **Network Efficiency**: Single connection with transaction batching
- **Conflict Handling**: Efficient UPSERT operations

## Monitoring and Verification

### Check Population Results

```sql
-- Count total aircraft
SELECT COUNT(*) FROM aircraft;

-- Count by data source
SELECT database_source, COUNT(*) 
FROM aircraft 
GROUP BY database_source;

-- Count by country
SELECT country_code, COUNT(*) 
FROM aircraft 
WHERE country_code IS NOT NULL
GROUP BY country_code 
ORDER BY COUNT(*) DESC;

-- Count military aircraft
SELECT COUNT(*) FROM aircraft WHERE is_military = true;

-- Sample enhanced data
SELECT hex, registration, icao_type_designator, operator, country_code
FROM aircraft 
WHERE icao_type_designator IS NOT NULL
LIMIT 10;
```

### Verify Data Quality

```sql
-- Check for aircraft with type information
SELECT 
    COUNT(*) as total,
    COUNT(icao_type_designator) as with_type,
    COUNT(operator) as with_operator,
    COUNT(country_code) as with_country
FROM aircraft;

-- Check wake turbulence distribution
SELECT wake_turbulence_category, COUNT(*)
FROM aircraft
WHERE wake_turbulence_category IS NOT NULL
GROUP BY wake_turbulence_category;
```

## Troubleshooting

### Common Issues

1. **Connection Errors**
   ```
   Error: Failed to connect to database
   ```
   - Verify DATABASE_URL is correct
   - Check network connectivity to TimescaleDB
   - Ensure SSL certificates are valid

2. **File Not Found**
   ```
   Error: Database path does not exist
   ```
   - Ensure database files are in `scripts/database/database/`
   - Check file permissions

3. **Import Errors**
   ```
   ModuleNotFoundError: No module named 'psycopg2'
   ```
   - Install dependencies: `pip install psycopg2-binary`

4. **Schema Errors**
   ```
   Error: column "icao_type_designator" does not exist
   ```
   - Run the database migration first
   - Verify schema matches expected structure

### Performance Issues

- **Slow Processing**: Normal for large datasets (200k+ aircraft)
- **Memory Usage**: Script processes files individually to minimize memory
- **Network Timeouts**: TimescaleDB connection should handle this automatically

### Data Issues

- **Missing Type Information**: Some aircraft may not have complete data
- **Registration Conflicts**: Script handles duplicate registrations gracefully
- **Country Detection**: Limited to major ICAO hex ranges

## Maintenance

### Regular Updates

To keep aircraft data current:

1. **Download Updated TAR1090 Data**: Get fresh database exports
2. **Replace Database Files**: Update files in `database/` directory
3. **Re-run Script**: Execute population script again

The script's UPSERT logic ensures:
- New aircraft are added
- Existing aircraft are updated with new information
- No duplicate entries are created

### Monitoring

Set up monitoring for:
- Database growth over time
- Data freshness (check `last_updated` timestamps)
- Coverage metrics (percentage with type information)

This script provides a robust foundation for maintaining comprehensive aircraft data in your TarmacTrack application.
