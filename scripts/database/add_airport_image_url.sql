-- Migration: Add image_url column to airports table
-- Date: 2025-07-11
-- Description: Add support for storing airport image URLs from cloud storage

-- Add image_url column to airports table
ALTER TABLE airports ADD COLUMN IF NOT EXISTS image_url VARCHAR(500) NULL;

-- Add comment to document the column
COMMENT ON COLUMN airports.image_url IS 'URL to airport image stored in cloud storage (Backblaze B2)';

-- Verify the column was added
SELECT column_name, data_type, is_nullable, character_maximum_length 
FROM information_schema.columns 
WHERE table_name = 'airports' AND column_name = 'image_url';
