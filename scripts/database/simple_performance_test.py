#!/usr/bin/env python3
"""
Simple performance test to measure database insert performance.
"""

import io
import time
import random
import psycopg2
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection string for TimescaleDB
DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?sslmode=require"

def cleanup_test_data(cursor):
    """Clean up any existing test data."""
    cursor.execute("DELETE FROM aircraft WHERE database_source = 'simple_test'")
    deleted = cursor.rowcount
    if deleted > 0:
        logger.info(f"Cleaned up {deleted} existing test records")

def test_copy_from_performance(cursor, record_count=1000):
    """Test COPY FROM performance with unique data."""
    logger.info(f"Testing COPY FROM with {record_count} records...")
    
    # Generate unique timestamp-based data
    timestamp = int(time.time() * 1000000)  # microseconds for uniqueness
    
    start_time = time.time()
    
    # Prepare data for COPY
    copy_data = io.StringIO()
    for i in range(record_count):
        hex_code = f'{timestamp:x}{i:06x}'[:12]  # Ensure unique hex codes
        registration = f'T{timestamp % 100000:05d}{i:03d}' if i % 3 == 0 else '\\N'
        
        values = [
            hex_code,
            registration,
            'B738' if i % 2 == 0 else 'A320',  # icao_type_designator
            'B737-800' if i % 2 == 0 else 'A320',  # type_description
            'M',  # wake_turbulence_category
            f'Test Aircraft {i}',  # aircraft_description
            '\\N',  # operator
            '\\N',  # year_built
            'false',  # is_military
            str(i % 20 == 0).lower(),  # is_interesting
            'false',  # is_pia
            'false',  # is_ladd
            'US',  # country_code
            'simple_test',  # database_source
            'NOW()',  # last_updated
            'NOW()'   # created_at
        ]
        copy_data.write('\t'.join(values) + '\n')
    
    copy_data.seek(0)
    
    # Perform COPY FROM
    cursor.copy_from(
        copy_data,
        'aircraft',
        columns=(
            'hex', 'registration', 'icao_type_designator', 'type_description',
            'wake_turbulence_category', 'aircraft_description', 'operator', 'year_built',
            'is_military', 'is_interesting', 'is_pia', 'is_ladd',
            'country_code', 'database_source', 'last_updated', 'created_at'
        )
    )
    
    end_time = time.time()
    duration = end_time - start_time
    rate = record_count / duration
    
    logger.info(f"✅ COPY FROM: {duration:.2f}s for {record_count} records")
    logger.info(f"📊 Rate: {rate:.1f} records/second")
    
    return duration, record_count, rate

def test_with_optimizations(cursor, record_count=1000):
    """Test COPY FROM with database optimizations."""
    logger.info(f"Testing OPTIMIZED COPY FROM with {record_count} records...")
    
    # Apply optimizations
    logger.info("Applying database optimizations...")
    cursor.execute("SET synchronous_commit = off")
    cursor.execute("SET work_mem = '256MB'")
    
    # Generate unique timestamp-based data
    timestamp = int(time.time() * 1000000) + 1000000  # Offset to avoid conflicts
    
    start_time = time.time()
    
    # Prepare data for COPY
    copy_data = io.StringIO()
    for i in range(record_count):
        hex_code = f'{timestamp:x}{i:06x}'[:12]  # Ensure unique hex codes
        registration = f'O{timestamp % 100000:05d}{i:03d}' if i % 3 == 0 else '\\N'
        
        values = [
            hex_code,
            registration,
            'B738' if i % 2 == 0 else 'A320',
            'B737-800' if i % 2 == 0 else 'A320',
            'M',
            f'Optimized Test Aircraft {i}',
            '\\N', '\\N',
            'false',
            str(i % 20 == 0).lower(),
            'false', 'false',
            'US',
            'simple_test',
            'NOW()', 'NOW()'
        ]
        copy_data.write('\t'.join(values) + '\n')
    
    copy_data.seek(0)
    
    # Perform COPY FROM
    cursor.copy_from(
        copy_data,
        'aircraft',
        columns=(
            'hex', 'registration', 'icao_type_designator', 'type_description',
            'wake_turbulence_category', 'aircraft_description', 'operator', 'year_built',
            'is_military', 'is_interesting', 'is_pia', 'is_ladd',
            'country_code', 'database_source', 'last_updated', 'created_at'
        )
    )
    
    end_time = time.time()
    duration = end_time - start_time
    rate = record_count / duration
    
    # Restore settings
    cursor.execute("SET synchronous_commit = on")
    
    logger.info(f"✅ Optimized COPY FROM: {duration:.2f}s for {record_count} records")
    logger.info(f"📊 Rate: {rate:.1f} records/second")
    
    return duration, record_count, rate

def estimate_full_load_time(rate_per_second, total_records):
    """Estimate time for full database load."""
    total_seconds = total_records / rate_per_second
    hours = total_seconds / 3600
    minutes = (total_seconds % 3600) / 60
    
    logger.info(f"📈 Estimated time for {total_records:,} records:")
    logger.info(f"   At {rate_per_second:.0f} records/sec: {hours:.1f} hours ({minutes:.0f} minutes)")

def main():
    """Run simple performance test."""
    logger.info("🧪 Starting simple performance test...")
    
    # Connect to database
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        logger.info("Connected to database")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        return
    
    try:
        # Clean up any existing test data
        cleanup_test_data(cursor)
        conn.commit()
        
        # Test 1: Basic COPY FROM
        duration1, count1, rate1 = test_copy_from_performance(cursor, 500)
        conn.commit()
        
        # Test 2: Optimized COPY FROM
        duration2, count2, rate2 = test_with_optimizations(cursor, 1000)
        conn.commit()
        
        # Clean up test data
        cleanup_test_data(cursor)
        conn.commit()
        
        # Results
        logger.info("\n" + "="*60)
        logger.info("PERFORMANCE TEST RESULTS")
        logger.info("="*60)
        logger.info(f"Basic COPY FROM:     {rate1:8.1f} records/second")
        logger.info(f"Optimized COPY FROM: {rate2:8.1f} records/second")
        
        improvement = rate2 / rate1
        logger.info(f"Optimization improvement: {improvement:.1f}x")
        
        # Estimate full load times
        logger.info("\n" + "="*60)
        logger.info("ESTIMATED FULL DATABASE LOAD TIMES")
        logger.info("="*60)
        
        # Estimate total records (rough calculation based on file count)
        estimated_total_records = 351 * 1000  # 351 files * ~1000 records each
        
        logger.info(f"Estimated total records to load: {estimated_total_records:,}")
        estimate_full_load_time(rate1, estimated_total_records)
        estimate_full_load_time(rate2, estimated_total_records)
        
        logger.info("\n🎯 RECOMMENDATIONS:")
        logger.info("1. Use the ultra-optimized script for full database load")
        logger.info("2. Drop constraints/indexes before bulk insert")
        logger.info("3. Use large batch sizes (25k-50k records)")
        logger.info("4. Minimize commits during bulk operations")
        logger.info("5. Consider running during off-peak hours")
        
    except Exception as e:
        logger.error(f"Error during performance test: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    main()
