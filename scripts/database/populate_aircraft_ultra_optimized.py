#!/usr/bin/env python3
"""
Ultra-optimized aircraft database population script designed for high-latency connections.
Minimizes round trips and maximizes throughput for China-to-US database connections.
"""

import json
import sys
import io
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection string for TimescaleDB
DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?sslmode=require"

# Path to your exported TAR1090 database files
DATABASE_PATH = Path(__file__).parent / 'database'

def parse_tar1090_flags(flags_string: str) -> tuple[bool, bool]:
    """Parse TAR1090 2-character flag string into boolean fields."""
    if not flags_string or len(flags_string) != 2:
        return False, False
    
    is_military = flags_string[0] == '1'
    is_interesting = flags_string[1] == '1'
    return is_military, is_interesting

def get_country_from_hex(hex_code: str) -> Optional[str]:
    """Get country code from ICAO hex code based on known ranges."""
    if not hex_code:
        return None
    
    hex_upper = hex_code.upper()
    first_char = hex_upper[0]
    
    country_mapping = {
        '0': 'US', '1': 'US', '2': 'US', '4': 'US',
        '3': 'DE', 'A': 'US', 'B': 'US', 'C': 'CA', 
        'D': 'DE', 'E': 'GB', 'F': 'FR'
    }
    
    if hex_upper.startswith('3D'):
        return 'DE'
    elif hex_upper.startswith('C0'):
        return 'CA'
    elif hex_upper.startswith('40'):
        return 'GB'
    
    return country_mapping.get(first_char)

def load_type_cache(database_path: Path) -> Dict[str, tuple]:
    """Load the ICAO aircraft types cache."""
    type_cache_file = database_path / 'icao_aircraft_types.json'
    
    if not type_cache_file.exists():
        logger.warning(f"Type cache file not found: {type_cache_file}")
        return {}
    
    try:
        with open(type_cache_file, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)
        
        type_cache = {}
        for icao_type, type_info in raw_data.items():
            if isinstance(type_info, dict):
                desc = type_info.get('desc', '')
                wtc = type_info.get('wtc', '')
                # Validate wake category
                valid_wake_categories = {'L', 'M', 'H', 'J'}
                if wtc not in valid_wake_categories:
                    wtc = None
                type_cache[icao_type.upper()] = (None, desc, wtc)
            
        logger.info(f"Loaded {len(type_cache)} aircraft types from cache")
        return type_cache
        
    except Exception as e:
        logger.error(f"Error loading type cache: {e}")
        return {}

def process_all_files_in_memory(database_path: Path, type_cache: Dict[str, tuple]) -> List[Dict[str, Any]]:
    """Process ALL files in memory first to minimize database round trips."""
    logger.info("🔄 Processing all JSON files in memory...")
    
    all_aircraft = []
    json_files = list(database_path.glob('*.json'))
    json_files = [f for f in json_files if f.name != 'icao_aircraft_types.json']
    
    logger.info(f"Found {len(json_files)} files to process")
    
    for i, file_path in enumerate(json_files):
        if i % 50 == 0:  # Progress every 50 files
            logger.info(f"Processing file {i+1}/{len(json_files)}: {file_path.name}")
        
        try:
            if file_path.stat().st_size == 0:
                continue
                
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not data or not isinstance(data, dict):
                continue
            
            file_prefix = file_path.stem.upper()
            
            for hex_suffix, aircraft_data in data.items():
                if hex_suffix == 'children' or not isinstance(aircraft_data, dict):
                    continue
                
                try:
                    full_hex_code = (file_prefix + hex_suffix).lower()
                    
                    registration = aircraft_data.get('r', '').strip() if aircraft_data.get('r') else ''
                    icao_type = aircraft_data.get('t', '').strip() if aircraft_data.get('t') else ''
                    flags = aircraft_data.get('f', '00') if aircraft_data.get('f') else '00'
                    description = aircraft_data.get('desc', '').strip() if aircraft_data.get('desc') else ''
                    
                    if not hex_suffix or len(hex_suffix) < 1:
                        continue
                    
                    is_military, is_interesting = parse_tar1090_flags(flags)
                    
                    # Get type information from cache
                    type_info = type_cache.get(icao_type.upper(), [None, None, None]) if icao_type else [None, None, None]
                    type_long = type_info[0] if len(type_info) > 0 and type_info[0] else None
                    type_description = type_info[1] if len(type_info) > 1 and type_info[1] else None
                    wake_category = type_info[2] if len(type_info) > 2 and type_info[2] else None
                    
                    country_code = get_country_from_hex(full_hex_code)
                    
                    aircraft_record = {
                        'hex': full_hex_code,
                        'registration': registration if registration and registration != 'DEFAULT' else None,
                        'icao_type_designator': icao_type if icao_type else None,
                        'type_description': type_description,
                        'wake_turbulence_category': wake_category,
                        'aircraft_description': description if description else type_long,
                        'is_military': is_military,
                        'is_interesting': is_interesting,
                        'country_code': country_code,
                        'database_source': 'tar1090'
                    }
                    
                    all_aircraft.append(aircraft_record)
                    
                except Exception as e:
                    continue
                    
        except Exception as e:
            logger.warning(f"Error processing file {file_path.name}: {e}")
            continue
    
    logger.info(f"✅ Processed {len(all_aircraft)} aircraft records from {len(json_files)} files")
    return all_aircraft

def ultra_bulk_insert(cursor, aircraft_records: List[Dict[str, Any]], chunk_size: int = 50000) -> int:
    """Ultra-optimized bulk insert using COPY FROM with large chunks."""
    if not aircraft_records:
        return 0
    
    total_inserted = 0
    
    # Process in chunks to avoid memory issues
    for i in range(0, len(aircraft_records), chunk_size):
        chunk = aircraft_records[i:i + chunk_size]
        logger.info(f"Inserting chunk {i//chunk_size + 1}: {len(chunk)} records")
        
        try:
            # Prepare data for COPY FROM
            copy_data = io.StringIO()
            
            for record in chunk:
                # Format values for COPY (tab-separated, NULL as \N)
                values = [
                    record['hex'],
                    record['registration'] or '\\N',
                    record['icao_type_designator'] or '\\N',
                    record['type_description'] or '\\N',
                    record['wake_turbulence_category'] or '\\N',
                    record['aircraft_description'] or '\\N',
                    '\\N',  # operator (not in TAR1090 data)
                    '\\N',  # year_built (not in TAR1090 data)
                    str(record['is_military']).lower(),
                    str(record['is_interesting']).lower(),
                    'false',  # is_pia
                    'false',  # is_ladd
                    record['country_code'] or '\\N',
                    record['database_source'],
                    'NOW()',  # last_updated
                    'NOW()'   # created_at
                ]
                
                copy_data.write('\t'.join(values) + '\n')
            
            copy_data.seek(0)
            
            # Use COPY FROM for bulk insert
            cursor.copy_from(
                copy_data,
                'aircraft',
                columns=(
                    'hex', 'registration', 'icao_type_designator', 'type_description',
                    'wake_turbulence_category', 'aircraft_description', 'operator', 'year_built',
                    'is_military', 'is_interesting', 'is_pia', 'is_ladd',
                    'country_code', 'database_source', 'last_updated', 'created_at'
                )
            )
            
            total_inserted += len(chunk)
            logger.info(f"✅ Inserted chunk: {len(chunk)} records. Total: {total_inserted}")
            
        except Exception as e:
            logger.error(f"Error inserting chunk: {e}")
            continue
    
    return total_inserted

def prepare_ultra_optimization(cursor):
    """Prepare database for ultra-optimized bulk insert."""
    logger.info("🚀 Preparing database for ULTRA-OPTIMIZED bulk insert...")
    
    try:
        # Drop ALL constraints and indexes temporarily
        logger.info("Dropping ALL constraints and indexes...")
        cursor.execute("ALTER TABLE aircraft DROP CONSTRAINT IF EXISTS aircraft_registration_key")
        cursor.execute("DROP INDEX IF EXISTS ix_aircraft_hex")
        cursor.execute("DROP TRIGGER IF EXISTS trg_aircraft_update_timestamp ON aircraft")
        
        # Ultra-aggressive optimization settings
        logger.info("Setting ULTRA-AGGRESSIVE optimization parameters...")
        cursor.execute("SET maintenance_work_mem = '4GB'")
        cursor.execute("SET work_mem = '1GB'")
        cursor.execute("SET checkpoint_completion_target = 0.9")
        cursor.execute("SET wal_buffers = '128MB'")
        cursor.execute("SET synchronous_commit = off")
        cursor.execute("SET fsync = off")  # DANGEROUS but fast
        cursor.execute("SET full_page_writes = off")  # DANGEROUS but fast
        cursor.execute("SET commit_delay = 100000")
        cursor.execute("SET commit_siblings = 10")
        
        logger.info("⚠️  WARNING: Database is in UNSAFE mode for maximum speed!")
        logger.info("✅ Ultra-optimization complete")
        
    except Exception as e:
        logger.error(f"Error preparing ultra-optimization: {e}")

def restore_ultra_optimization(cursor):
    """Restore database to safe state after ultra-optimized insert."""
    logger.info("🔧 Restoring database to SAFE state...")
    
    try:
        # Restore safe settings
        logger.info("Restoring safe database settings...")
        cursor.execute("SET synchronous_commit = on")
        cursor.execute("SET fsync = on")
        cursor.execute("SET full_page_writes = on")
        cursor.execute("SET commit_delay = 0")
        cursor.execute("SET commit_siblings = 5")
        
        # Recreate trigger
        logger.info("Recreating trigger...")
        cursor.execute("""
            CREATE TRIGGER trg_aircraft_update_timestamp 
            BEFORE UPDATE ON aircraft 
            FOR EACH ROW 
            EXECUTE FUNCTION update_aircraft_last_updated()
        """)
        
        # Recreate constraints and indexes
        logger.info("Recreating constraints and indexes...")
        cursor.execute("""
            CREATE UNIQUE INDEX aircraft_registration_key 
            ON aircraft (registration) 
            WHERE registration IS NOT NULL AND registration != ''
        """)
        
        # Essential indexes only
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_aircraft_icao_type ON aircraft (icao_type_designator)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_aircraft_country ON aircraft (country_code)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_aircraft_database_source ON aircraft (database_source)")
        
        # Analyze table
        logger.info("Analyzing table...")
        cursor.execute("ANALYZE aircraft")
        
        logger.info("✅ Database restored to safe state")
        
    except Exception as e:
        logger.error(f"Error restoring ultra-optimization: {e}")

def main():
    """Main function for ultra-optimized population."""
    if not DATABASE_PATH.exists():
        logger.error(f"Database path does not exist: {DATABASE_PATH}")
        sys.exit(1)
    
    start_time = time.time()
    
    # Load type cache
    logger.info("Loading ICAO aircraft types cache...")
    type_cache = load_type_cache(DATABASE_PATH)
    
    # Process all files in memory first
    all_aircraft = process_all_files_in_memory(DATABASE_PATH, type_cache)
    
    if not all_aircraft:
        logger.error("No aircraft records found!")
        sys.exit(1)
    
    logger.info(f"📊 Total aircraft to insert: {len(all_aircraft)}")
    
    # Connect to database
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        logger.info("Connected to database")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        sys.exit(1)
    
    try:
        # Ultra-optimize database
        prepare_ultra_optimization(cursor)
        conn.commit()
        
        # Single massive insert operation
        logger.info("🚀 Starting ULTRA-OPTIMIZED bulk insert...")
        insert_start = time.time()
        
        total_inserted = ultra_bulk_insert(cursor, all_aircraft)
        
        # Single commit for everything
        logger.info("💾 Performing final commit...")
        conn.commit()
        
        insert_end = time.time()
        insert_duration = insert_end - insert_start
        
        # Restore database
        restore_ultra_optimization(cursor)
        conn.commit()
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        logger.info(f"🎉 ULTRA-OPTIMIZED insertion complete!")
        logger.info(f"📊 Total records processed: {len(all_aircraft)}")
        logger.info(f"📊 Total records inserted: {total_inserted}")
        logger.info(f"⏱️  Insert time: {insert_duration:.2f} seconds")
        logger.info(f"⏱️  Total time: {total_duration:.2f} seconds")
        logger.info(f"🚀 Insert rate: {total_inserted / insert_duration:.0f} records/second")
        
    except Exception as e:
        logger.error(f"Error during ultra-optimized processing: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    main()
