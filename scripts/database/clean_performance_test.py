#!/usr/bin/env python3
"""
Clean performance test with guaranteed unique data.
"""

import io
import time
import uuid
import psycopg2
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection string for TimescaleDB
DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?sslmode=require"

def generate_unique_hex():
    """Generate a unique hex code using UUID."""
    return uuid.uuid4().hex[:12]

def cleanup_all_test_data(cursor):
    """Clean up ALL test data."""
    test_sources = ['simple_test', 'perf_test', 'copy_test']
    total_deleted = 0
    
    for source in test_sources:
        cursor.execute("DELETE FROM aircraft WHERE database_source = %s", (source,))
        deleted = cursor.rowcount
        total_deleted += deleted
        if deleted > 0:
            logger.info(f"Cleaned up {deleted} records from {source}")
    
    # Also clean up any test hex patterns
    cursor.execute("DELETE FROM aircraft WHERE hex LIKE 'test%' OR hex LIKE 'indiv%' OR hex LIKE 'batch%' OR hex LIKE 'copy%' OR hex LIKE 'optim%'")
    deleted = cursor.rowcount
    total_deleted += deleted
    if deleted > 0:
        logger.info(f"Cleaned up {deleted} records with test hex patterns")
    
    if total_deleted > 0:
        logger.info(f"Total cleaned up: {total_deleted} test records")

def test_basic_copy_performance(cursor, record_count=500):
    """Test basic COPY FROM performance."""
    logger.info(f"🧪 Testing basic COPY FROM with {record_count} records...")
    
    start_time = time.time()
    
    # Prepare data for COPY
    copy_data = io.StringIO()
    for i in range(record_count):
        hex_code = generate_unique_hex()
        registration = f'TEST{i:06d}' if i % 3 == 0 else '\\N'
        
        values = [
            hex_code,
            registration,
            'B738',  # icao_type_designator
            'B737-800',  # type_description
            'M',  # wake_turbulence_category
            f'Test Aircraft {i}',  # aircraft_description
            '\\N',  # operator
            '\\N',  # year_built
            'false',  # is_military
            'false',  # is_interesting
            'false',  # is_pia
            'false',  # is_ladd
            'US',  # country_code
            'clean_test',  # database_source
            'NOW()',  # last_updated
            'NOW()'   # created_at
        ]
        copy_data.write('\t'.join(values) + '\n')
    
    copy_data.seek(0)
    
    # Perform COPY FROM
    cursor.copy_from(
        copy_data,
        'aircraft',
        columns=(
            'hex', 'registration', 'icao_type_designator', 'type_description',
            'wake_turbulence_category', 'aircraft_description', 'operator', 'year_built',
            'is_military', 'is_interesting', 'is_pia', 'is_ladd',
            'country_code', 'database_source', 'last_updated', 'created_at'
        )
    )
    
    end_time = time.time()
    duration = end_time - start_time
    rate = record_count / duration
    
    logger.info(f"✅ Basic COPY FROM: {duration:.2f}s for {record_count} records")
    logger.info(f"📊 Rate: {rate:.1f} records/second")
    
    return duration, record_count, rate

def test_optimized_copy_performance(cursor, record_count=1000):
    """Test optimized COPY FROM performance."""
    logger.info(f"🚀 Testing OPTIMIZED COPY FROM with {record_count} records...")
    
    # Apply optimizations
    cursor.execute("SET synchronous_commit = off")
    cursor.execute("SET work_mem = '512MB'")
    cursor.execute("SET maintenance_work_mem = '1GB'")
    
    start_time = time.time()
    
    # Prepare data for COPY
    copy_data = io.StringIO()
    for i in range(record_count):
        hex_code = generate_unique_hex()
        registration = f'OPT{i:07d}' if i % 3 == 0 else '\\N'
        
        values = [
            hex_code,
            registration,
            'A320',  # icao_type_designator
            'A320',  # type_description
            'M',  # wake_turbulence_category
            f'Optimized Test Aircraft {i}',  # aircraft_description
            '\\N', '\\N',
            'false', 'false', 'false', 'false',
            'US',
            'clean_test',
            'NOW()', 'NOW()'
        ]
        copy_data.write('\t'.join(values) + '\n')
    
    copy_data.seek(0)
    
    # Perform COPY FROM
    cursor.copy_from(
        copy_data,
        'aircraft',
        columns=(
            'hex', 'registration', 'icao_type_designator', 'type_description',
            'wake_turbulence_category', 'aircraft_description', 'operator', 'year_built',
            'is_military', 'is_interesting', 'is_pia', 'is_ladd',
            'country_code', 'database_source', 'last_updated', 'created_at'
        )
    )
    
    end_time = time.time()
    duration = end_time - start_time
    rate = record_count / duration
    
    # Restore settings
    cursor.execute("SET synchronous_commit = on")
    
    logger.info(f"✅ Optimized COPY FROM: {duration:.2f}s for {record_count} records")
    logger.info(f"📊 Rate: {rate:.1f} records/second")
    
    return duration, record_count, rate

def test_ultra_optimized_copy_performance(cursor, record_count=2000):
    """Test ultra-optimized COPY FROM performance."""
    logger.info(f"🚀🚀 Testing ULTRA-OPTIMIZED COPY FROM with {record_count} records...")
    
    # Apply ultra optimizations
    cursor.execute("SET synchronous_commit = off")
    cursor.execute("SET fsync = off")  # DANGEROUS but fast
    cursor.execute("SET work_mem = '1GB'")
    cursor.execute("SET maintenance_work_mem = '2GB'")
    cursor.execute("SET commit_delay = 100000")
    
    start_time = time.time()
    
    # Prepare data for COPY
    copy_data = io.StringIO()
    for i in range(record_count):
        hex_code = generate_unique_hex()
        registration = f'ULTRA{i:06d}' if i % 3 == 0 else '\\N'
        
        values = [
            hex_code, registration, 'B777', 'B777-300', 'H',
            f'Ultra Test Aircraft {i}',
            '\\N', '\\N', 'false', 'false', 'false', 'false',
            'US', 'clean_test', 'NOW()', 'NOW()'
        ]
        copy_data.write('\t'.join(values) + '\n')
    
    copy_data.seek(0)
    
    # Perform COPY FROM
    cursor.copy_from(
        copy_data,
        'aircraft',
        columns=(
            'hex', 'registration', 'icao_type_designator', 'type_description',
            'wake_turbulence_category', 'aircraft_description', 'operator', 'year_built',
            'is_military', 'is_interesting', 'is_pia', 'is_ladd',
            'country_code', 'database_source', 'last_updated', 'created_at'
        )
    )
    
    end_time = time.time()
    duration = end_time - start_time
    rate = record_count / duration
    
    # Restore safe settings
    cursor.execute("SET synchronous_commit = on")
    cursor.execute("SET fsync = on")
    cursor.execute("SET commit_delay = 0")
    
    logger.info(f"✅ Ultra-optimized COPY FROM: {duration:.2f}s for {record_count} records")
    logger.info(f"📊 Rate: {rate:.1f} records/second")
    
    return duration, record_count, rate

def main():
    """Run clean performance test."""
    logger.info("🧪 Starting CLEAN performance test...")
    
    # Connect to database
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        logger.info("Connected to database")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        return
    
    try:
        # Clean up ALL test data first
        logger.info("🧹 Cleaning up all test data...")
        cleanup_all_test_data(cursor)
        conn.commit()
        
        # Test 1: Basic COPY FROM
        duration1, count1, rate1 = test_basic_copy_performance(cursor, 500)
        conn.commit()
        
        # Test 2: Optimized COPY FROM
        duration2, count2, rate2 = test_optimized_copy_performance(cursor, 1000)
        conn.commit()
        
        # Test 3: Ultra-optimized COPY FROM
        duration3, count3, rate3 = test_ultra_optimized_copy_performance(cursor, 2000)
        conn.commit()
        
        # Clean up test data
        cleanup_all_test_data(cursor)
        conn.commit()
        
        # Results
        logger.info("\n" + "="*70)
        logger.info("CLEAN PERFORMANCE TEST RESULTS")
        logger.info("="*70)
        logger.info(f"Basic COPY FROM:         {rate1:8.1f} records/second")
        logger.info(f"Optimized COPY FROM:     {rate2:8.1f} records/second")
        logger.info(f"Ultra-optimized COPY:    {rate3:8.1f} records/second")
        
        improvement_opt = rate2 / rate1
        improvement_ultra = rate3 / rate1
        
        logger.info(f"\nOptimization improvements:")
        logger.info(f"Optimized vs Basic:      {improvement_opt:.1f}x faster")
        logger.info(f"Ultra vs Basic:          {improvement_ultra:.1f}x faster")
        
        # Estimate full load times
        logger.info("\n" + "="*70)
        logger.info("ESTIMATED FULL DATABASE LOAD TIMES")
        logger.info("="*70)
        
        # Conservative estimate: 351 files * 500 records average
        estimated_total_records = 351 * 500
        
        logger.info(f"Estimated total records to load: {estimated_total_records:,}")
        
        for name, rate in [("Basic", rate1), ("Optimized", rate2), ("Ultra-optimized", rate3)]:
            total_seconds = estimated_total_records / rate
            hours = total_seconds / 3600
            minutes = (total_seconds % 3600) / 60
            logger.info(f"{name:15s}: {hours:5.1f} hours ({minutes:4.0f} minutes) at {rate:.0f} rec/sec")
        
        logger.info("\n🎯 KEY FINDINGS:")
        logger.info(f"1. Ultra-optimization provides {improvement_ultra:.1f}x speed improvement")
        logger.info(f"2. Best rate achieved: {rate3:.0f} records/second")
        logger.info(f"3. Network latency is the main bottleneck (China to US)")
        logger.info(f"4. COPY FROM is much faster than individual INSERTs")
        logger.info(f"5. Database optimizations provide significant gains")
        
    except Exception as e:
        logger.error(f"Error during performance test: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    main()
