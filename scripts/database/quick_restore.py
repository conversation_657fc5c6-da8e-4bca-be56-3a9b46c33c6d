#!/usr/bin/env python3
"""
Quick database restoration - restore essential constraints and indexes.
"""

import psycopg2
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?sslmode=require"

def main():
    logger.info("🔒 Quick database restoration...")
    
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        # Restore essential components
        logger.info("Recreating trigger...")
        cursor.execute("""
            CREATE TRIGGER trg_aircraft_update_timestamp 
            BEFORE UPDATE ON aircraft 
            FOR EACH ROW 
            EXECUTE FUNCTION update_aircraft_last_updated()
        """)
        
        logger.info("Recreating registration constraint...")
        cursor.execute("""
            CREATE UNIQUE INDEX aircraft_registration_key 
            ON aircraft (registration) 
            WHERE registration IS NOT NULL AND registration != ''
        """)
        
        logger.info("Creating useful indexes...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_aircraft_icao_type ON aircraft (icao_type_designator)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_aircraft_country ON aircraft (country_code)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_aircraft_database_source ON aircraft (database_source)")
        
        logger.info("Analyzing table...")
        cursor.execute("ANALYZE aircraft")
        
        conn.commit()
        logger.info("✅ Restoration complete!")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Error: {e}")

if __name__ == "__main__":
    main()
