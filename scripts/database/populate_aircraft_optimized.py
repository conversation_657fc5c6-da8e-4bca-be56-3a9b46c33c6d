#!/usr/bin/env python3
"""
Optimized aircraft database population script using COPY FROM for maximum performance.
"""

import json
import sys
import io
from pathlib import Path
from typing import Dict, Any, Optional
import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection string for TimescaleDB
DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?sslmode=require"

# Path to your exported TAR1090 database files
DATABASE_PATH = Path(__file__).parent / 'database'

def parse_tar1090_flags(flags_string: str) -> tuple[bool, bool]:
    """Parse TAR1090 2-character flag string into boolean fields."""
    if not flags_string or len(flags_string) != 2:
        return False, False
    
    is_military = flags_string[0] == '1'
    is_interesting = flags_string[1] == '1'
    return is_military, is_interesting

def get_country_from_hex(hex_code: str) -> Optional[str]:
    """Get country code from ICAO hex code based on known ranges."""
    if not hex_code:
        return None
    
    hex_upper = hex_code.upper()
    first_char = hex_upper[0]
    
    country_mapping = {
        '0': 'US', '1': 'US', '2': 'US', '4': 'US',
        '3': 'DE', 'A': 'US', 'B': 'US', 'C': 'CA', 
        'D': 'DE', 'E': 'GB', 'F': 'FR'
    }
    
    if hex_upper.startswith('3D'):
        return 'DE'
    elif hex_upper.startswith('C0'):
        return 'CA'
    elif hex_upper.startswith('40'):
        return 'GB'
    
    return country_mapping.get(first_char)

def load_type_cache(database_path: Path) -> Dict[str, tuple]:
    """Load the ICAO aircraft types cache."""
    type_cache_file = database_path / 'icao_aircraft_types.json'
    
    if not type_cache_file.exists():
        logger.warning(f"Type cache file not found: {type_cache_file}")
        return {}
    
    try:
        with open(type_cache_file, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)
        
        type_cache = {}
        for icao_type, type_info in raw_data.items():
            if isinstance(type_info, dict):
                desc = type_info.get('desc', '')
                wtc = type_info.get('wtc', '')
                # Validate wake category
                valid_wake_categories = {'L', 'M', 'H', 'J'}
                if wtc not in valid_wake_categories:
                    wtc = None
                type_cache[icao_type.upper()] = (None, desc, wtc)
            
        logger.info(f"Loaded {len(type_cache)} aircraft types from cache")
        return type_cache
        
    except Exception as e:
        logger.error(f"Error loading type cache: {e}")
        return {}

def process_aircraft_file(file_path: Path, type_cache: Dict[str, tuple]) -> list[Dict[str, Any]]:
    """Process a single aircraft database file and return aircraft records."""
    aircraft_records = []
    
    try:
        if file_path.stat().st_size == 0:
            logger.warning(f"File {file_path.name} is empty, skipping")
            return aircraft_records
            
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not data or not isinstance(data, dict):
            logger.warning(f"File {file_path.name} contains no valid aircraft data")
            return aircraft_records
        
        file_prefix = file_path.stem.upper()
        
        for hex_suffix, aircraft_data in data.items():
            if hex_suffix == 'children':
                continue
            
            try:
                full_hex_code = (file_prefix + hex_suffix).lower()
                
                if not isinstance(aircraft_data, dict):
                    continue
                
                registration = aircraft_data.get('r', '').strip() if aircraft_data.get('r') else ''
                icao_type = aircraft_data.get('t', '').strip() if aircraft_data.get('t') else ''
                flags = aircraft_data.get('f', '00') if aircraft_data.get('f') else '00'
                description = aircraft_data.get('desc', '').strip() if aircraft_data.get('desc') else ''
                
                if not hex_suffix or len(hex_suffix) < 1:
                    continue
                
                is_military, is_interesting = parse_tar1090_flags(flags)
                
                # Get type information from cache
                type_info = type_cache.get(icao_type.upper(), [None, None, None]) if icao_type else [None, None, None]
                type_long = type_info[0] if len(type_info) > 0 and type_info[0] else None
                type_description = type_info[1] if len(type_info) > 1 and type_info[1] else None
                wake_category = type_info[2] if len(type_info) > 2 and type_info[2] else None
                
                country_code = get_country_from_hex(full_hex_code)
                
                aircraft_record = {
                    'hex': full_hex_code,
                    'registration': registration if registration and registration != 'DEFAULT' else None,
                    'icao_type_designator': icao_type if icao_type else None,
                    'type_description': type_description,
                    'wake_turbulence_category': wake_category,
                    'aircraft_description': description if description else type_long,
                    'is_military': is_military,
                    'is_interesting': is_interesting,
                    'country_code': country_code,
                    'database_source': 'tar1090'
                }
                
                aircraft_records.append(aircraft_record)
                
            except Exception as e:
                logger.warning(f"Error processing aircraft {hex_suffix} in {file_path.name}: {e}")
                continue
            
    except Exception as e:
        logger.error(f"Unexpected error processing file {file_path.name}: {e}")
    
    return aircraft_records

def prepare_optimization(cursor):
    """Prepare database for optimized bulk insert."""
    logger.info("🔧 Preparing database for optimized bulk insert...")

    try:
        # Temporarily drop the registration unique constraint
        logger.info("Dropping registration unique constraint temporarily...")
        cursor.execute("ALTER TABLE aircraft DROP CONSTRAINT IF EXISTS aircraft_registration_key")

        # Drop non-essential indexes temporarily
        logger.info("Dropping non-essential indexes temporarily...")
        cursor.execute("DROP INDEX IF EXISTS ix_aircraft_hex")  # Redundant with primary key

        # Temporarily disable the update trigger to avoid overhead
        logger.info("Disabling update trigger temporarily...")
        cursor.execute("DROP TRIGGER IF EXISTS trg_aircraft_update_timestamp ON aircraft")

        # Set optimized settings for bulk insert
        logger.info("Setting optimized database parameters...")
        cursor.execute("SET maintenance_work_mem = '2GB'")
        cursor.execute("SET work_mem = '512MB'")
        cursor.execute("SET checkpoint_completion_target = 0.9")
        cursor.execute("SET wal_buffers = '64MB'")
        cursor.execute("SET synchronous_commit = off")  # Faster but less durable
        cursor.execute("SET commit_delay = 100000")  # Delay commits to batch them
        cursor.execute("SET commit_siblings = 10")  # Only delay if multiple transactions

        logger.info("✅ Database optimization complete")

    except Exception as e:
        logger.error(f"Error preparing optimization: {e}")

def restore_optimization(cursor):
    """Restore database constraints and indexes after bulk insert."""
    logger.info("🔧 Restoring database constraints and indexes...")

    try:
        # Restore database settings
        cursor.execute("SET synchronous_commit = on")
        cursor.execute("SET commit_delay = 0")
        cursor.execute("SET commit_siblings = 5")

        # Recreate the update trigger
        logger.info("Recreating update trigger...")
        cursor.execute("""
            CREATE TRIGGER trg_aircraft_update_timestamp
            BEFORE UPDATE ON aircraft
            FOR EACH ROW
            EXECUTE FUNCTION update_aircraft_last_updated()
        """)

        # Recreate registration unique constraint (handling duplicates)
        logger.info("Recreating registration unique constraint...")
        cursor.execute("""
            CREATE UNIQUE INDEX aircraft_registration_key
            ON aircraft (registration)
            WHERE registration IS NOT NULL AND registration != ''
        """)

        # Recreate useful indexes (but not redundant ones)
        logger.info("Recreating indexes...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_aircraft_icao_type ON aircraft (icao_type_designator)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_aircraft_operator ON aircraft (operator)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_aircraft_military ON aircraft (is_military)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_aircraft_country ON aircraft (country_code)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_aircraft_wake_category ON aircraft (wake_turbulence_category)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_aircraft_last_updated ON aircraft (last_updated)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_aircraft_database_source ON aircraft (database_source)")

        # Analyze table for updated statistics
        logger.info("Analyzing table for updated statistics...")
        cursor.execute("ANALYZE aircraft")

        logger.info("✅ Database restoration complete")

    except Exception as e:
        logger.error(f"Error restoring optimization: {e}")

def bulk_insert_aircraft(cursor, aircraft_records: list[Dict[str, Any]]) -> int:
    """Insert aircraft records using COPY FROM for maximum performance."""
    if not aircraft_records:
        return 0
    
    try:
        # Prepare data for COPY FROM
        copy_data = io.StringIO()
        
        for record in aircraft_records:
            # Format values for COPY (tab-separated, NULL as \N)
            values = [
                record['hex'],
                record['registration'] or '\\N',
                record['icao_type_designator'] or '\\N',
                record['type_description'] or '\\N',
                record['wake_turbulence_category'] or '\\N',
                record['aircraft_description'] or '\\N',
                '\\N',  # operator (not in TAR1090 data)
                '\\N',  # year_built (not in TAR1090 data)
                str(record['is_military']).lower(),
                str(record['is_interesting']).lower(),
                'false',  # is_pia
                'false',  # is_ladd
                record['country_code'] or '\\N',
                record['database_source'],
                'NOW()',  # last_updated
                'NOW()'   # created_at
            ]
            
            copy_data.write('\t'.join(values) + '\n')
        
        copy_data.seek(0)
        
        # Use COPY FROM for bulk insert
        cursor.copy_from(
            copy_data,
            'aircraft',
            columns=(
                'hex', 'registration', 'icao_type_designator', 'type_description',
                'wake_turbulence_category', 'aircraft_description', 'operator', 'year_built',
                'is_military', 'is_interesting', 'is_pia', 'is_ladd',
                'country_code', 'database_source', 'last_updated', 'created_at'
            )
        )
        
        return len(aircraft_records)
        
    except Exception as e:
        logger.error(f"Error in bulk insert: {e}")
        return 0

def main():
    """Main function to populate aircraft database with optimized performance."""
    if not DATABASE_PATH.exists():
        logger.error(f"Database path does not exist: {DATABASE_PATH}")
        sys.exit(1)
    
    # Load type cache
    logger.info("Loading ICAO aircraft types cache...")
    type_cache = load_type_cache(DATABASE_PATH)
    logger.info(f"Loaded {len(type_cache)} aircraft types")
    
    # Connect to database
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        logger.info("Connected to database")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        sys.exit(1)
    
    try:
        # Prepare database for optimization
        prepare_optimization(cursor)
        conn.commit()
        
        total_processed = 0
        total_inserted = 0
        batch_size = 50000  # Even larger batch size for COPY FROM to minimize round trips
        current_batch = []
        commit_frequency = 200000  # Commit every 200k records to minimize round trips

        # Process all JSON files in the database directory
        json_files = list(DATABASE_PATH.glob('*.json'))
        json_files = [f for f in json_files if f.name != 'icao_aircraft_types.json']

        logger.info(f"Found {len(json_files)} database files to process")
        logger.info(f"Using batch size: {batch_size}")
        logger.info(f"Commit frequency: every {commit_frequency} records")

        last_commit_count = 0

        for i, file_path in enumerate(json_files):
            logger.info(f"Processing file {i+1}/{len(json_files)}: {file_path.name}")

            aircraft_records = process_aircraft_file(file_path, type_cache)

            if aircraft_records:
                logger.info(f"Found {len(aircraft_records)} aircraft in {file_path.name}")
                current_batch.extend(aircraft_records)
                total_processed += len(aircraft_records)

                # Insert batch when it reaches batch_size
                if len(current_batch) >= batch_size:
                    logger.info(f"Bulk inserting batch of {len(current_batch)} aircraft...")
                    inserted = bulk_insert_aircraft(cursor, current_batch)
                    total_inserted += inserted
                    current_batch = []

                    # Only commit periodically to reduce round trips
                    if total_inserted - last_commit_count >= commit_frequency:
                        conn.commit()
                        last_commit_count = total_inserted
                        logger.info(f"💾 Committed at {total_inserted} records")

                    logger.info(f"✅ Inserted {inserted} aircraft. Total: {total_inserted}")

                    # Progress update
                    logger.info(f"📊 Progress: {i+1}/{len(json_files)} files, {total_inserted} aircraft inserted")
            else:
                logger.info(f"No aircraft found in {file_path.name}")

        # Insert remaining records
        if current_batch:
            logger.info(f"Bulk inserting final batch of {len(current_batch)} aircraft...")
            inserted = bulk_insert_aircraft(cursor, current_batch)
            total_inserted += inserted
            logger.info(f"✅ Inserted final batch of {inserted} aircraft")

        # Final commit
        conn.commit()
        logger.info(f"💾 Final commit completed")
        
        # Restore database optimization
        restore_optimization(cursor)
        conn.commit()
        
        logger.info(f"🎉 Processing complete! Processed {total_processed} aircraft, inserted {total_inserted}")
        
    except Exception as e:
        logger.error(f"Error during processing: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    main()
