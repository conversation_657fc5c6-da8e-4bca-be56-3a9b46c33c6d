#!/usr/bin/env python3
"""
Analyze database performance and suggest optimizations.
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection string for TimescaleDB
DATABASE_URL = "postgresql://tsdbadmin:<EMAIL>:34087/tsdb?sslmode=require"

def analyze_indexes():
    """Analyze current indexes on aircraft table."""
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Get all indexes on aircraft table
        cursor.execute("""
            SELECT 
                indexname,
                indexdef,
                tablespace
            FROM pg_indexes 
            WHERE tablename = 'aircraft'
            ORDER BY indexname
        """)
        
        indexes = cursor.fetchall()
        logger.info("Current indexes on aircraft table:")
        for idx in indexes:
            logger.info(f"  - {idx['indexname']}: {idx['indexdef']}")
        
        # Check for constraints
        cursor.execute("""
            SELECT 
                conname,
                contype,
                pg_get_constraintdef(oid) as definition
            FROM pg_constraint 
            WHERE conrelid = 'aircraft'::regclass
            ORDER BY conname
        """)
        
        constraints = cursor.fetchall()
        logger.info("\nConstraints on aircraft table:")
        for con in constraints:
            logger.info(f"  - {con['conname']} ({con['contype']}): {con['definition']}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Error analyzing indexes: {e}")

def check_table_stats():
    """Check table statistics and size."""
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Table size and row count
        cursor.execute("""
            SELECT 
                pg_size_pretty(pg_total_relation_size('aircraft')) as table_size,
                pg_size_pretty(pg_relation_size('aircraft')) as data_size,
                (SELECT COUNT(*) FROM aircraft) as row_count
        """)
        
        stats = cursor.fetchone()
        logger.info(f"\nTable Statistics:")
        logger.info(f"  - Total size: {stats['table_size']}")
        logger.info(f"  - Data size: {stats['data_size']}")
        logger.info(f"  - Row count: {stats['row_count']}")
        
        # Check for bloat
        cursor.execute("""
            SELECT 
                schemaname,
                tablename,
                attname,
                n_distinct,
                correlation
            FROM pg_stats 
            WHERE tablename = 'aircraft'
            ORDER BY attname
        """)
        
        stats = cursor.fetchall()
        logger.info(f"\nColumn Statistics:")
        for stat in stats:
            logger.info(f"  - {stat['attname']}: distinct={stat['n_distinct']}, correlation={stat['correlation']}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Error checking table stats: {e}")

def test_insert_performance():
    """Test different insert strategies."""
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        import time
        
        # Test data
        test_records = []
        for i in range(100):
            test_records.append({
                'hex': f'test{i:03d}',
                'registration': f'N-TEST{i:03d}',
                'icao_type_designator': 'TEST',
                'aircraft_description': 'Test Aircraft',
                'is_military': False,
                'is_interesting': False,
                'country_code': 'US',
                'database_source': 'perf_test'
            })
        
        # Test 1: Individual inserts
        logger.info("Testing individual inserts...")
        start_time = time.time()
        
        for record in test_records[:10]:  # Only test 10 for speed
            cursor.execute("""
                INSERT INTO aircraft (
                    hex, registration, icao_type_designator, aircraft_description,
                    is_military, is_interesting, country_code, database_source,
                    last_updated, created_at
                ) VALUES (
                    %(hex)s, %(registration)s, %(icao_type_designator)s, %(aircraft_description)s,
                    %(is_military)s, %(is_interesting)s, %(country_code)s, %(database_source)s,
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                )
                ON CONFLICT (hex) DO NOTHING
            """, record)
        
        conn.commit()
        individual_time = time.time() - start_time
        logger.info(f"Individual inserts (10 records): {individual_time:.2f} seconds")
        
        # Test 2: Batch insert with executemany
        logger.info("Testing batch insert with executemany...")
        start_time = time.time()
        
        # Update hex codes to avoid conflicts
        batch_records = []
        for i, record in enumerate(test_records[10:20]):
            record['hex'] = f'batch{i:03d}'
            batch_records.append(record)
        
        cursor.executemany("""
            INSERT INTO aircraft (
                hex, registration, icao_type_designator, aircraft_description,
                is_military, is_interesting, country_code, database_source,
                last_updated, created_at
            ) VALUES (
                %(hex)s, %(registration)s, %(icao_type_designator)s, %(aircraft_description)s,
                %(is_military)s, %(is_interesting)s, %(country_code)s, %(database_source)s,
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            )
            ON CONFLICT (hex) DO NOTHING
        """, batch_records)
        
        conn.commit()
        batch_time = time.time() - start_time
        logger.info(f"Batch insert (10 records): {batch_time:.2f} seconds")
        
        # Test 3: COPY FROM (fastest for bulk inserts)
        logger.info("Testing COPY FROM...")
        start_time = time.time()
        
        # Prepare data for COPY
        import io
        copy_data = io.StringIO()
        for i, record in enumerate(test_records[20:30]):
            record['hex'] = f'copy{i:03d}'
            copy_data.write(f"{record['hex']}\t{record['registration']}\t{record['icao_type_designator']}\t\\N\t\\N\t{record['aircraft_description']}\t\\N\t\\N\t{record['is_military']}\t{record['is_interesting']}\t\\N\t\\N\t{record['country_code']}\t{record['database_source']}\t\\N\t\\N\n")
        
        copy_data.seek(0)
        cursor.copy_from(
            copy_data, 
            'aircraft',
            columns=('hex', 'registration', 'icao_type_designator', 'type_description', 'wake_turbulence_category', 'aircraft_description', 'operator', 'year_built', 'is_military', 'is_interesting', 'is_pia', 'is_ladd', 'country_code', 'database_source', 'last_updated', 'created_at')
        )
        
        conn.commit()
        copy_time = time.time() - start_time
        logger.info(f"COPY FROM (10 records): {copy_time:.2f} seconds")
        
        # Performance comparison
        logger.info(f"\nPerformance Comparison (per record):")
        logger.info(f"  - Individual: {individual_time/10:.3f} seconds/record")
        logger.info(f"  - Batch: {batch_time/10:.3f} seconds/record")
        logger.info(f"  - COPY: {copy_time/10:.3f} seconds/record")
        
        # Cleanup
        cursor.execute("DELETE FROM aircraft WHERE database_source = 'perf_test'")
        conn.commit()
        logger.info("Cleaned up test data")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Error testing insert performance: {e}")

def suggest_optimizations():
    """Suggest performance optimizations."""
    logger.info("\n" + "="*60)
    logger.info("PERFORMANCE OPTIMIZATION SUGGESTIONS")
    logger.info("="*60)
    
    logger.info("\n1. INDEXING OPTIMIZATIONS:")
    logger.info("   - The unique constraint on registration is causing conflicts")
    logger.info("   - Consider dropping registration unique constraint temporarily")
    logger.info("   - Add indexes after bulk insert is complete")
    
    logger.info("\n2. BULK INSERT STRATEGIES:")
    logger.info("   - Use COPY FROM for maximum performance")
    logger.info("   - Use larger batch sizes (1000-10000 records)")
    logger.info("   - Disable autocommit and commit less frequently")
    
    logger.info("\n3. DATABASE SETTINGS:")
    logger.info("   - Increase work_mem for better sort performance")
    logger.info("   - Temporarily disable triggers if any")
    logger.info("   - Consider UNLOGGED tables for initial load")
    
    logger.info("\n4. CONNECTION OPTIMIZATIONS:")
    logger.info("   - Use connection pooling")
    logger.info("   - Increase network buffer sizes")
    logger.info("   - Use prepared statements for repeated queries")

def main():
    """Run performance analysis."""
    logger.info("🔍 Analyzing database performance...")
    
    analyze_indexes()
    check_table_stats()
    test_insert_performance()
    suggest_optimizations()

if __name__ == "__main__":
    main()
