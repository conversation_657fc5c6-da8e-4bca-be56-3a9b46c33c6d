#!/usr/bin/env python3
"""
Test script for airport image functionality.
This script tests the complete workflow of airport image management.
"""

import asyncio
import aiohttp
import json
from pathlib import Path

# Configuration
API_BASE_URL = "https://api.skytraces.com/api/v1"
TEST_ICAO = "4FL5"

async def test_airport_endpoint():
    """Test that the airport endpoint returns image_url field."""
    print("🔍 Testing airport endpoint...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{API_BASE_URL}/airports/{TEST_ICAO}") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Airport endpoint working")
                    print(f"   ICAO: {data.get('icao')}")
                    print(f"   Name: {data.get('name')}")
                    print(f"   Image URL: {data.get('image_url', 'None')}")
                    return True
                else:
                    print(f"❌ Airport endpoint failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Airport endpoint error: {e}")
            return False

async def test_image_info_endpoint():
    """Test the image info endpoint."""
    print("\n🔍 Testing image info endpoint...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{API_BASE_URL}/airports/{TEST_ICAO}/image/info") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Image info endpoint working")
                    print(f"   ICAO: {data.get('icao')}")
                    print(f"   Has Image: {data.get('has_image')}")
                    print(f"   Image URL: {data.get('image_url', 'None')}")
                    return True
                else:
                    print(f"❌ Image info endpoint failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Image info endpoint error: {e}")
            return False

async def test_upload_endpoint():
    """Test the image upload endpoint with a sample image."""
    print("\n🔍 Testing image upload endpoint...")
    print("   Note: This requires a valid image file and B2 credentials")
    
    # Create a simple test image (1x1 pixel PNG)
    test_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\x12IDATx\x9cc```bPPP\x00\x02\xd2\x00\x00\x00\x05\x00\x01\r\n\x87\xdc\x00\x00\x00\x00IEND\xaeB`\x82'
    
    async with aiohttp.ClientSession() as session:
        try:
            # Create form data
            data = aiohttp.FormData()
            data.add_field('file', test_image_data, filename='test.png', content_type='image/png')
            
            async with session.post(f"{API_BASE_URL}/airports/{TEST_ICAO}/image", data=data) as response:
                response_text = await response.text()
                if response.status == 200:
                    data = json.loads(response_text)
                    print(f"✅ Image upload endpoint working")
                    print(f"   Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                    print(f"   Image URL: {data.get('image_url', 'None')}")
                    return True
                else:
                    print(f"❌ Image upload failed: {response.status}")
                    print(f"   Response: {response_text}")
                    return False
        except Exception as e:
            print(f"❌ Image upload error: {e}")
            return False

async def test_delete_endpoint():
    """Test the image delete endpoint."""
    print("\n🔍 Testing image delete endpoint...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.delete(f"{API_BASE_URL}/airports/{TEST_ICAO}/image") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Image delete endpoint working")
                    print(f"   Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                    return True
                else:
                    print(f"❌ Image delete failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Image delete error: {e}")
            return False

async def main():
    """Run all tests."""
    print("🚀 Testing Airport Image Implementation")
    print("=" * 50)
    
    tests = [
        test_airport_endpoint,
        test_image_info_endpoint,
        test_upload_endpoint,
        test_delete_endpoint
    ]
    
    results = []
    for test in tests:
        result = await test()
        results.append(result)
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    passed = sum(results)
    total = len(results)
    print(f"   Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    print("\n📝 Next Steps:")
    print("1. Run the database migration: scripts/database/add_missing_airport_columns.sql")
    print("2. Set B2 credentials in your environment variables")
    print("3. Test the admin interface at /admin")
    print("4. Upload an image for 4FL5 airport")
    print("5. Check the airport page to see the image")

if __name__ == "__main__":
    asyncio.run(main())
