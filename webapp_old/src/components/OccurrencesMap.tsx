'use client';

import { useEffect, useRef, useState } from 'react';
import dynamic from 'next/dynamic';
import { MoreVertical } from 'lucide-react';
import { FlightTraceResponse } from '../types/api';

// Create the map component with all react-leaflet imports
const DynamicMapComponent = dynamic(
  () => import('./MapContent'),
  { 
    ssr: false,
    loading: () => (
      <div className="w-full h-full flex items-center justify-center bg-muted/10">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading map...</p>
        </div>
      </div>
    )
  }
);

interface MapLayer {
  id: string;
  name: string;
  url: string;
  attribution: string;
  maxZoom: number;
}

// Aviation-focused map layers
const MAP_LAYERS: MapLayer[] = [
  {
    id: 'satellite',
    name: 'Satellite',
    url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
    attribution: '© Esri',
    maxZoom: 19,
  },
  {
    id: 'osm',
    name: 'OpenStreetMap',
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '© OpenStreetMap contributors',
    maxZoom: 19,
  },
  {
    id: 'terrain',
    name: 'Terrain',
    url: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png',
    attribution: '© OpenTopoMap contributors',
    maxZoom: 17,
  },
];

interface MapControlsProps {
  currentLayer: string;
  onLayerChange: (layerId: string) => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetView: () => void;
}

function MapControls({ 
  currentLayer, 
  onLayerChange, 
  onZoomIn, 
  onZoomOut, 
  onResetView
}: MapControlsProps) {
  const [isLayerMenuOpen, setIsLayerMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsLayerMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const currentLayerName = MAP_LAYERS.find(layer => layer.id === currentLayer)?.name || 'Satellite';

  return (
    <div className="absolute bottom-4 right-4 z-[1000]">
      {/* Layer selection dropdown */}
      {isLayerMenuOpen && (
        <div ref={menuRef} className="absolute bottom-full right-0 mb-2 bg-card border rounded-lg shadow-lg p-2 min-w-40">
          <div className="text-xs font-semibold text-muted-foreground mb-2 px-1">Map Layers</div>
          <div className="space-y-1">
            {MAP_LAYERS.map((layer) => (
              <button
                key={layer.id}
                onClick={() => {
                  onLayerChange(layer.id);
                  setIsLayerMenuOpen(false);
                }}
                className={`w-full text-left px-2 py-1.5 text-sm rounded transition-colors ${
                  currentLayer === layer.id
                    ? 'bg-primary text-primary-foreground'
                    : 'hover:bg-muted text-muted-foreground hover:text-foreground'
                }`}
              >
                {layer.name}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Compact control panel */}
      <div className="bg-card border rounded-lg shadow-lg">
        <div className="flex flex-col">
          {/* Zoom In */}
          <button
            onClick={onZoomIn}
            className="px-3 py-2 text-lg font-bold hover:bg-muted transition-colors border-b rounded-t-lg"
            title="Zoom In"
          >
            +
          </button>
          
          {/* Zoom Out */}
          <button
            onClick={onZoomOut}
            className="px-3 py-2 text-lg font-bold hover:bg-muted transition-colors border-b"
            title="Zoom Out"
          >
            −
          </button>
          
          {/* Reset View */}
          <button
            onClick={onResetView}
            className="px-3 py-1.5 text-sm font-semibold hover:bg-muted transition-colors border-b"
            title="Reset View"
          >
            ⌂
          </button>
          
          {/* Layer Menu Toggle */}
          <button
            onClick={() => setIsLayerMenuOpen(!isLayerMenuOpen)}
            className={`px-3 py-2 hover:bg-muted transition-colors rounded-b-lg flex items-center justify-center ${
              isLayerMenuOpen ? 'bg-muted' : ''
            }`}
            title={`Current: ${currentLayerName}`}
          >
            <MoreVertical className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}

interface OccurrencesMapProps {
  icao: string;
  className?: string;
  drawingMode?: boolean;
  onPolygonPointAdded?: (points: [number, number][]) => void;
  onPolygonPointDragged?: (index: number, newPosition: [number, number]) => void;
  existingPolygon?: [number, number][] | null;
  currentPoints?: [number, number][];
  radiusMode?: boolean;
  onMapClick?: (lat: number, lng: number) => void;
  radiusCenter?: [number, number] | null;
  radiusDistance?: number;
  flightTrace?: FlightTraceResponse | null;
  showFlightTrace?: boolean;
  hoveredTimestamp?: string | null;
}

function OccurrencesMapComponent({ 
  icao,
  className = '',
  drawingMode = false,
  onPolygonPointAdded,
  onPolygonPointDragged,
  existingPolygon,
  currentPoints = [],
  radiusMode = false,
  onMapClick,
  radiusCenter,
  radiusDistance,
  flightTrace,
  showFlightTrace = true,
  hoveredTimestamp
}: OccurrencesMapProps) {
  const [currentLayer, setCurrentLayer] = useState('satellite');
  
  // Use refs to avoid recreating functions on every render
  const zoomInRef = useRef<() => void>(() => {});
  const zoomOutRef = useRef<() => void>(() => {});
  const resetViewRef = useRef<() => void>(() => {});

  const currentLayerConfig = MAP_LAYERS.find(layer => layer.id === currentLayer) || MAP_LAYERS[0];

  return (
    <div className={`w-full h-full relative ${className}`}>
      <DynamicMapComponent
        icao={icao}
        currentLayerConfig={currentLayerConfig}
        onZoomIn={zoomInRef}
        onZoomOut={zoomOutRef}
        onResetView={resetViewRef}
        drawingMode={drawingMode}
        onPolygonPointAdded={onPolygonPointAdded}
        onPolygonPointDragged={onPolygonPointDragged}
        existingPolygon={existingPolygon}
        currentPoints={currentPoints}
        radiusMode={radiusMode}
        onMapClick={onMapClick}
        radiusCenter={radiusCenter}
        radiusDistance={radiusDistance}
        flightTrace={flightTrace}
        showFlightTrace={showFlightTrace}
        hoveredTimestamp={hoveredTimestamp}
      />
      
      <MapControls
        currentLayer={currentLayer}
        onLayerChange={setCurrentLayer}
        onZoomIn={() => zoomInRef.current()}
        onZoomOut={() => zoomOutRef.current()}
        onResetView={() => resetViewRef.current()}
      />
    </div>
  );
}

export default function OccurrencesMap(props: OccurrencesMapProps) {
  return <OccurrencesMapComponent {...props} />;
} 