'use client';

import { use<PERSON><PERSON>back, useMemo, useRef, useEffect } from 'react';
import { Navigation } from 'lucide-react';

interface HeadingCompassProps {
  startHeading: number;
  endHeading: number;
  onHeadingChange: (start: number, end: number) => void;
  className?: string;
}

export default function HeadingCompass({ 
  startHeading, 
  endHeading, 
  onHeadingChange, 
  className = '' 
}: HeadingCompassProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const startSliderRef = useRef<SVGCircleElement>(null);
  const endSliderRef = useRef<SVGCircleElement>(null);
  const arcRef = useRef<SVGPathElement>(null);
  const sectorRef = useRef<SVGPathElement>(null);
  
  // Track drag state without causing re-renders
  const dragStateRef = useRef<{
    isDragging: boolean;
    mode: 'start' | 'end' | null;
    currentStartHeading: number;
    currentEndHeading: number;
    animationFrameId: number | null;
  }>({
    isDragging: false,
    mode: null,
    currentStartHeading: startHeading,
    currentEndHeading: endHeading,
    animationFrameId: null
  });

  // Update internal state when props change
  useEffect(() => {
    if (!dragStateRef.current.isDragging) {
      dragStateRef.current.currentStartHeading = startHeading;
      dragStateRef.current.currentEndHeading = endHeading;
    }
  }, [startHeading, endHeading]);

  // Convert heading to angle (0° = North, clockwise)
  const headingToAngle = (heading: number) => (heading - 90) * (Math.PI / 180);

  // Convert angle to heading
  const angleToHeading = (angle: number) => {
    let heading = (angle * (180 / Math.PI)) + 90;
    if (heading < 0) heading += 360;
    if (heading >= 360) heading -= 360;
    return Math.round(heading);
  };

  // Calculate position on circle
  const getPositionOnCircle = useCallback((heading: number, radius: number) => {
    const angle = headingToAngle(heading);
    return {
      x: Math.cos(angle) * radius,
      y: Math.sin(angle) * radius
    };
  }, []);

  // Get heading from mouse position
  const getHeadingFromMousePosition = useCallback((clientX: number, clientY: number) => {
    if (!svgRef.current) return null;
    
    const rect = svgRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const x = clientX - centerX;
    const y = clientY - centerY;
    
    const angle = Math.atan2(y, x);
    return angleToHeading(angle);
  }, []);

  // Calculate paths
  const calculatePaths = useCallback((startHdg: number, endHdg: number) => {
    const radius = 80;
    const centerX = 100;
    const centerY = 100;

    const startPos = getPositionOnCircle(startHdg, radius);
    const endPos = getPositionOnCircle(endHdg, radius);

    const startX = centerX + startPos.x;
    const startY = centerY + startPos.y;
    const endX = centerX + endPos.x;
    const endY = centerY + endPos.y;

    let angleDiff = endHdg - startHdg;
    if (angleDiff < 0) angleDiff += 360;
    const largeArcFlag = angleDiff > 180 ? 1 : 0;

    const arcPath = `M ${startX} ${startY} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${endX} ${endY}`;
    const sectorPath = `M ${centerX} ${centerY} L ${startX} ${startY} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${endX} ${endY} Z`;

    return { arcPath, sectorPath, startPos, endPos };
  }, [getPositionOnCircle]);

  // Update visual elements directly
  const updateVisuals = useCallback((startHdg: number, endHdg: number) => {
    const { arcPath, sectorPath, startPos, endPos } = calculatePaths(startHdg, endHdg);
    
    // Update DOM elements directly
    if (arcRef.current) {
      arcRef.current.setAttribute('d', arcPath);
    }
    if (sectorRef.current) {
      sectorRef.current.setAttribute('d', sectorPath);
    }
    if (startSliderRef.current) {
      startSliderRef.current.setAttribute('cx', (100 + startPos.x).toString());
      startSliderRef.current.setAttribute('cy', (100 + startPos.y).toString());
    }
    if (endSliderRef.current) {
      endSliderRef.current.setAttribute('cx', (100 + endPos.x).toString());
      endSliderRef.current.setAttribute('cy', (100 + endPos.y).toString());
    }
  }, [calculatePaths]);

  // Throttled state update
  const updateReactState = useCallback(() => {
    const { currentStartHeading, currentEndHeading } = dragStateRef.current;
    if (currentStartHeading !== startHeading || currentEndHeading !== endHeading) {
      onHeadingChange(currentStartHeading, currentEndHeading);
    }
  }, [startHeading, endHeading, onHeadingChange]);

  // Mouse move handler with requestAnimationFrame
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!dragStateRef.current.isDragging) return;
    
    const newHeading = getHeadingFromMousePosition(event.clientX, event.clientY);
    if (newHeading === null) return;

    const dragState = dragStateRef.current;
    
    // Update internal state
    if (dragState.mode === 'start') {
      dragState.currentStartHeading = newHeading;
    } else if (dragState.mode === 'end') {
      dragState.currentEndHeading = newHeading;
    }

    // Cancel previous animation frame if any
    if (dragState.animationFrameId) {
      cancelAnimationFrame(dragState.animationFrameId);
    }

    // Schedule visual update
    dragState.animationFrameId = requestAnimationFrame(() => {
      updateVisuals(dragState.currentStartHeading, dragState.currentEndHeading);
      dragState.animationFrameId = null;
    });
  }, [getHeadingFromMousePosition, updateVisuals]);

  // Mouse up handler
  const handleMouseUp = useCallback(() => {
    const dragState = dragStateRef.current;
    
    if (dragState.isDragging) {
      dragState.isDragging = false;
      dragState.mode = null;
      
      // Final state update
      updateReactState();
      
      // Clean up
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
      
      if (dragState.animationFrameId) {
        cancelAnimationFrame(dragState.animationFrameId);
        dragState.animationFrameId = null;
      }
    }
  }, [handleMouseMove, updateReactState]);

  // Mouse down handler
  const handleMouseDown = useCallback((mode: 'start' | 'end', event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    
    const dragState = dragStateRef.current;
    dragState.isDragging = true;
    dragState.mode = mode;
    
    // Add global event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = 'grabbing';
    document.body.style.userSelect = 'none';
  }, [handleMouseMove, handleMouseUp]);

  // Initial paths calculation
  const { arcPath, sectorPath, startPos, endPos } = useMemo(() => 
    calculatePaths(startHeading, endHeading), 
    [startHeading, endHeading, calculatePaths]
  );

  // Format heading for display
  const formatHeading = (heading: number) => {
    return `${heading.toString().padStart(3, '0')}°`;
  };

  const centerX = 100;
  const centerY = 100;
  const radius = 80;
  const sliderRadius = 8;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Heading Display */}
      <div className="flex justify-between items-center">
        <div className="text-center">
          <div className="text-sm font-medium text-muted-foreground">From</div>
          <div className="text-lg font-mono font-bold text-blue-600">{formatHeading(startHeading)}</div>
        </div>
        <Navigation className="h-5 w-5 text-primary" />
        <div className="text-center">
          <div className="text-sm font-medium text-muted-foreground">To</div>
          <div className="text-lg font-mono font-bold text-orange-600">{formatHeading(endHeading)}</div>
        </div>
      </div>

      {/* Compass Circle */}
      <div className="flex justify-center">
        <svg
          ref={svgRef}
          width="200"
          height="200"
          viewBox="0 0 200 200"
          className="select-none"
        >
          {/* Background circle */}
          <circle
            cx={centerX}
            cy={centerY}
            r={radius}
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            className="text-muted-foreground/30"
          />

          {/* Shaded sector for selected range */}
          <path
            ref={sectorRef}
            d={sectorPath}
            fill="rgb(59 130 246 / 0.15)"
            stroke="none"
          />

          {/* Cardinal directions */}
          {[
            { label: 'N', heading: 0 },
            { label: 'E', heading: 90 },
            { label: 'S', heading: 180 },
            { label: 'W', heading: 270 }
          ].map(({ label, heading }) => {
            const pos = getPositionOnCircle(heading, radius + 15);
            return (
              <text
                key={label}
                x={centerX + pos.x}
                y={centerY + pos.y}
                textAnchor="middle"
                dominantBaseline="middle"
                className="text-sm font-bold fill-current text-muted-foreground pointer-events-none"
              >
                {label}
              </text>
            );
          })}

          {/* Degree markings */}
          {Array.from({ length: 36 }, (_, i) => i * 10).map((heading) => {
            const isMain = heading % 90 === 0;
            const pos = getPositionOnCircle(heading, radius);
            const outerPos = getPositionOnCircle(heading, radius + (isMain ? 8 : 4));
            
            return (
              <line
                key={heading}
                x1={centerX + pos.x}
                y1={centerY + pos.y}
                x2={centerX + outerPos.x}
                y2={centerY + outerPos.y}
                stroke="currentColor"
                strokeWidth={isMain ? "2" : "1"}
                className="text-muted-foreground/50 pointer-events-none"
              />
            );
          })}

          {/* Selected range arc stroke */}
          <path
            ref={arcRef}
            d={arcPath}
            fill="none"
            stroke="rgb(59 130 246)"
            strokeWidth="4"
            className="pointer-events-none"
          />

          {/* Start heading slider */}
          <circle
            ref={startSliderRef}
            cx={centerX + startPos.x}
            cy={centerY + startPos.y}
            r={sliderRadius}
            fill="rgb(59 130 246)"
            stroke="white"
            strokeWidth="3"
            className="cursor-grab drop-shadow-md"
            style={{ filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))' }}
            onMouseDown={(e) => handleMouseDown('start', e)}
          />

          {/* End heading slider */}
          <circle
            ref={endSliderRef}
            cx={centerX + endPos.x}
            cy={centerY + endPos.y}
            r={sliderRadius}
            fill="rgb(234 88 12)"
            stroke="white"
            strokeWidth="3"
            className="cursor-grab drop-shadow-md"
            style={{ filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))' }}
            onMouseDown={(e) => handleMouseDown('end', e)}
          />

          {/* Center dot */}
          <circle
            cx={centerX}
            cy={centerY}
            r="3"
            fill="currentColor"
            className="text-muted-foreground pointer-events-none"
          />
        </svg>
      </div>

      {/* Instructions */}
      <div className="text-center">
        <p className="text-xs text-muted-foreground">
          Drag the <span className="text-blue-600 font-medium">blue dot</span> to set start heading, 
          <span className="text-orange-600 font-medium"> orange dot</span> to set end heading
        </p>
      </div>
    </div>
  );
} 