'use client';

import { useCallback, useMemo } from 'react';
import { Calendar, MapPin, Mountain, Navigation, Plane } from 'lucide-react';
import { Button } from '@/components/ui/button';
import HeadingCompass from './HeadingCompass';
import { 
  TimeRangeFilter,
  AltitudeFilter, 
  SearchAreaFilter,
  AircraftDetailsFilter,
  FilterSection 
} from './filters';

interface SearchFilters {
  dateTimeFrom: string;
  dateTimeTo: string;
  altitudeFilterMode: 'groundAirborne' | 'flightEvents';
  includeGroundAircraft: boolean;
  includeAirborneAircraft: boolean;
  minAltitude: number;
  maxAltitude: number;
  flightEvents: {
    landing: boolean;
    takeoff: boolean;
    goAround: boolean;
    touchAndGo: boolean;
  };
  radiusCenter: [number, number] | null;
  radiusDistance: number;
  headingStart: number;
  headingEnd: number;
  occurrenceType: string;
  aircraftClass: string;
  registration: string;
  squawk: string;
}

type SectionKey = 'timeRange' | 'searchArea' | 'altitude' | 'heading' | 'typeClassReg';

interface OccurrenceSearchFiltersProps {
  filters: SearchFilters;
  enabledFilters: Set<SectionKey>;
  openSection: SectionKey | null;
  searchMethod: 'radius' | 'draw';
  drawnPolygon: [number, number][] | null;
  isSearching: boolean;
  onFiltersChange: (updates: Partial<SearchFilters>) => void;
  onEnabledFiltersChange: (filterId: SectionKey) => void;
  onOpenSectionChange: (sectionId: SectionKey) => void;
  onSearchMethodChange: (method: 'radius' | 'draw') => void;
  onDeletePolygon: () => void;
  onSetEndToNow: () => void;
  onHeadingChange: (start: number, end: number) => void;
  onSearch: () => void;
  isRadiusConfirmed?: boolean;
  onModifyRadius?: () => void;
  isSearchAreaValid?: boolean;
}

export default function OccurrenceSearchFilters({
  filters,
  enabledFilters,
  openSection,
  searchMethod,
  drawnPolygon,
  isSearching,
  onFiltersChange,
  onEnabledFiltersChange,
  onOpenSectionChange,
  onSearchMethodChange,
  onDeletePolygon,
  onSetEndToNow,
  onHeadingChange,
  onSearch,
  isRadiusConfirmed,
  onModifyRadius,
  isSearchAreaValid,
}: OccurrenceSearchFiltersProps) {
  
  // Filter configuration
  const filterConfigs = useMemo(() => [
    {
      id: 'timeRange' as SectionKey,
      title: 'Time Range',
      icon: Calendar,
      required: true,
      isValid: (filters: SearchFilters) => Boolean(filters.dateTimeFrom && filters.dateTimeTo),
    },
    {
      id: 'searchArea' as SectionKey,
      title: 'Search Area',
      icon: MapPin,
      required: true,
      isValid: () => Boolean(isSearchAreaValid),
    },
    {
      id: 'altitude' as SectionKey,
      title: 'Altitude',
      icon: Mountain,
      required: false,
      isValid: () => true,
    },
    {
      id: 'heading' as SectionKey,
      title: 'Heading',
      icon: Navigation,
      required: false,
      isValid: () => true,
    },
    {
      id: 'typeClassReg' as SectionKey,
      title: 'Type, Class, Registration & Squawk',
      icon: Plane,
      required: false,
      isValid: (filters: SearchFilters) => Boolean(filters.occurrenceType || filters.aircraftClass || filters.registration || filters.squawk),
    }
  ], [isSearchAreaValid]);

  const requiredFilters = filterConfigs.filter(config => config.required);
  const isFormValid = requiredFilters.every(config => config.isValid(filters));

  const toggleSection = useCallback((sectionId: SectionKey) => {
    onOpenSectionChange(sectionId);
  }, [onOpenSectionChange]);

  return (
    <div className="p-3 space-y-2">
      {/* Time Range Filter */}
      <FilterSection
        id="timeRange"
        title="Time Range"
        icon={Calendar}
        isRequired={true}
        isEnabled={true}
        isValid={Boolean(filters.dateTimeFrom && filters.dateTimeTo)}
        isOpen={openSection === 'timeRange'}
        onToggleSection={() => toggleSection('timeRange')}
        onToggleEnabled={() => {}}
      >
        <TimeRangeFilter
          dateTimeFrom={filters.dateTimeFrom}
          dateTimeTo={filters.dateTimeTo}
          onChange={onFiltersChange}
          onSetToNow={onSetEndToNow}
        />
      </FilterSection>

      {/* Search Area Filter */}
      <FilterSection
        id="searchArea"
        title="Search Area"
        icon={MapPin}
        isRequired={true}
        isEnabled={true}
        isValid={Boolean(isSearchAreaValid)}
        isOpen={openSection === 'searchArea'}
        onToggleSection={() => toggleSection('searchArea')}
        onToggleEnabled={() => {}}
      >
        <SearchAreaFilter
          searchMethod={searchMethod}
          radiusCenter={filters.radiusCenter}
          radiusDistance={filters.radiusDistance}
          drawnPolygon={drawnPolygon}
          onSearchMethodChange={onSearchMethodChange}
          onDeletePolygon={onDeletePolygon}
          onModifyRadius={onModifyRadius}
          isRadiusConfirmed={isRadiusConfirmed}
        />
      </FilterSection>

      {/* Altitude Filter */}
      <FilterSection
        id="altitude"
        title="Altitude"
        icon={Mountain}
        isRequired={false}
        isEnabled={enabledFilters.has('altitude')}
        isValid={true}
        isOpen={openSection === 'altitude'}
        onToggleSection={() => toggleSection('altitude')}
        onToggleEnabled={() => onEnabledFiltersChange('altitude')}
      >
        <AltitudeFilter
          altitudeFilterMode={filters.altitudeFilterMode}
          includeGroundAircraft={filters.includeGroundAircraft}
          includeAirborneAircraft={filters.includeAirborneAircraft}
          minAltitude={filters.minAltitude}
          maxAltitude={filters.maxAltitude}
          flightEvents={filters.flightEvents}
          onChange={onFiltersChange}
        />
      </FilterSection>

      {/* Heading Filter */}
      <FilterSection
        id="heading"
        title="Heading"
        icon={Navigation}
        isRequired={false}
        isEnabled={enabledFilters.has('heading')}
        isValid={true}
        isOpen={openSection === 'heading'}
        onToggleSection={() => toggleSection('heading')}
        onToggleEnabled={() => onEnabledFiltersChange('heading')}
      >
        <HeadingCompass
          startHeading={filters.headingStart}
          endHeading={filters.headingEnd}
          onHeadingChange={onHeadingChange}
        />
      </FilterSection>

      {/* Aircraft Details Filter */}
      <FilterSection
        id="typeClassReg"
        title="Type, Class, Registration & Squawk"
        icon={Plane}
        isRequired={false}
        isEnabled={enabledFilters.has('typeClassReg')}
        isValid={Boolean(filters.occurrenceType || filters.aircraftClass || filters.registration || filters.squawk)}
        isOpen={openSection === 'typeClassReg'}
        onToggleSection={() => toggleSection('typeClassReg')}
        onToggleEnabled={() => onEnabledFiltersChange('typeClassReg')}
      >
        <AircraftDetailsFilter
          occurrenceType={filters.occurrenceType}
          aircraftClass={filters.aircraftClass}
          registration={filters.registration}
          squawk={filters.squawk}
          onChange={onFiltersChange}
        />
      </FilterSection>

      <div className="pt-2">
        <Button
          onClick={onSearch}
          disabled={!isFormValid || isSearching}
          className="w-full"
          size="lg"
        >
          {isSearching ? 'Searching...' : 'Search Occurrences'}
        </Button>
      </div>
    </div>
  );
}

export type { SearchFilters, SectionKey }; 