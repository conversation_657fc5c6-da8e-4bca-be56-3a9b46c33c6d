# Layout Components

## AppNavbar

A reusable navigation bar component that provides consistent branding and navigation across the webapp.

### Features
- Consistent SkyTraces branding with plane icon
- Optional airport information display
- Optional live data update indicator
- Optional custom title/subtitle
- Responsive design

### Usage
```tsx
import AppNavbar from '../components/AppNavbar';

<AppNavbar
  // For airport pages
  airport={airport}
  airportLoading={loading}
  airportError={error}
  icao="KJFK"
  
  // For live data
  lastUpdated={new Date()}
  isLoading={false}
  
  // For custom pages
  title="Custom Page"
  subtitle="Page description"
/>
```

## AppLayout

A complete layout wrapper that includes the navbar and provides proper full-height layout.

### Features
- Full-height screen layout
- Integrated AppNavbar
- Flex-based content area that takes remaining height
- Prevents bottom spacing issues

### Usage
```tsx
import AppLayout from '../components/AppLayout';

<AppLayout
  icao="KJFK"
  title="Page Title"
  subtitle="Page description"
>
  <YourPageContent />
</AppLayout>
```

## Benefits

1. **Consistent UI**: All pages have the same navbar and layout
2. **No spacing issues**: Proper full-height layout prevents bottom white space
3. **Reusable**: Easy to add to new pages
4. **Type-safe**: Full TypeScript support
5. **Flexible**: Optional props for different use cases 