'use client';

import { useMemo, useEffect, useState, useRef } from 'react';
import { useMap, useMapEvents } from 'react-leaflet';
import { createPortal } from 'react-dom';
import { ChevronUp, ChevronDown } from 'lucide-react';
import { AircraftPositionResponse, FlightPhaseEnum } from '../types/api';
import { getLatLngFromH3 } from '../lib/utils/coordinates';
import { formatAltitude } from '../lib/utils/formatting';
import { UI_CONFIG } from '../lib/constants';
import { getAltitudeZIndex } from '../lib/aircraft-config';

interface AircraftLabelsProps {
  positions: AircraftPositionResponse[];
}

interface LabelData {
  hex: string;
  flight: string;
  altitude: string;
  isClimbing: boolean;
  isDescending: boolean;
  position: [number, number];
}

export function AircraftLabels({ positions }: AircraftLabelsProps) {
  const map = useMap();
  const [mapContainer, setMapContainer] = useState<HTMLElement | null>(null);
  const [mapVersion, setMapVersion] = useState(0);
  const [isMapMoving, setIsMapMoving] = useState(false);
  const previousPositionsRef = useRef<Map<string, [number, number]>>(new Map());
  
  // Get map container element
  useEffect(() => {
    if (map) {
      setMapContainer(map.getContainer());
    }
  }, [map]);
  
  // Track map movement to disable transitions during zoom/pan
  useMapEvents({
    movestart: () => setIsMapMoving(true),
    moveend: () => {
      setIsMapMoving(false);
      setMapVersion(prev => prev + 1);
    },
    zoomstart: () => setIsMapMoving(true),
    zoomend: () => {
      setIsMapMoving(false);
      setMapVersion(prev => prev + 1);
    },
    viewreset: () => {
      setIsMapMoving(false);
      setMapVersion(prev => prev + 1);
    },
  });
  
  // Calculate label data - simplified without collision detection
  const labelData = useMemo(() => {
    if (!map) return [];
    
    // Filter to only airborne aircraft with flight numbers
    const airborneAircraft = positions.filter(position => 
      position.flight_phase !== FlightPhaseEnum.GROUND && 
      position.flight &&
      position.flight.trim() !== ''
    );
    
    const labels: LabelData[] = [];
    
    for (const aircraft of airborneAircraft) {
      const latLng = getLatLngFromH3(aircraft.h3_index);
      if (!latLng) continue;
      
      const [lat, lng] = latLng;
      
      // Determine climb/descent status
      const verticalRate = aircraft.baro_rate || 0;
      const isClimbing = verticalRate > UI_CONFIG.VERTICAL_RATE_THRESHOLD;
      const isDescending = verticalRate < -UI_CONFIG.VERTICAL_RATE_THRESHOLD;
      
      labels.push({
        hex: aircraft.hex,
        flight: aircraft.flight || aircraft.hex,
        altitude: formatAltitude(aircraft.alt_baro),
        isClimbing,
        isDescending,
        position: [lat, lng],
      });
    }
    
    return labels;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [positions, map, mapVersion]); // mapVersion is intentionally included to trigger recalculation on zoom
  
  // Update previous positions for transition detection
  useEffect(() => {
    const currentPositions = new Map();
    labelData.forEach(label => {
      currentPositions.set(label.hex, label.position);
    });
    previousPositionsRef.current = currentPositions;
  }, [labelData]);
  
  if (!mapContainer || labelData.length === 0) return null;
  
  // Render labels as portal to map container
  return createPortal(
    <>
      {labelData.map(label => {
        // Simple consistent positioning - always to the right of aircraft
        const currentScreenPos = map.latLngToContainerPoint(label.position);
        const labelOffset = 35; // Simple fixed offset to the right
        
        // Calculate z-index based on altitude for proper layering
        const aircraft = positions.find(p => p.hex === label.hex);
        const isGround = aircraft?.flight_phase === FlightPhaseEnum.GROUND;
        const zIndex = getAltitudeZIndex(aircraft?.alt_baro, isGround);
        
        // Check if this aircraft position has changed (for smooth transitions)
        const previousPosition = previousPositionsRef.current.get(label.hex);
        const positionChanged = previousPosition && 
          (previousPosition[0] !== label.position[0] || previousPosition[1] !== label.position[1]);
        
        // Only apply transition for aircraft movement, not map movement
        const shouldTransition = !isMapMoving && positionChanged;
        
        return (
          <div
            key={label.hex}
            className="absolute pointer-events-none"
            style={{
              left: currentScreenPos.x + labelOffset,
              top: currentScreenPos.y,
              transform: 'translateY(-50%)',
              transition: shouldTransition ? 'left 5s linear, top 5s linear' : 'none',
              zIndex: zIndex + 200, // Add offset to appear above markers
            }}
          >
            <div className="bg-white/30 backdrop-blur-sm border border-gray-400/30 rounded-md px-3 py-1.5 shadow-lg">
              {/* Flight number on top */}
              <div className="text-sm font-bold text-gray-900 leading-tight">
                {label.flight}
              </div>
              
              {/* Altitude and climb/descent indicator below */}
              <div className="flex items-center gap-1.5 text-gray-800">
                <span className="text-sm font-medium">{label.altitude}</span>
                {(label.isClimbing || label.isDescending) && (
                  <div className={`flex items-center ${
                    label.isClimbing ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {label.isClimbing ? (
                      <ChevronUp className="w-3.5 h-3.5" />
                    ) : (
                      <ChevronDown className="w-3.5 h-3.5" />
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </>,
    mapContainer
  );
}

export default AircraftLabels; 