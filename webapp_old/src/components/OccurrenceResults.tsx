'use client';

import { useState, useMemo, useEffect } from 'react';
import { Clock, Plane, MapPin, FileText, Hash, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from './ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from './ui/pagination';
import { OccurrenceSearchResponse, OccurrenceResponse } from '../types/api';

interface OccurrenceResultsProps {
  results: OccurrenceSearchResponse | null;
  isLoading: boolean;
  onOccurrenceClick?: (occurrence: OccurrenceResponse) => void;
  selectedOccurrence?: OccurrenceResponse | null;
}

type SortField = 'hex' | 'flight' | 'firstseen' | 'duration' | 'positions' | 'registration';
type SortOrder = 'asc' | 'desc';

const PAGE_SIZE = 25;

export default function OccurrenceResults({ 
  results, 
  isLoading, 
  onOccurrenceClick,
  selectedOccurrence 
}: OccurrenceResultsProps) {
  const [sortField, setSortField] = useState<SortField>('firstseen');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [currentPage, setCurrentPage] = useState(1);

  // Helper function for shorter time format
  const formatTime = (isoString: string) => {
    return new Date(isoString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDuration = (start: string, end: string) => {
    const startTime = new Date(start);
    const endTime = new Date(end);
    const durationMs = endTime.getTime() - startTime.getTime();
    const durationSeconds = Math.round(durationMs / 1000);
    
    if (durationSeconds < 60) {
      return `${durationSeconds}s`;
    } else if (durationSeconds < 3600) {
      const minutes = Math.round(durationSeconds / 60);
      return `${minutes}m`;
    } else {
      const hours = Math.floor(durationSeconds / 3600);
      const minutes = Math.round((durationSeconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  };

  const getDurationInSeconds = (start: string, end: string) => {
    const startTime = new Date(start);
    const endTime = new Date(end);
    return Math.round((endTime.getTime() - startTime.getTime()) / 1000);
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
    // Reset to first page when sorting changes
    setCurrentPage(1);
  };

  const sortedOccurrences = useMemo(() => {
    if (!results?.occurrences) return [];
    
    return [...results.occurrences].sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;
      
      switch (sortField) {
        case 'hex':
          aValue = a.hex;
          bValue = b.hex;
          break;
        case 'flight':
          aValue = a.flight || '';
          bValue = b.flight || '';
          break;
        case 'firstseen':
          aValue = new Date(a.firstseen).getTime();
          bValue = new Date(b.firstseen).getTime();
          break;
        case 'duration':
          aValue = getDurationInSeconds(a.firstseen, a.lastseen);
          bValue = getDurationInSeconds(b.firstseen, b.lastseen);
          break;
        case 'positions':
          aValue = a.position_count;
          bValue = b.position_count;
          break;
        case 'registration':
          aValue = a.registration || '';
          bValue = b.registration || '';
          break;
        default:
          return 0;
      }
      
      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
  }, [results?.occurrences, sortField, sortOrder]);

  // Pagination calculations
  const totalPages = Math.ceil(sortedOccurrences.length / PAGE_SIZE);
  const startIndex = (currentPage - 1) * PAGE_SIZE;
  const endIndex = startIndex + PAGE_SIZE;
  const paginatedOccurrences = sortedOccurrences.slice(startIndex, endIndex);

  // Reset to first page when results change
  useEffect(() => {
    setCurrentPage(1);
  }, [results?.count]);

  const handleOccurrenceClick = (occurrence: OccurrenceResponse) => {
    if (onOccurrenceClick) {
      onOccurrenceClick(occurrence);
    }
  };

  const isSelected = (occurrence: OccurrenceResponse) => {
    return selectedOccurrence?.hex === occurrence.hex && 
           selectedOccurrence?.firstseen === occurrence.firstseen;
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-3 w-3" />;
    }
    return sortOrder === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />;
  };

  const SortableHeader = ({ field, children }: { field: SortField; children: React.ReactNode }) => (
    <Button
      variant="ghost"
      size="sm"
      className="h-auto p-0 font-medium hover:bg-transparent"
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center gap-1">
        {children}
        {getSortIcon(field)}
      </div>
    </Button>
  );

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Searching occurrences...</p>
        </div>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center">
          <Plane className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-muted-foreground mb-2">No Search Results</h3>
          <p className="text-sm text-muted-foreground">Perform a search to see aircraft occurrences here.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Summary Card */}
      <Card className="p-3">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Hash className="h-4 w-4 text-muted-foreground" />
            <h2 className="text-sm font-semibold">Search Results</h2>
          </div>
          <div className="text-xs text-muted-foreground space-y-1">
            <div>
              {results.count} occurrences found
              {totalPages > 1 && (
                <span className="ml-2">
                  (Page {currentPage} of {totalPages} • Showing {startIndex + 1}-{Math.min(endIndex, sortedOccurrences.length)})
                </span>
              )}
            </div>
            <div>Execution time: {results.execution_time_ms}ms</div>
            {selectedOccurrence && (
              <div className="text-primary">Flight trace displayed for {selectedOccurrence.hex.toUpperCase()}</div>
            )}
          </div>
        </div>
      </Card>

      {/* Results Table */}
      {results.count > 0 ? (
        <div className="space-y-3">
          <Card className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="hover:bg-transparent">
                  <TableHead className="w-20">
                    <SortableHeader field="hex">Hex</SortableHeader>
                  </TableHead>
                  <TableHead>
                    <SortableHeader field="flight">Flight</SortableHeader>
                  </TableHead>
                  <TableHead>
                    <SortableHeader field="registration">Registration</SortableHeader>
                  </TableHead>
                  <TableHead>
                    <SortableHeader field="firstseen">First Seen</SortableHeader>
                  </TableHead>
                  <TableHead>
                    <SortableHeader field="duration">Duration</SortableHeader>
                  </TableHead>
                  <TableHead>
                    <SortableHeader field="positions">Positions</SortableHeader>
                  </TableHead>
                  <TableHead className="w-20">Category</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedOccurrences.map((occurrence) => (
                  <TableRow
                    key={`${occurrence.hex}-${occurrence.firstseen}`}
                    className={`cursor-pointer ${
                      isSelected(occurrence) 
                        ? 'bg-primary/10 hover:bg-primary/20' 
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => handleOccurrenceClick(occurrence)}
                  >
                    <TableCell className="font-mono">
                      <div className="flex items-center space-x-2">
                        <div className="w-6 h-4 bg-primary/10 rounded flex items-center justify-center">
                          <span className="text-xs font-mono text-primary">
                            {occurrence.hex.slice(-4).toUpperCase()}
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        {occurrence.flight ? (
                          <>
                            <Plane className="h-3 w-3 text-muted-foreground" />
                            <span className="font-mono text-sm">{occurrence.flight}</span>
                          </>
                        ) : (
                          <span className="text-muted-foreground text-sm">No flight</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-sm">
                      {occurrence.registration || '—'}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">{formatTime(occurrence.firstseen)}</span>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium text-sm">
                      {formatDuration(occurrence.firstseen, occurrence.lastseen)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <MapPin className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">{occurrence.position_count}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {occurrence.category && (
                        <Badge variant="secondary" className="text-xs py-0 px-1">
                          {occurrence.category}
                        </Badge>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Card>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious 
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        if (currentPage > 1) setCurrentPage(currentPage - 1);
                      }}
                      className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
                    />
                  </PaginationItem>
                  
                  {/* First page */}
                  {currentPage > 2 && (
                    <>
                      <PaginationItem>
                        <PaginationLink 
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            setCurrentPage(1);
                          }}
                        >
                          1
                        </PaginationLink>
                      </PaginationItem>
                      {currentPage > 3 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}
                    </>
                  )}
                  
                  {/* Previous page */}
                  {currentPage > 1 && (
                    <PaginationItem>
                      <PaginationLink 
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          setCurrentPage(currentPage - 1);
                        }}
                      >
                        {currentPage - 1}
                      </PaginationLink>
                    </PaginationItem>
                  )}
                  
                  {/* Current page */}
                  <PaginationItem>
                    <PaginationLink 
                      href="#" 
                      isActive
                      onClick={(e) => e.preventDefault()}
                    >
                      {currentPage}
                    </PaginationLink>
                  </PaginationItem>
                  
                  {/* Next page */}
                  {currentPage < totalPages && (
                    <PaginationItem>
                      <PaginationLink 
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          setCurrentPage(currentPage + 1);
                        }}
                      >
                        {currentPage + 1}
                      </PaginationLink>
                    </PaginationItem>
                  )}
                  
                  {/* Last page */}
                  {currentPage < totalPages - 1 && (
                    <>
                      {currentPage < totalPages - 2 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}
                      <PaginationItem>
                        <PaginationLink 
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            setCurrentPage(totalPages);
                          }}
                        >
                          {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    </>
                  )}
                  
                  <PaginationItem>
                    <PaginationNext 
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        if (currentPage < totalPages) setCurrentPage(currentPage + 1);
                      }}
                      className={currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      ) : (
        <Card className="p-6">
          <div className="text-center">
            <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
            <h3 className="text-sm font-semibold text-muted-foreground mb-1">No Occurrences Found</h3>
            <p className="text-xs text-muted-foreground">
              No aircraft occurrences match your search criteria.
            </p>
          </div>
        </Card>
      )}
    </div>
  );
} 