'use client';

import { Polyline } from 'react-leaflet';
import { FlightTraceResponse } from '../types/api';
import { AIRCRAFT_CONFIG, getAltitudeColor } from '../lib/aircraft-config';
import { getLatLngFromH3 } from '../lib/utils/coordinates';

interface FlightTraceTrailsProps {
  flightTrace: FlightTraceResponse | null;
  showTrace?: boolean;
}

// Create a trail path from flight trace points
const createTraceTrailPath = (
  traceResponse: FlightTraceResponse
): [number, number][] => {
  const path: [number, number][] = [];
  
  // Add trace points in chronological order
  for (const tracePoint of traceResponse.trace_points) {
    if (tracePoint.h3_index) {
      const coords = getLatLngFromH3(tracePoint.h3_index);
      if (coords) {
        path.push(coords);
      }
    }
  }
  
  return path;
};

// Get trail color based on the flight trace data
const getTraceColor = (traceResponse: FlightTraceResponse): string => {
  // Try to get an average altitude for color determination
  const altitudes = traceResponse.trace_points
    .map(point => point.alt_baro)
    .filter(alt => alt !== null && alt !== undefined) as number[];
  
  if (altitudes.length === 0) {
    return AIRCRAFT_CONFIG.ALTITUDE_COLORS.ground;
  }
  
  const avgAltitude = altitudes.reduce((sum, alt) => sum + alt, 0) / altitudes.length;
  return getAltitudeColor(avgAltitude, false);
};

export function FlightTraceTrails({ 
  flightTrace, 
  showTrace = true
}: FlightTraceTrailsProps) {
  if (!showTrace || !flightTrace || flightTrace.trace_points.length < 2) {
    return null;
  }

  const trailPath = createTraceTrailPath(flightTrace);
  
  // Need at least 2 points to draw a line
  if (trailPath.length < 2) return null;
  
  const trailColor = getTraceColor(flightTrace);
  
  return (
    <Polyline
      key={`flight-trace-${flightTrace.hex}`}
      positions={trailPath}
      pathOptions={{
        color: trailColor,
        weight: AIRCRAFT_CONFIG.TRAIL_WEIGHT + 1, // Slightly thicker than regular trails
        opacity: 0.9, // More opaque than regular trails
        dashArray: undefined, // Solid line for flight traces
      }}
    />
  );
}

export default FlightTraceTrails; 