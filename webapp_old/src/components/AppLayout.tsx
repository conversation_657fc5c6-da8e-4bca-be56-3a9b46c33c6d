'use client';

import React from 'react';
import AppNavbar from './AppNavbar';
import { AirportResponse } from '../types/api';

interface AppLayoutProps {
  children: React.ReactNode;
  
  // Navbar props (all optional)
  airport?: AirportResponse | null;
  airportLoading?: boolean;
  airportError?: string | null;
  icao?: string;
  lastUpdated?: Date;
  isLoading?: boolean;
  title?: string;
  subtitle?: string;
  
  // Layout customization
  className?: string;
}

export function AppLayout({
  children,
  airport,
  airportLoading,
  airportError,
  icao,
  lastUpdated,
  isLoading,
  title,
  subtitle,
  className = ''
}: AppLayoutProps) {
  return (
    <div className={`h-screen flex flex-col bg-background ${className}`}>
      <AppNavbar
        airport={airport}
        airportLoading={airportLoading}
        airportError={airportError}
        icao={icao}
        lastUpdated={lastUpdated}
        isLoading={isLoading}
        title={title}
        subtitle={subtitle}
      />
      
      {/* Main content area that takes remaining height */}
      <div className="flex-1 min-h-0">
        {children}
      </div>
    </div>
  );
}

export default AppLayout; 