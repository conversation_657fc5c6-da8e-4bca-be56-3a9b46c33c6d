'use client';

import { Polyline } from 'react-leaflet';
import { AircraftPositionResponse } from '../types/api';
import { AIRCRAFT_CONFIG, getAltitudeColor } from '../lib/aircraft-config';
import { getLatLngFromH3 } from '../lib/utils/coordinates';

interface AircraftTrailsProps {
  positions: AircraftPositionResponse[];
  showTrails?: boolean;
  maxTrailLength?: number;
}

// Get trail color based on aircraft altitude/phase
const getTrailColor = (position: AircraftPositionResponse): string => {
  const isGround = position.flight_phase === 'ground';
  return getAltitudeColor(position.alt_baro, isGround);
};

// Create a trail path from current position and trace points
const createTrailPath = (
  position: AircraftPositionResponse,
  maxLength?: number
): [number, number][] => {
  const path: [number, number][] = [];
  
  // Add trace points (historical positions)
  if (position.trace && position.trace.length > 0) {
    const traceToUse = maxLength 
      ? position.trace.slice(-maxLength)
      : position.trace;
      
    for (const tracePoint of traceToUse) {
      const coords = getLatLngFromH3(tracePoint.h3_index);
      if (coords) {
        path.push(coords);
      }
    }
  }
  
  // Add current position as the latest point
  const currentCoords = getLatLngFromH3(position.h3_index);
  if (currentCoords) {
    path.push(currentCoords);
  }
  
  return path;
};

// Get trail opacity based on age and length
const getTrailOpacity = (traceLength: number): number => {
  if (traceLength > 20) return AIRCRAFT_CONFIG.TRAIL_OPACITY.long;
  if (traceLength > 10) return AIRCRAFT_CONFIG.TRAIL_OPACITY.medium;
  return AIRCRAFT_CONFIG.TRAIL_OPACITY.short;
};

export function AircraftTrails({ 
  positions, 
  showTrails = true,
  maxTrailLength = AIRCRAFT_CONFIG.MAX_TRAIL_LENGTH
}: AircraftTrailsProps) {
  if (!showTrails) return null;

  return (
    <>
      {positions.map((position) => {
        // Only show trail if we have trace data and it's meaningful
        if (!position.trace || position.trace.length < AIRCRAFT_CONFIG.MIN_TRAIL_POINTS) return null;
        
        const trailPath = createTrailPath(position, maxTrailLength);
        
        // Need at least minimum points to draw a line
        if (trailPath.length < AIRCRAFT_CONFIG.MIN_TRAIL_POINTS) return null;
        
        const trailColor = getTrailColor(position);
        const opacity = getTrailOpacity(trailPath.length);
        
        return (
          <Polyline
            key={`trail-${position.hex}`}
            positions={trailPath}
            pathOptions={{
              color: trailColor,
              weight: AIRCRAFT_CONFIG.TRAIL_WEIGHT,
              opacity: opacity,
              dashArray: AIRCRAFT_CONFIG.TRAIL_DASH_PATTERN,
            }}
          />
        );
      })}
    </>
  );
}

export default AircraftTrails; 