'use client';

import React from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { PlaneIcon, RefreshCw, Menu, Home, Activity, AlertTriangle, Monitor } from 'lucide-react';
import { AirportInfo } from './AirportInfo';
import { AirportResponse } from '../types/api';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";

interface AppNavbarProps {
  // Airport-specific props (optional - for airport pages)
  airport?: AirportResponse | null;
  airportLoading?: boolean;
  airportError?: string | null;
  icao?: string;
  
  // Optional last updated indicator
  lastUpdated?: Date;
  isLoading?: boolean;
  
  // Optional title override
  title?: string;
  subtitle?: string;
}

export function AppNavbar({
  airport,
  airportLoading,
  airportError,
  icao,
  lastUpdated,
  isLoading,
  title,
  subtitle
}: AppNavbarProps) {
  const router = useRouter();
  const pathname = usePathname();

  const menuItems = [
    {
      label: 'Home',
      href: '/',
      icon: Home,
      description: 'Return to homepage'
    }
  ];

  // Add airport-specific menu items if we have an ICAO code
  if (icao) {
    menuItems.push(
      {
        label: 'Airport Overview',
        href: `/${icao}`,
        icon: Monitor,
        description: 'Airport dashboard'
      },
      {
        label: 'Live Activity',
        href: `/${icao}/live`,
        icon: Activity,
        description: 'Real-time flights and operations'
      },
      {
        label: 'Occurrences',
        href: `/${icao}/occurrences`,
        icon: AlertTriangle,
        description: 'Incident reports and safety data'
      }
    );
  }

  const handleNavigation = (href: string) => {
    router.push(href);
  };

  return (
    <header className="border-b bg-card px-4 lg:px-6 py-3 flex-shrink-0">
      <div className="flex items-center gap-3">
        {/* Mobile Menu Button - Left Side */}
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="outline" size="icon" className="lg:hidden">
              <Menu className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-72">
            <SheetHeader>
              <SheetTitle className="flex items-center gap-2">
                <PlaneIcon className="h-5 w-5 text-primary" />
                Navigation
              </SheetTitle>
            </SheetHeader>
            <div className="mt-6 space-y-2">
              {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = pathname === item.href;
                
                return (
                  <button
                    key={item.href}
                    onClick={() => handleNavigation(item.href)}
                    className={`w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-left transition-colors ${
                      isActive 
                        ? 'bg-primary text-primary-foreground' 
                        : 'hover:bg-muted hover:text-foreground text-muted-foreground'
                    }`}
                  >
                    <Icon className="h-4 w-4 flex-shrink-0" />
                    <div>
                      <div className="font-medium">{item.label}</div>
                      <div className="text-xs opacity-70">{item.description}</div>
                    </div>
                  </button>
                );
              })}
            </div>
          </SheetContent>
        </Sheet>

        {/* Desktop Menu Button - Left Side */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon" className="hidden lg:flex">
              <Menu className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            {menuItems.map((item, index) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              
              return (
                <React.Fragment key={item.href}>
                  {index === 1 && icao && <DropdownMenuSeparator />}
                  <DropdownMenuItem 
                    onClick={() => handleNavigation(item.href)}
                    className={`flex items-center gap-2 cursor-pointer ${
                      isActive ? 'bg-primary/10 text-primary' : ''
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <div>
                      <div className="font-medium">{item.label}</div>
                      <div className="text-xs text-muted-foreground">{item.description}</div>
                    </div>
                  </DropdownMenuItem>
                </React.Fragment>
              );
            })}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Logo */}
        <Link href="/" className="flex items-center gap-3 hover:opacity-80 transition-opacity">
          <PlaneIcon className="h-6 w-6 text-primary" />
          <h1 className="text-xl lg:text-2xl font-bold">SkyTraces</h1>
        </Link>
        
        <span className="text-muted-foreground">|</span>
        
        {/* Airport info or custom title */}
        {icao && airport !== undefined ? (
          <AirportInfo 
            airport={airport} 
            loading={airportLoading || false} 
            error={airportError || null} 
            icao={icao} 
          />
        ) : title ? (
          <div>
            <h2 className="text-lg font-semibold">{title}</h2>
            {subtitle && (
              <p className="text-sm text-muted-foreground">{subtitle}</p>
            )}
          </div>
        ) : null}
        
        {/* Live data indicator */}
        {lastUpdated && (
          <div className="ml-auto flex items-center gap-2 text-sm text-muted-foreground">
            <RefreshCw className="h-4 w-4" />
            <span className="hidden sm:inline">Last updated: {lastUpdated.toLocaleTimeString()}</span>
            <span className="sm:hidden">Updated: {lastUpdated.toLocaleTimeString([], { timeStyle: 'short' })}</span>
            {isLoading && (
              <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full" />
            )}
          </div>
        )}
      </div>
    </header>
  );
}

export default AppNavbar; 