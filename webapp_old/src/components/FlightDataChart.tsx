'use client';

import { useState, useMemo, useRef, useCallback, useEffect } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, ReferenceArea, ReferenceLine } from 'recharts';
import { ChevronUp, ChevronDown, Plane, BarChart3 } from 'lucide-react';
import { FlightTraceResponse, FlightPhaseEnum, OccurrenceResponse } from '../types/api';

// ===== TYPES =====
interface FlightDataChartProps {
  flightTrace: FlightTraceResponse | null;
  isVisible?: boolean;
  selectedOccurrence?: OccurrenceResponse | null;
  onTimeHover?: (timestamp: string | null) => void;
}

interface ChartDataPoint {
  time: string;
  timeDisplay: string;
  altitudeFt?: number;
  groundSpeedKts?: number;
  flightPhase?: FlightPhaseEnum;
  timeMs: number;
}

interface OccurrenceRange {
  start: string;
  end: string;
}

// ===== CONSTANTS =====
const CHART_MARGINS = { top: 5, right: 30, left: 20, bottom: 5 } as const;
const CHART_COLORS = {
  altitude: '#3b82f6',
  groundSpeed: '#22c55e',
  timeMarker: '#3b82f6',
  occurrenceHighlight: '#fbbf24',
  grid: '#f1f5f9',
  axis: '#64748b',
} as const;

// ===== CUSTOM HOOKS =====
const useChartData = (flightTrace: FlightTraceResponse | null): ChartDataPoint[] => {
  return useMemo(() => {
    if (!flightTrace?.trace_points) return [];

    return flightTrace.trace_points
      .filter(point => point.alt_baro != null || point.gs != null)
      .map(point => {
        const date = new Date(point.time);
        return {
          time: point.time,
          timeDisplay: date.toLocaleTimeString(),
          altitudeFt: point.alt_baro,
          groundSpeedKts: point.gs,
          flightPhase: point.flight_phase,
          timeMs: date.getTime(),
        };
      })
      .sort((a, b) => a.timeMs - b.timeMs);
  }, [flightTrace]);
};

const useOccurrenceRange = (
  selectedOccurrence: OccurrenceResponse | null,
  chartData: ChartDataPoint[]
): OccurrenceRange | null => {
  return useMemo(() => {
    if (!selectedOccurrence || chartData.length === 0) return null;
    
    const firstSeenMs = new Date(selectedOccurrence.firstseen).getTime();
    const lastSeenMs = new Date(selectedOccurrence.lastseen).getTime();
    
    // Find the closest chart data points to the occurrence range
    const firstPointIndex = chartData.findIndex(point => point.timeMs >= firstSeenMs);
    const lastPointIndex = chartData.findIndex(point => point.timeMs > lastSeenMs);
    
    if (firstPointIndex === -1) return null;
    
    const startTime = chartData[firstPointIndex]?.timeDisplay;
    const endTime = lastPointIndex === -1 
      ? chartData[chartData.length - 1]?.timeDisplay 
      : chartData[lastPointIndex - 1]?.timeDisplay;
    
    return startTime && endTime ? { start: startTime, end: endTime } : null;
  }, [selectedOccurrence, chartData]);
};

const useDragTimeMarker = (selectedOccurrence: OccurrenceResponse | null) => {
  const [dragTimeMarker, setDragTimeMarker] = useState<string | null>(null);

  // Initialize and update drag time marker when selected occurrence changes
  useEffect(() => {
    if (selectedOccurrence) {
      setDragTimeMarker(selectedOccurrence.firstseen);
    } else {
      setDragTimeMarker(null);
    }
  }, [selectedOccurrence]);

  return [dragTimeMarker, setDragTimeMarker] as const;
};

// ===== COMPONENT =====
export default function FlightDataChart({ 
  flightTrace, 
  isVisible = true, 
  selectedOccurrence, 
  onTimeHover 
}: FlightDataChartProps) {
  // ===== STATE =====
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const chartRef = useRef<HTMLDivElement>(null);

  // ===== DERIVED STATE =====
  const chartData = useChartData(flightTrace);
  const occurrenceRange = useOccurrenceRange(selectedOccurrence, chartData);
  const [dragTimeMarker, setDragTimeMarker] = useDragTimeMarker(selectedOccurrence);

  // Auto-trigger onTimeHover when dragTimeMarker is first set
  useEffect(() => {
    if (dragTimeMarker && onTimeHover) {
      onTimeHover(dragTimeMarker);
    }
  }, [dragTimeMarker, onTimeHover]);

  // Convert drag time marker to display format
  const dragTimeMarkerDisplay = useMemo(() => {
    if (!dragTimeMarker) return null;
    return new Date(dragTimeMarker).toLocaleTimeString();
  }, [dragTimeMarker]);

  // Calculate chart metrics
  const chartMetrics = useMemo(() => {
    const hasAltitudeData = chartData.some(point => point.altitudeFt != null);
    const hasGroundSpeedData = chartData.some(point => point.groundSpeedKts != null);
    const maxAltitude = Math.max(...chartData.map(p => p.altitudeFt || 0));
    const maxGroundSpeed = Math.max(...chartData.map(p => p.groundSpeedKts || 0));

    return {
      hasAltitudeData,
      hasGroundSpeedData,
      maxAltitude,
      maxGroundSpeed,
    };
  }, [chartData]);

  // ===== EVENT HANDLERS =====
  // Handle chart mouse events - using Recharts' coordinate system
  const handleChartMouseMove = useCallback((e: any) => {
    if (e && e.activeLabel) {
      // Find the data point for this time using Recharts' activeLabel
      const dataPoint = chartData.find(point => point.timeDisplay === e.activeLabel);
      if (dataPoint) {
        // If we're dragging, update the marker position
        if (isDragging) {
          setDragTimeMarker(dataPoint.time);
          onTimeHover?.(dataPoint.time);
        }
      }
    }
  }, [isDragging, chartData, onTimeHover]);

  // Handle mouse down to start dragging
  const handleMouseDown = useCallback((e: any) => {
    e.preventDefault?.();
    setIsDragging(true);
    
    // Set initial position if we have activeLabel
    if (e && e.activeLabel) {
      const dataPoint = chartData.find(point => point.timeDisplay === e.activeLabel);
      if (dataPoint) {
        setDragTimeMarker(dataPoint.time);
        onTimeHover?.(dataPoint.time);
      }
    }
  }, [chartData, onTimeHover]);

  // Handle mouse up to stop dragging
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Handle mouse leave to stop dragging
  const handleMouseLeave = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Handle header click to toggle expansion
  const handleHeaderClick = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  // ===== EARLY RETURNS =====
  if (!isVisible || !flightTrace || chartData.length === 0) {
    return null;
  }

  // ===== RENDER =====
  return (
    <div 
      className={`absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-2xl z-[1000] transition-transform duration-300 ease-in-out ${
        isExpanded ? 'translate-y-0' : 'translate-y-[calc(100%-48px)]'
      }`}
      role="region"
      aria-label="Flight Data Chart"
    >
      {/* Header */}
      <header 
        className="h-12 bg-gray-50 border-b border-gray-200 flex items-center justify-between px-4 cursor-pointer hover:bg-gray-100 transition-colors"
        onClick={handleHeaderClick}
        role="button"
        tabIndex={0}
        aria-label={`${isExpanded ? 'Collapse' : 'Expand'} flight data chart`}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleHeaderClick();
          }
        }}
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-gray-600" aria-hidden="true" />
            <span className="text-sm font-medium text-gray-900">Flight Data</span>
          </div>
          
          {flightTrace.registration && (
            <div className="flex items-center gap-1 text-xs text-gray-600">
              <Plane className="h-3 w-3" aria-hidden="true" />
              <span>{flightTrace.registration}</span>
            </div>
          )}
          
          <span className="text-xs text-gray-500">
            {chartData.length} points
          </span>
          
          {selectedOccurrence && occurrenceRange && (
            <div className="flex items-center gap-1 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
              <span>Occurrence range highlighted</span>
            </div>
          )}
          
          {dragTimeMarkerDisplay && (
            <div className="flex items-center gap-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
              <span>Time: {dragTimeMarkerDisplay}</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {chartMetrics.hasAltitudeData && (
            <div className="flex items-center gap-1 text-xs text-blue-600">
              <div className="w-2 h-2 bg-blue-600 rounded-full" aria-hidden="true"></div>
              <span>Altitude</span>
            </div>
          )}
          
          {chartMetrics.hasGroundSpeedData && (
            <div className="flex items-center gap-1 text-xs text-green-600">
              <div className="w-2 h-2 bg-green-600 rounded-full" aria-hidden="true"></div>
              <span>Ground Speed</span>
            </div>
          )}
          
          {isExpanded ? (
            <ChevronDown className="h-4 w-4 text-gray-600" aria-hidden="true" />
          ) : (
            <ChevronUp className="h-4 w-4 text-gray-600" aria-hidden="true" />
          )}
        </div>
      </header>

      {/* Chart Content */}
      <div className={`transition-all duration-300 ease-in-out overflow-hidden ${
        isExpanded ? 'h-80' : 'h-0'
      }`}>
        <div className="p-4 h-full">
          <div 
            ref={chartRef}
            className={`w-full h-full ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
            role="img"
            aria-label="Flight data timeline chart"
          >
            <ResponsiveContainer width="100%" height="100%">
              <LineChart 
                data={chartData} 
                margin={CHART_MARGINS}
                onMouseMove={handleChartMouseMove}
                onMouseDown={handleMouseDown}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseLeave}
              >
                <CartesianGrid strokeDasharray="3 3" stroke={CHART_COLORS.grid} />
                
                {/* Highlight occurrence range */}
                {occurrenceRange && (
                  <ReferenceArea
                    x1={occurrenceRange.start}
                    x2={occurrenceRange.end}
                    fill="#f59e0b"
                    fillOpacity={0.3}
                    stroke="#f59e0b"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                  />
                )}
                
                {/* Draggable time marker */}
                {dragTimeMarkerDisplay && (
                  <ReferenceLine
                    x={dragTimeMarkerDisplay}
                    yAxisId="altitude"
                    stroke={CHART_COLORS.timeMarker}
                    strokeWidth={3}
                    strokeDasharray="none"
                    label={{ 
                      value: "●", 
                      position: "top",
                      style: { 
                        fontSize: "16px", 
                        fill: CHART_COLORS.timeMarker,
                        textAnchor: "middle"
                      }
                    }}
                  />
                )}
                
                <XAxis 
                  dataKey="timeDisplay"
                  tick={{ fontSize: 11 }}
                  tickFormatter={(value, index) => index % Math.ceil(chartData.length / 8) === 0 ? value : ''}
                  stroke={CHART_COLORS.axis}
                />
                
                <YAxis 
                  yAxisId="altitude"
                  orientation="left"
                  tick={{ fontSize: 11 }}
                  tickFormatter={(value) => `${(value / 1000).toFixed(1)}k`}
                  domain={[0, Math.ceil(chartMetrics.maxAltitude / 1000) * 1000]}
                  stroke={CHART_COLORS.altitude}
                  label={{ 
                    value: 'Altitude (ft)', 
                    angle: -90, 
                    position: 'insideLeft', 
                    style: { 
                      textAnchor: 'middle', 
                      fontSize: '11px', 
                      fill: CHART_COLORS.altitude 
                    } 
                  }}
                />
                
                {chartMetrics.hasGroundSpeedData && (
                  <YAxis 
                    yAxisId="groundspeed"
                    orientation="right"
                    tick={{ fontSize: 11 }}
                    domain={[0, Math.ceil(chartMetrics.maxGroundSpeed / 50) * 50]}
                    stroke={CHART_COLORS.groundSpeed}
                    label={{ 
                      value: 'Ground Speed (kts)', 
                      angle: 90, 
                      position: 'insideRight', 
                      style: { 
                        textAnchor: 'middle', 
                        fontSize: '11px', 
                        fill: CHART_COLORS.groundSpeed 
                      } 
                    }}
                  />
                )}
                
                {chartMetrics.hasAltitudeData && (
                  <Line
                    yAxisId="altitude"
                    type="monotone"
                    dataKey="altitudeFt"
                    stroke={CHART_COLORS.altitude}
                    strokeWidth={2}
                    dot={false}
                    connectNulls={false}
                    name="Altitude"
                  />
                )}
                
                {chartMetrics.hasGroundSpeedData && (
                  <Line
                    yAxisId="groundspeed"
                    type="monotone"
                    dataKey="groundSpeedKts"
                    stroke={CHART_COLORS.groundSpeed}
                    strokeWidth={2}
                    dot={false}
                    connectNulls={false}
                    name="Ground Speed"
                  />
                )}
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
} 