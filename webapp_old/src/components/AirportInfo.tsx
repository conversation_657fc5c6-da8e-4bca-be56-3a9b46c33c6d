import { MapPin, AlertCircle, Loader2 } from 'lucide-react';
import { AirportResponse } from '../types/api';

interface AirportInfoProps {
  airport: AirportResponse | null;
  loading: boolean;
  error: string | null;
  icao: string;
}

/**
 * Component to display airport information in the header
 * 
 * Simple and clean display of airport name and location
 */
export function AirportInfo({ airport, loading, error, icao }: AirportInfoProps) {
  if (loading) {
    return (
      <div className="flex items-center gap-3">
        <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
        <div className="flex items-center gap-2">
          <MapPin className="h-4 w-4 text-muted-foreground" />
          <span className="text-lg font-semibold text-primary">{icao.toUpperCase()}</span>
          <span className="text-muted-foreground">Loading...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center gap-3">
        <AlertCircle className="h-4 w-4 text-destructive" />
        <div className="flex items-center gap-2">
          <MapPin className="h-4 w-4 text-muted-foreground" />
          <span className="text-lg font-semibold text-primary">{icao.toUpperCase()}</span>
          <span className="text-sm text-destructive">{error}</span>
        </div>
      </div>
    );
  }

  if (!airport) {
    return (
      <div className="flex items-center gap-3">
        <AlertCircle className="h-4 w-4 text-muted-foreground" />
        <div className="flex items-center gap-2">
          <MapPin className="h-4 w-4 text-muted-foreground" />
          <span className="text-lg font-semibold text-primary">{icao.toUpperCase()}</span>
          <span className="text-muted-foreground">Airport not found</span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-3">
      <MapPin className="h-4 w-4 text-muted-foreground" />
      <div className="flex items-center gap-2">
        <span className="text-lg font-semibold text-primary">
          {airport.icao}
          {airport.iata && (
            <span className="text-muted-foreground font-normal">
              {' '}({airport.iata})
            </span>
          )}
        </span>
        {airport.name && (
          <span className="text-muted-foreground">
            • {airport.name}
          </span>
        )}
      </div>
    </div>
  );
} 