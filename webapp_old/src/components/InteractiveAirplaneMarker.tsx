'use client';

import { useMemo, useEffect } from 'react';
import { Mark<PERSON> } from 'react-leaflet';
import L from 'leaflet';
import { FlightTraceResponse } from '../types/api';
import { createSvgIcon, getAircraftIcon } from '../lib/aircraftIcons';
import { getAltitudeColor, getAircraftIconSize } from '../lib/aircraft-config';
import { getLatLngFromH3 } from '../lib/utils/coordinates';

interface InteractiveAirplaneMarkerProps {
  flightTrace: FlightTraceResponse | null;
  hoveredTimestamp: string | null;
  showMarker?: boolean;
}

export default function InteractiveAirplaneMarker({ 
  flightTrace, 
  hoveredTimestamp, 
  showMarker = true 
}: InteractiveAirplaneMarkerProps) {
  // Add CSS for enhanced airplane marker styling
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .interactive-airplane-marker {
        filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.6)) drop-shadow(0 0 12px rgba(59, 130, 246, 0.3));
        transition: all 0.2s ease-in-out;
        animation: pulse-glow 2s infinite;
        z-index: 1000 !important;
      }
      
      @keyframes pulse-glow {
        0%, 100% {
          filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.6)) drop-shadow(0 0 12px rgba(59, 130, 246, 0.3));
        }
        50% {
          filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.8)) drop-shadow(0 0 16px rgba(59, 130, 246, 0.5));
        }
      }
      
      .interactive-airplane-marker:hover {
        transform: scale(1.1);
        filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.8)) drop-shadow(0 0 20px rgba(59, 130, 246, 0.6));
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Find the position and create airplane icon based on hovered timestamp
  const markerData = useMemo(() => {
    if (!flightTrace || !hoveredTimestamp || !showMarker) return null;

    // Find the trace point closest to the hovered timestamp
    const targetTime = new Date(hoveredTimestamp).getTime();
    let closestPoint = null;
    let smallestTimeDiff = Infinity;

    for (const point of flightTrace.trace_points) {
      const pointTime = new Date(point.time).getTime();
      const timeDiff = Math.abs(pointTime - targetTime);
      
      if (timeDiff < smallestTimeDiff && point.h3_index) {
        smallestTimeDiff = timeDiff;
        closestPoint = point;
      }
    }

    if (!closestPoint || !closestPoint.h3_index) return null;

    // Get coordinates from H3 index
    const coords = getLatLngFromH3(closestPoint.h3_index);
    if (!coords) return null;

    // Determine aircraft icon type and properties
    const [shapeName, scale] = getAircraftIcon(
      flightTrace.category,
      flightTrace.type,
      undefined,
      undefined
    );

    const isGround = closestPoint.alt_baro === 0 || closestPoint.flight_phase === 'ground';
    const color = getAltitudeColor(closestPoint.alt_baro || 0, isGround);
    const rotation = closestPoint.track || 0;
    
    // Create enhanced airplane icon (larger and more prominent)
    const svgDataUrl = createSvgIcon(shapeName, rotation, color, scale * 1.8, isGround);
    const iconSize = getAircraftIconSize(scale * 1.8);

    const icon = L.icon({
      iconUrl: svgDataUrl,
      iconSize: [iconSize, iconSize],
      iconAnchor: [iconSize / 2, iconSize / 2],
      className: 'interactive-airplane-marker',
    });

    return {
      position: coords as [number, number],
      icon,
      data: closestPoint
    };
  }, [flightTrace, hoveredTimestamp, showMarker]);

  if (!markerData) return null;

  return (
    <Marker
      position={markerData.position}
      icon={markerData.icon}
      zIndexOffset={1000} // Ensure it appears above other markers
    />
  );
} 