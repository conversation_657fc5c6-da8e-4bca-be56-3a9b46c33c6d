import { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, useMap, Polygon, CircleMarker, Circle, Marker } from 'react-leaflet';
import L from 'leaflet';
import FlightTraceTrails from './FlightTraceTrails';
import InteractiveAirplaneMarker from './InteractiveAirplaneMarker';
import { FlightTraceResponse } from '../types/api';

// Constants
const DEFAULT_COORDINATES = { lat: 40.7128, lng: -74.0060, zoom: 13 } as const;

const POLYGON_STYLE = {
  color: '#3388ff',
  weight: 2,
  opacity: 0.8,
  fillColor: '#3388ff',
  fillOpacity: 0.2,
} as const;

const RADIUS_STYLE = {
  center: { color: '#ff6b35', weight: 3, opacity: 1, fillColor: '#ffffff', fillOpacity: 1 },
  circle: { color: '#ff6b35', weight: 2, opacity: 0.8, fillColor: '#ff6b35', fillOpacity: 0.1 },
} as const;

/**
 * Creates a custom icon for polygon control points
 */
const createPolygonPointIcon = (): L.DivIcon => {
  return L.divIcon({
    className: 'polygon-point-marker',
    html: `
      <div style="
        background: #3388ff; 
        border: 2px solid white; 
        border-radius: 50%; 
        width: 12px; 
        height: 12px; 
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
      "></div>
    `,
    iconSize: [12, 12],
    iconAnchor: [6, 6]
  });
};

interface MapControllerProps {
  icao: string;
  onZoomIn: React.MutableRefObject<() => void>;
  onZoomOut: React.MutableRefObject<() => void>;
  onResetView: React.MutableRefObject<() => void>;
  drawingMode: boolean;
  onPolygonPointAdded?: (points: [number, number][]) => void;
  onPolygonPointDragged?: (index: number, newPosition: [number, number]) => void;
  currentPoints: [number, number][];
  radiusMode: boolean;
  onMapClick?: (lat: number, lng: number) => void;
  radiusCenter?: [number, number] | null;
  radiusDistance?: number;
}

/**
 * Core map controller handling interactions and overlays
 */
function MapController({
  icao,
  onZoomIn,
  onZoomOut,
  onResetView,
  drawingMode,
  onPolygonPointAdded,
  onPolygonPointDragged,
  currentPoints,
  radiusMode,
  onMapClick,
  radiusCenter,
  radiusDistance
}: MapControllerProps) {
  const map = useMap();
  const polygonRef = useRef<L.Polygon | null>(null);
  const draggedPointsRef = useRef<[number, number][]>([]);

  // Initialize map view and controls
  useEffect(() => {
    map.setView([DEFAULT_COORDINATES.lat, DEFAULT_COORDINATES.lng], DEFAULT_COORDINATES.zoom);

    // Bind control functions to refs for external access
    onZoomIn.current = () => map.zoomIn();
    onZoomOut.current = () => map.zoomOut();
    onResetView.current = () => map.setView([DEFAULT_COORDINATES.lat, DEFAULT_COORDINATES.lng], DEFAULT_COORDINATES.zoom);
  }, [map, icao, onZoomIn, onZoomOut, onResetView]);

  // Handle map clicks for different modes
  useEffect(() => {
    if (!drawingMode && !radiusMode) return;
    
    const handleMapClick = (e: L.LeafletMouseEvent) => {
      const { lat, lng } = e.latlng;
      
      if (radiusMode) {
        onMapClick?.(lat, lng);
      } else if (drawingMode) {
        const newPoints: [number, number][] = [...currentPoints, [lat, lng]];
        onPolygonPointAdded?.(newPoints);
      }
    };

    map.on('click', handleMapClick);
    return () => map.off('click', handleMapClick);
  }, [map, drawingMode, radiusMode, currentPoints, onPolygonPointAdded, onMapClick]);

  /**
   * Handles smooth polygon point dragging without React re-renders
   */
  const createDragHandlers = (pointIndex: number) => ({
    dragstart: () => {
      draggedPointsRef.current = [...currentPoints];
    },
    drag: (e: L.DragEndEvent) => {
      const marker = e.target as L.Marker;
      const position = marker.getLatLng();
      
      // Update position in ref (no React re-render)
      draggedPointsRef.current[pointIndex] = [position.lat, position.lng];
      
      // Update polygon visual directly via Leaflet API
      if (polygonRef.current && draggedPointsRef.current.length >= 3) {
        polygonRef.current.setLatLngs(draggedPointsRef.current);
      }
    },
    dragend: (e: L.DragEndEvent) => {
      // Single React state update when dragging completes
      const marker = e.target as L.Marker;
      const position = marker.getLatLng();
      onPolygonPointDragged?.(pointIndex, [position.lat, position.lng]);
    }
  });

  return (
    <>
      {/* Polygon Drawing Mode */}
      {drawingMode && (
        <>
          {/* Draggable control points */}
          {currentPoints.map((point, index) => (
            <Marker
              key={`polygon-point-${index}`}
              position={point}
              draggable
              icon={createPolygonPointIcon()}
              eventHandlers={createDragHandlers(index)}
            />
          ))}
          
          {/* Polygon shape (3+ points required) */}
          {currentPoints.length >= 3 && (
            <Polygon
              ref={polygonRef}
              positions={currentPoints}
              pathOptions={POLYGON_STYLE}
            />
          )}
        </>
      )}
      
      {/* Radius Search Mode */}
      {radiusMode && radiusCenter && (
        <>
          {/* Center point marker */}
          <CircleMarker
            center={radiusCenter}
            radius={8}
            pathOptions={RADIUS_STYLE.center}
          />
          
          {/* Search radius circle */}
          {radiusDistance && radiusDistance > 0 && (
            <Circle
              center={radiusCenter}
              radius={radiusDistance}
              pathOptions={RADIUS_STYLE.circle}
            />
          )}
        </>
      )}
    </>
  );
}

interface MapContentProps {
  icao: string;
  currentLayerConfig: {
    url: string;
    attribution: string;
    maxZoom: number;
  };
  onZoomIn: React.MutableRefObject<() => void>;
  onZoomOut: React.MutableRefObject<() => void>;
  onResetView: React.MutableRefObject<() => void>;
  drawingMode: boolean;
  onPolygonPointAdded?: (points: [number, number][]) => void;
  onPolygonPointDragged?: (index: number, newPosition: [number, number]) => void;
  existingPolygon?: [number, number][] | null;
  currentPoints?: [number, number][];
  radiusMode?: boolean;
  onMapClick?: (lat: number, lng: number) => void;
  radiusCenter?: [number, number] | null;
  radiusDistance?: number;
  flightTrace?: FlightTraceResponse | null;
  showFlightTrace?: boolean;
  hoveredTimestamp?: string | null;
}

/**
 * Main map content component with layers and overlays
 */
export default function MapContent({
  icao,
  currentLayerConfig,
  onZoomIn,
  onZoomOut,
  onResetView,
  drawingMode,
  onPolygonPointAdded,
  onPolygonPointDragged,
  existingPolygon,
  currentPoints = [],
  radiusMode = false,
  onMapClick,
  radiusCenter,
  radiusDistance,
  flightTrace,
  showFlightTrace = true,
  hoveredTimestamp
}: MapContentProps) {
  return (
    <MapContainer
      center={[DEFAULT_COORDINATES.lat, DEFAULT_COORDINATES.lng]}
      zoom={DEFAULT_COORDINATES.zoom}
      className="w-full h-full"
      zoomControl={false}
    >
      {/* Base map layer */}
      <TileLayer
        url={currentLayerConfig.url}
        attribution={currentLayerConfig.attribution}
        maxZoom={currentLayerConfig.maxZoom}
      />
      
      {/* Interactive map controller */}
      <MapController
        icao={icao}
        onZoomIn={onZoomIn}
        onZoomOut={onZoomOut}
        onResetView={onResetView}
        drawingMode={drawingMode}
        onPolygonPointAdded={onPolygonPointAdded}
        onPolygonPointDragged={onPolygonPointDragged}
        currentPoints={currentPoints}
        radiusMode={radiusMode}
        onMapClick={onMapClick}
        radiusCenter={radiusCenter}
        radiusDistance={radiusDistance}
      />
      
      {/* Confirmed polygon overlay (when not actively drawing) */}
      {!radiusMode && !drawingMode && existingPolygon && existingPolygon.length >= 3 && (
        <Polygon
          positions={existingPolygon}
          pathOptions={POLYGON_STYLE}
        />
      )}
      
      {/* Flight trace visualization */}
      <FlightTraceTrails
        flightTrace={flightTrace || null}
        showTrace={showFlightTrace}
      />
      
      {/* Interactive airplane marker following chart hover */}
      <InteractiveAirplaneMarker
        flightTrace={flightTrace || null}
        hoveredTimestamp={hoveredTimestamp || null}
        showMarker={showFlightTrace && !!hoveredTimestamp}
      />
    </MapContainer>
  );
} 