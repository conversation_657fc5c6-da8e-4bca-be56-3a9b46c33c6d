'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import L from 'leaflet';
import { MoreVertical, Eye, EyeOff } from 'lucide-react';
import { AirportResponse, RunwayResponse } from '../types/api';
import { getAirportCoordinates, parseRunwayCenterline } from '../lib/utils/airport-coordinates';
import { useAircraftPositions } from '../lib/hooks/useAircraftPositions';
import { MAP_CONFIG } from '../lib/constants';
import AircraftMarkers from './AircraftMarkers';
import AircraftTrails from './AircraftTrails';
import AircraftLabels from './AircraftLabels';

// Fix for default markers in Next.js
if (typeof window !== 'undefined') {
  delete (L.Icon.Default.prototype as unknown as Record<string, unknown>)._getIconUrl;
  L.Icon.Default.mergeOptions({
    iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
    iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
  });
}

interface MapLayer {
  id: string;
  name: string;
  url: string;
  attribution: string;
  maxZoom: number;
}

// Aviation-focused map layers
const MAP_LAYERS: MapLayer[] = [
  {
    id: 'satellite',
    name: 'Satellite',
    url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
    attribution: '© Esri',
    maxZoom: 19,
  },
  {
    id: 'osm',
    name: 'OpenStreetMap',
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '© OpenStreetMap contributors',
    maxZoom: 19,
  },
  {
    id: 'terrain',
    name: 'Terrain',
    url: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png',
    attribution: '© OpenTopoMap contributors',
    maxZoom: 17,
  },
  {
    id: 'aviation',
    name: 'Aviation Chart',
    url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
    attribution: '© CARTO',
    maxZoom: 19,
  },
];

interface MapControlsProps {
  currentLayer: string;
  onLayerChange: (layerId: string) => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetView: () => void;
  showTrails: boolean;
  onToggleTrails: () => void;
}

function MapControls({ 
  currentLayer, 
  onLayerChange, 
  onZoomIn, 
  onZoomOut, 
  onResetView,
  showTrails,
  onToggleTrails
}: MapControlsProps) {
  const [isLayerMenuOpen, setIsLayerMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsLayerMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const currentLayerName = MAP_LAYERS.find(layer => layer.id === currentLayer)?.name || 'Satellite';

  return (
    <div className="absolute bottom-4 right-4 z-[1000]">
      {/* Layer selection dropdown */}
      {isLayerMenuOpen && (
        <div ref={menuRef} className="absolute bottom-full right-0 mb-2 bg-card border rounded-lg shadow-lg p-2 min-w-40">
          <div className="text-xs font-semibold text-muted-foreground mb-2 px-1">Map Layers</div>
          <div className="space-y-1">
            {MAP_LAYERS.map((layer) => (
              <button
                key={layer.id}
                onClick={() => {
                  onLayerChange(layer.id);
                  setIsLayerMenuOpen(false);
                }}
                className={`w-full text-left px-2 py-1.5 text-sm rounded transition-colors ${
                  currentLayer === layer.id
                    ? 'bg-primary text-primary-foreground'
                    : 'hover:bg-muted text-muted-foreground hover:text-foreground'
                }`}
              >
                {layer.name}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Compact control panel */}
      <div className="bg-card border rounded-lg shadow-lg">
        <div className="flex flex-col">
          {/* Zoom In */}
          <button
            onClick={onZoomIn}
            className="px-3 py-2 text-lg font-bold hover:bg-muted transition-colors border-b rounded-t-lg"
            title="Zoom In"
          >
            +
          </button>
          
          {/* Zoom Out */}
          <button
            onClick={onZoomOut}
            className="px-3 py-2 text-lg font-bold hover:bg-muted transition-colors border-b"
            title="Zoom Out"
          >
            −
          </button>
          
          {/* Reset View */}
          <button
            onClick={onResetView}
            className="px-3 py-1.5 text-sm font-semibold hover:bg-muted transition-colors border-b"
            title="Reset View"
          >
            ⌂
          </button>

          {/* Toggle Trails */}
          <button
            onClick={onToggleTrails}
            className={`px-3 py-2 hover:bg-muted transition-colors border-b flex items-center justify-center ${
              showTrails ? 'bg-blue-100 text-blue-700' : ''
            }`}
            title={showTrails ? "Hide Aircraft Trails" : "Show Aircraft Trails"}
          >
            {showTrails ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
          
          {/* Layer Menu Toggle */}
          <button
            onClick={() => setIsLayerMenuOpen(!isLayerMenuOpen)}
            className={`px-3 py-2 hover:bg-muted transition-colors rounded-b-lg flex items-center justify-center ${
              isLayerMenuOpen ? 'bg-muted' : ''
            }`}
            title={`Current: ${currentLayerName}`}
          >
            <MoreVertical className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}

interface MapControllerProps {
  airport: AirportResponse | null;
  onZoomIn: React.MutableRefObject<() => void>;
  onZoomOut: React.MutableRefObject<() => void>;
  onResetView: React.MutableRefObject<() => void>;
}

/**
 * Map controller that handles view positioning and runways
 * 
 * Benefits of our monorepo structure shown here:
 * - Airport coordinates extracted from API response using webapp types
 * - Runway data automatically available and properly typed
 * - No need to maintain separate coordinate databases
 */
function MapController({ airport, onZoomIn, onZoomOut, onResetView }: MapControllerProps) {
  const map = useMap();

  useEffect(() => {
    const coordinates = getAirportCoordinates(airport) || [...MAP_CONFIG.DEFAULT_CENTER];
    const zoom = airport ? MAP_CONFIG.AIRPORT_ZOOM : MAP_CONFIG.DEFAULT_ZOOM;
    map.setView(coordinates, zoom);
  }, [airport, map]);

  useEffect(() => {
    const handleZoomIn = () => map.zoomIn();
    const handleZoomOut = () => map.zoomOut();
    const handleResetView = () => {
      const coordinates = getAirportCoordinates(airport) || [...MAP_CONFIG.DEFAULT_CENTER];
      const zoom = airport ? MAP_CONFIG.AIRPORT_ZOOM : MAP_CONFIG.DEFAULT_ZOOM;
      map.setView(coordinates, zoom);
    };

    // Update the callback refs
    onZoomIn.current = handleZoomIn;
    onZoomOut.current = handleZoomOut;
    onResetView.current = handleResetView;
  }, [map, airport, onZoomIn, onZoomOut, onResetView]);

  return null;
}

/**
 * Component to track map bounds and update aircraft positions
 */
function MapBoundsTracker({ onBoundsChange }: { onBoundsChange: (bounds: { north: number; east: number; south: number; west: number }) => void }) {
  const [lastBounds, setLastBounds] = useState<{ north: number; east: number; south: number; west: number } | null>(null);

  const map = useMapEvents({
    moveend: () => updateBounds(),
    zoomend: () => updateBounds(),
  });

  const updateBounds = useCallback(() => {
    const bounds = map.getBounds();
    const newBounds = {
      north: bounds.getNorth(),
      east: bounds.getEast(),
      south: bounds.getSouth(),
      west: bounds.getWest(),
    };

    // Only update if bounds have changed significantly (to avoid micro-movements)
    if (!lastBounds || 
        Math.abs(newBounds.north - lastBounds.north) > MAP_CONFIG.BOUNDS_THRESHOLD ||
        Math.abs(newBounds.east - lastBounds.east) > MAP_CONFIG.BOUNDS_THRESHOLD ||
        Math.abs(newBounds.south - lastBounds.south) > MAP_CONFIG.BOUNDS_THRESHOLD ||
        Math.abs(newBounds.west - lastBounds.west) > MAP_CONFIG.BOUNDS_THRESHOLD) {
      setLastBounds(newBounds);
      onBoundsChange(newBounds);
    }
  }, [map, onBoundsChange, lastBounds]);

  // Set initial bounds
  useEffect(() => {
    const timer = setTimeout(() => {
      updateBounds();
    }, MAP_CONFIG.BOUNDS_UPDATE_DELAY);

    return () => clearTimeout(timer);
  }, [updateBounds]);

  return null;
}

/**
 * Component to render runways on the map
 * Demonstrates how our webapp types ensure runway data is properly structured
 */
function RunwayOverlays({ airport }: { airport: AirportResponse | null }) {
  if (!airport || !airport.runways) return null;

  return (
    <>
      {airport.runways.map((runway) => {
        // Check if runway has centerline data (future enhancement)
        const runwayWithCenterline = runway as RunwayResponse & { centerline?: string };
        const coordinates = parseRunwayCenterline(runwayWithCenterline.centerline);
        
        if (coordinates.length === 0) return null;

        return (
          <Polyline
            key={`${airport.icao}-${runway.name}`}
            positions={coordinates}
            pathOptions={{
              color: '#3b82f6',
              weight: 4,
              opacity: 0.8,
            }}
          >
            {/* TODO: Add popup with runway details */}
          </Polyline>
        );
      })}
    </>
  );
}

interface AirportMapProps {
  airport: AirportResponse | null;
  className?: string;
}

/**
 * Enhanced AirportMap component that uses real API data
 * 
 * Key improvements from monorepo structure:
 * - No hardcoded coordinates - uses API data
 * - Displays actual runways from database
 * - Type-safe throughout thanks to webapp types
 * - Automatically updates when airport data changes
 */
function AirportMapComponent({ airport, className = '' }: AirportMapProps) {
  const [currentLayer, setCurrentLayer] = useState('satellite');
  const [mapBounds, setMapBounds] = useState<{ north: number; east: number; south: number; west: number } | null>(null);
  const [showTrails, setShowTrails] = useState(true);
  const onZoomIn = useRef<() => void>(() => {});
  const onZoomOut = useRef<() => void>(() => {});
  const onResetView = useRef<() => void>(() => {});

  // Fetch aircraft positions for the current map bounds
  const { positions: aircraftPositions, loading: aircraftLoading, error: aircraftError } = useAircraftPositions({
    bounds: mapBounds || undefined,
    enabled: !!mapBounds, // Only fetch when we have bounds
  });

  const currentLayerConfig = MAP_LAYERS.find(layer => layer.id === currentLayer) || MAP_LAYERS[0];
  const coordinates = getAirportCoordinates(airport) || [...MAP_CONFIG.DEFAULT_CENTER];
  const zoom = airport ? MAP_CONFIG.AIRPORT_ZOOM : MAP_CONFIG.DEFAULT_ZOOM;

  const handleBoundsChange = useCallback((bounds: { north: number; east: number; south: number; west: number }) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🗺️ Map bounds changed:', bounds);
    }
    setMapBounds(bounds);
  }, []);

  const handleToggleTrails = () => {
    setShowTrails(!showTrails);
  };

  return (
    <div className={`relative ${className}`}>
      <MapContainer
        center={coordinates}
        zoom={zoom}
        className="h-full w-full rounded-lg"
        zoomControl={false}
        attributionControl={false}
      >
        <TileLayer
          url={currentLayerConfig.url}
          attribution={currentLayerConfig.attribution}
          maxZoom={currentLayerConfig.maxZoom}
        />
        
        {/* Airport marker - only show if we have coordinates */}
        {airport && getAirportCoordinates(airport) && (
          <Marker position={getAirportCoordinates(airport)!} />
        )}

        {/* Runway overlays */}
        <RunwayOverlays airport={airport} />

        {/* Aircraft trails */}
        <AircraftTrails positions={aircraftPositions} showTrails={showTrails} />

        {/* Aircraft markers */}
        <AircraftMarkers positions={aircraftPositions} />

        {/* Aircraft labels */}
        <AircraftLabels positions={aircraftPositions} />

        <MapController
          airport={airport}
          onZoomIn={onZoomIn}
          onZoomOut={onZoomOut}
          onResetView={onResetView}
        />

        <MapBoundsTracker
          onBoundsChange={handleBoundsChange}
        />
      </MapContainer>

      <MapControls
        currentLayer={currentLayer}
        onLayerChange={setCurrentLayer}
        onZoomIn={() => onZoomIn.current()}
        onZoomOut={() => onZoomOut.current()}
        onResetView={() => onResetView.current()}
        showTrails={showTrails}
        onToggleTrails={handleToggleTrails}
      />

      {/* Aircraft status indicator */}
      <div className="absolute top-4 left-4 z-[1000] bg-card border rounded-lg shadow-lg p-2 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
          <span>
            Aircraft: {aircraftLoading ? 'Loading...' : `${aircraftPositions.length}`}
          </span>
        </div>
        <div className="flex items-center space-x-2 mt-1">
          <div className={`w-2 h-2 rounded-full ${showTrails ? 'bg-green-500' : 'bg-gray-400'}`}></div>
          <span className="text-xs">
            Trails: {showTrails ? 'Visible' : 'Hidden'}
          </span>
        </div>
        {aircraftError && (
          <div className="text-red-500 text-xs mt-1">
            Error: {aircraftError}
          </div>
        )}
      </div>
    </div>
  );
}

export default AirportMapComponent; 