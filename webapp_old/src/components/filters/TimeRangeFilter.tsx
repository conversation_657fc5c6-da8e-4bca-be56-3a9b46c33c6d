import { Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface TimeRangeFilterProps {
  dateTimeFrom: string;
  dateTimeTo: string;
  onChange: (updates: { dateTimeFrom?: string; dateTimeTo?: string }) => void;
  onSetToNow: () => void;
}

export default function TimeRangeFilter({ 
  dateTimeFrom, 
  dateTimeTo, 
  onChange, 
  onSetToNow 
}: TimeRangeFilterProps) {
  return (
    <div className="space-y-2">
      <div className="grid grid-cols-2 gap-3">
        <div className="space-y-1">
          <Label htmlFor="datetime-from" className="text-sm">From Date & Time</Label>
          <Input
            id="datetime-from"
            type="datetime-local"
            value={dateTimeFrom}
            onChange={(e) => onChange({ dateTimeFrom: e.target.value })}
            className="text-sm"
          />
        </div>
        <div className="space-y-1">
          <Label htmlFor="datetime-to" className="text-sm">To Date & Time</Label>
          <Input
            id="datetime-to"
            type="datetime-local"
            value={dateTimeTo}
            onChange={(e) => onChange({ dateTimeTo: e.target.value })}
            className="text-sm"
          />
          <Button
            variant="secondary"
            size="sm"
            onClick={onSetToNow}
            className="w-full text-xs mt-1"
          >
            <Clock className="h-3 w-3 mr-1" />
            Set to Now (Local)
          </Button>
        </div>
      </div>
    </div>
  );
} 