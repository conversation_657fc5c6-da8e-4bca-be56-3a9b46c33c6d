import { MapPin, Edit } from 'lucide-react';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface SearchAreaFilterProps {
  searchMethod: 'radius' | 'draw';
  radiusCenter: [number, number] | null;
  radiusDistance: number;
  drawnPolygon: [number, number][] | null;
  onSearchMethodChange: (method: 'radius' | 'draw') => void;
  onDeletePolygon?: () => void;
  isRadiusConfirmed?: boolean;
  onModifyRadius?: () => void;
}

/**
 * Formats distance value for display
 */
const formatDistance = (distance: number): string => {
  return distance >= 1000 
    ? `${(distance / 1000).toFixed(1)} km`
    : `${distance} m`;
};

/**
 * Search area configuration component with radius and polygon drawing modes
 */
export default function SearchAreaFilter({
  searchMethod,
  radiusCenter,
  radiusDistance,
  drawnPolygon,
  onSearchMethodChange,
  onDeletePolygon,
  isRadiusConfirmed,
  onModifyRadius
}: SearchAreaFilterProps) {
  return (
    <div className="space-y-2">
      <Tabs value={searchMethod} onValueChange={(value) => onSearchMethodChange(value as 'radius' | 'draw')}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="radius">Search Radius</TabsTrigger>
          <TabsTrigger value="draw">Draw Search Area</TabsTrigger>
        </TabsList>

        {/* Radius Search Tab */}
        <TabsContent value="radius" className="space-y-2 mt-2">
          <div className="bg-muted/30 rounded-md p-3">
            <div className="flex items-center gap-2 mb-2">
              <MapPin className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-sm">Radius Search</span>
            </div>
            
            {radiusCenter ? (
              <div className="space-y-2">
                {/* Status badges */}
                <div className="space-y-1">
                  <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                    ✓ Center: {radiusCenter[0].toFixed(4)}, {radiusCenter[1].toFixed(4)}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    Radius: {formatDistance(radiusDistance)}
                  </Badge>
                  {isRadiusConfirmed && (
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-xs">
                      ✓ Confirmed
                    </Badge>
                  )}
                </div>
                
                {isRadiusConfirmed ? (
                  <div className="space-y-2">
                    <p className="text-xs text-muted-foreground">
                      Search radius confirmed. Ready to search.
                    </p>
                    {onModifyRadius && (
                      <Button
                        onClick={onModifyRadius}
                        size="sm"
                        variant="outline"
                        className="w-full"
                      >
                        Modify Radius
                      </Button>
                    )}
                  </div>
                ) : (
                  <p className="text-xs text-muted-foreground">
                    Use map controls to adjust radius and confirm search area.
                  </p>
                )}
              </div>
            ) : (
              <p className="text-xs text-muted-foreground">
                Click map to set center, then use map controls to adjust radius.
              </p>
            )}
          </div>
        </TabsContent>
        
        {/* Polygon Drawing Tab */}
        <TabsContent value="draw" className="space-y-2 mt-2">
          <div className="bg-muted/30 rounded-md p-3">
            <div className="flex items-center gap-2 mb-2">
              <Edit className="h-4 w-4 text-purple-600" />
              <span className="font-medium text-sm">Custom Area</span>
            </div>
            
            {drawnPolygon ? (
              <div className="space-y-2">
                <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                  ✓ Polygon with {drawnPolygon.length} points
                </Badge>
                {onDeletePolygon && (
                  <Button
                    onClick={onDeletePolygon}
                    size="sm"
                    variant="destructive"
                    className="w-full"
                  >
                    Delete Polygon
                  </Button>
                )}
              </div>
            ) : (
              <p className="text-xs text-muted-foreground">
                Use map to draw custom search area
              </p>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 