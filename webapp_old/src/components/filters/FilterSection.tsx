import { ChevronDown, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface FilterSectionProps {
  id: string;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  isRequired: boolean;
  isEnabled: boolean;
  isValid: boolean;
  isOpen: boolean;
  children: React.ReactNode;
  onToggleSection: () => void;
  onToggleEnabled: () => void;
}

export default function FilterSection({
  id,
  title,
  icon: Icon,
  isRequired,
  isEnabled,
  isValid,
  isOpen,
  children,
  onToggleSection,
  onToggleEnabled
}: FilterSectionProps) {
  const canExpand = isRequired || isEnabled;
  const isApplied = isRequired ? isValid : isEnabled;
  const showRequired = isRequired && !isValid;

  const handleEnableToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleEnabled();
    // Auto-expand when enabling a filter
    if (!isEnabled && !isOpen) {
      onToggleSection();
    }
  };

  const handleCheckboxChange = (checked: boolean) => {
    onToggleEnabled();
    // Auto-expand when enabling a filter
    if (!isEnabled && !isOpen) {
      onToggleSection();
    }
  };

  const StatusBadge = () => {
    if (isApplied) {
      return (
        <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
          Applied
        </Badge>
      );
    }
    
    if (showRequired) {
      return (
        <Badge variant="destructive" className="text-xs">
          Required
        </Badge>
      );
    }
    
    return null;
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader 
        className={`px-3 py-1.5 transition-colors ${
          canExpand ? 'cursor-pointer hover:bg-muted/50' : 'opacity-60'
        }`}
        onClick={canExpand ? onToggleSection : undefined}
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 flex-1">
            <Icon className="h-4 w-4 text-primary" />
            <h3 className="text-base font-semibold">{title}</h3>
            <StatusBadge />
          </div>
          
          {!isRequired && (
            <div className="flex items-center gap-2">
              <Checkbox
                checked={isEnabled}
                onCheckedChange={handleCheckboxChange}
                className="h-4 w-4"
                onClick={(e) => e.stopPropagation()}
              />
              <Label 
                className="text-xs text-muted-foreground cursor-pointer"
                onClick={handleEnableToggle}
              >
                Enable
              </Label>
            </div>
          )}
          
          {isRequired && (
            <div className="flex items-center gap-2">
              <Checkbox
                checked={true}
                disabled={true}
                className="opacity-50 h-4 w-4"
              />
              <Label className="text-xs text-muted-foreground">
                Required
              </Label>
            </div>
          )}
          
          {canExpand && (
            isOpen ? (
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-4 w-4 text-muted-foreground" />
            )
          )}
        </div>
      </CardHeader>
      
      {isOpen && canExpand && (
        <CardContent className="px-3 pb-2 pt-0">
          {children}
        </CardContent>
      )}
    </Card>
  );
} 