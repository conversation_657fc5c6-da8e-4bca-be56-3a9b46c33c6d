import { Plane } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface AircraftDetailsFilterProps {
  occurrenceType: string;
  aircraftClass: string;
  registration: string;
  squawk: string;
  onChange: (updates: {
    occurrenceType?: string;
    aircraftClass?: string;
    registration?: string;
    squawk?: string;
  }) => void;
}

export default function AircraftDetailsFilter({
  occurrenceType,
  aircraftClass,
  registration,
  squawk,
  onChange
}: AircraftDetailsFilterProps) {
  const occurrenceTypeOptions = [
    { value: 'near-miss', label: 'Near Miss' },
    { value: 'ground-collision', label: 'Ground Collision' },
    { value: 'weather', label: 'Weather Related' },
    { value: 'mechanical', label: 'Mechanical Failure' },
    { value: 'runway-incursion', label: 'Runway Incursion' }
  ];

  const aircraftClassOptions = [
    { value: 'commercial', label: 'Commercial' },
    { value: 'private', label: 'Private' },
    { value: 'cargo', label: 'Cargo' },
    { value: 'military', label: 'Military' }
  ];

  return (
    <div className="space-y-2">
      <div className="grid grid-cols-2 gap-3">
        <div className="space-y-1">
          <Label htmlFor="occurrence-type" className="text-sm">Occurrence Type</Label>
          <Select 
            value={occurrenceType || undefined} 
            onValueChange={(value) => onChange({ occurrenceType: value || '' })}
          >
            <SelectTrigger id="occurrence-type" className="text-sm">
              <SelectValue placeholder="Select occurrence type" />
            </SelectTrigger>
            <SelectContent>
              {occurrenceTypeOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-1">
          <Label htmlFor="aircraft-class" className="text-sm">Aircraft Class</Label>
          <Select 
            value={aircraftClass || undefined} 
            onValueChange={(value) => onChange({ aircraftClass: value || '' })}
          >
            <SelectTrigger id="aircraft-class" className="text-sm">
              <SelectValue placeholder="Select aircraft class" />
            </SelectTrigger>
            <SelectContent>
              {aircraftClassOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div className="space-y-1">
          <Label htmlFor="registration" className="text-sm">Registration</Label>
          <Input
            id="registration"
            placeholder="e.g., N123AB, G-ABCD"
            value={registration}
            onChange={(e) => onChange({ registration: e.target.value })}
            className="text-sm"
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="squawk" className="text-sm">Squawk Code</Label>
          <Input
            id="squawk"
            placeholder="e.g., 1200, 7700"
            value={squawk}
            onChange={(e) => onChange({ squawk: e.target.value })}
            className="text-sm"
          />
        </div>
      </div>
    </div>
  );
} 