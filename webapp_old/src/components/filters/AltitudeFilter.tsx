import { Mountain } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';

interface FlightEvents {
  landing: boolean;
  takeoff: boolean;
  goAround: boolean;
  touchAndGo: boolean;
}

interface AltitudeFilterProps {
  altitudeFilterMode: 'groundAirborne' | 'flightEvents';
  includeGroundAircraft: boolean;
  includeAirborneAircraft: boolean;
  minAltitude: number;
  maxAltitude: number;
  flightEvents: FlightEvents;
  onChange: (updates: {
    altitudeFilterMode?: 'groundAirborne' | 'flightEvents';
    includeGroundAircraft?: boolean;
    includeAirborneAircraft?: boolean;
    minAltitude?: number;
    maxAltitude?: number;
    flightEvents?: FlightEvents;
  }) => void;
}

export default function AltitudeFilter({
  altitudeFilterMode,
  includeGroundAircraft,
  includeAirborneAircraft,
  minAltitude,
  maxAltitude,
  flightEvents,
  onChange
}: AltitudeFilterProps) {
  const handleModeChange = (mode: 'groundAirborne' | 'flightEvents') => {
    if (mode === 'groundAirborne') {
      onChange({ 
        altitudeFilterMode: mode,
        flightEvents: {
          landing: false,
          takeoff: false,
          goAround: false,
          touchAndGo: false
        }
      });
    } else {
      onChange({ 
        altitudeFilterMode: mode,
        includeGroundAircraft: false,
        includeAirborneAircraft: false
      });
    }
  };

  const handleFlightEventChange = (event: keyof FlightEvents, checked: boolean) => {
    onChange({
      flightEvents: {
        ...flightEvents,
        [event]: checked
      }
    });
  };

  const flightEventOptions = [
    { key: 'landing' as const, label: 'Landing', description: 'Aircraft descending and touching down on runway' },
    { key: 'takeoff' as const, label: 'Takeoff', description: 'Aircraft departing from runway and gaining altitude' },
    { key: 'goAround' as const, label: 'Go Around', description: 'Aborted landing approach, climbing away from runway' },
    { key: 'touchAndGo' as const, label: 'Touch and Go', description: 'Brief runway contact followed by immediate takeoff' }
  ];

  return (
    <div className="space-y-3">
      <Tabs value={altitudeFilterMode} onValueChange={(value) => handleModeChange(value as 'groundAirborne' | 'flightEvents')}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="groundAirborne">Ground & Airborne</TabsTrigger>
          <TabsTrigger value="flightEvents">Flight Events</TabsTrigger>
        </TabsList>

        <TabsContent value="groundAirborne" className="space-y-3 mt-3">
          <div className={`cursor-pointer transition-all rounded-md p-3 border ${
            includeGroundAircraft ? 'ring-2 ring-primary bg-primary/5 border-primary' : 'hover:bg-muted/50 border-border'
          }`}>
            <div className="flex items-center space-x-3" onClick={() => onChange({ includeGroundAircraft: !includeGroundAircraft })}>
              <Checkbox 
                checked={includeGroundAircraft}
                onCheckedChange={() => {}} // Handled by parent onClick
                className="h-4 w-4"
              />
              <div>
                <h4 className="text-sm font-medium">Ground Aircraft</h4>
                <p className="text-xs text-muted-foreground">Include aircraft on the ground (0 ft altitude)</p>
              </div>
            </div>
          </div>

          <div className={`cursor-pointer transition-all rounded-md p-3 border ${
            includeAirborneAircraft ? 'ring-2 ring-primary bg-primary/5 border-primary' : 'hover:bg-muted/50 border-border'
          }`}>
            <div className="flex items-center space-x-3" onClick={() => onChange({ includeAirborneAircraft: !includeAirborneAircraft })}>
              <Checkbox 
                checked={includeAirborneAircraft}
                onCheckedChange={() => {}} // Handled by parent onClick
                className="h-4 w-4"
              />
              <div className="flex-1">
                <h4 className="text-sm font-medium">Airborne Aircraft</h4>
                <p className="text-xs text-muted-foreground">Include aircraft in flight</p>
              </div>
            </div>
            
            {includeAirborneAircraft && (
              <div className="mt-3 pt-3 border-t space-y-3" onClick={(e) => e.stopPropagation()}>
                <div className="flex items-center justify-between">
                  <Label className="text-sm">Altitude Range</Label>
                  <span className="text-xs text-muted-foreground">
                    {includeGroundAircraft 
                      ? `Up to ${maxAltitude.toLocaleString()} ft` 
                      : `${minAltitude.toLocaleString()} - ${maxAltitude.toLocaleString()} ft`
                    }
                  </span>
                </div>
                
                {!includeGroundAircraft && (
                  <div className="space-y-2">
                    <Label className="text-xs">Minimum Altitude: {minAltitude.toLocaleString()} ft</Label>
                    <Slider
                      value={[minAltitude]}
                      onValueChange={([value]) => onChange({ 
                        minAltitude: value,
                        maxAltitude: Math.max(value, maxAltitude)
                      })}
                      max={50000}
                      step={100}
                      className="w-full"
                    />
                  </div>
                )}
                
                <div className="space-y-2">
                  <Label className="text-xs">Maximum Altitude: {maxAltitude.toLocaleString()} ft</Label>
                  <Slider
                    value={[maxAltitude]}
                    onValueChange={([value]) => onChange({ 
                      maxAltitude: value,
                      minAltitude: includeGroundAircraft ? 0 : Math.min(minAltitude, value)
                    })}
                    max={50000}
                    step={100}
                    className="w-full"
                  />
                </div>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="flightEvents" className="space-y-3 mt-3">
          <p className="text-xs text-muted-foreground mb-3">
            Select specific flight events to filter by. These events have inherent altitude characteristics.
          </p>
          
          {flightEventOptions.map(event => (
            <div 
              key={event.key}
              className={`cursor-pointer transition-all rounded-md p-3 border ${
                flightEvents[event.key] ? 'ring-2 ring-primary bg-primary/5 border-primary' : 'hover:bg-muted/50 border-border'
              }`}
            >
              <div className="flex items-center space-x-3" onClick={() => handleFlightEventChange(event.key, !flightEvents[event.key])}>
                <Checkbox 
                  checked={flightEvents[event.key]}
                  onCheckedChange={() => {}} // Handled by parent onClick
                  className="h-4 w-4"
                />
                <div>
                  <h4 className="text-sm font-medium">{event.label}</h4>
                  <p className="text-xs text-muted-foreground">{event.description}</p>
                </div>
              </div>
            </div>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
} 