'use client';

import { <PERSON><PERSON>, Popup } from 'react-leaflet';
import L from 'leaflet';
import { Plane, TrendingUp, TrendingDown } from 'lucide-react';
import { AircraftPositionResponse, FlightPhaseEnum } from '../types/api';
import { getAircraftIcon, createSvgIcon } from '../lib/aircraftIcons';
import { AIRCRAFT_CONFIG, getAltitudeColor, getAircraftIconSize, getAltitudeZIndex } from '../lib/aircraft-config';
import { getLatLngFromH3 } from '../lib/utils/coordinates';
import { formatAltitude, formatSpeed, formatTrack } from '../lib/utils/formatting';
import { UI_CONFIG } from '../lib/constants';

interface AircraftMarkersProps {
  positions: AircraftPositionResponse[];
}

// Helper function to safely get type_code (temporary until officially added to API)
const getTypeCode = (position: AircraftPositionResponse): string | undefined => {
  return (position as AircraftPositionResponse & { type_code?: string }).type_code;
};

// Create custom aircraft icon using tar1090-inspired SVG shapes
const createAircraftIcon = (
  position: AircraftPositionResponse,
  rotation: number = 0,
  isGround: boolean = false
) => {
  const [shapeName, scale] = getAircraftIcon(
    position.category,
    getTypeCode(position),
    undefined,
    undefined
  );

  const color = getAltitudeColor(position.alt_baro, isGround);
  const svgDataUrl = createSvgIcon(shapeName, rotation, color, scale, isGround);
  const iconSize = getAircraftIconSize(scale);

  return L.icon({
    iconUrl: svgDataUrl,
    iconSize: [iconSize, iconSize],
    iconAnchor: [iconSize / 2, iconSize / 2],
    popupAnchor: [0, -iconSize / 2],
    className: 'aircraft-marker-icon',
  });
};

// Get aircraft type display name
const getAircraftTypeDisplay = (position: AircraftPositionResponse): string => {
  const typeCode = getTypeCode(position);
  if (typeCode) return typeCode;
  if (position.category) return `Cat ${position.category}`;
  return 'Unknown';
};

export function AircraftMarkers({ positions }: AircraftMarkersProps) {
  // Limit aircraft display for performance
  const limitedPositions = positions.slice(0, AIRCRAFT_CONFIG.MAX_AIRCRAFT_DISPLAY);
  
  if (process.env.NODE_ENV === 'development' && positions.length > AIRCRAFT_CONFIG.MAX_AIRCRAFT_DISPLAY) {
    console.warn(`⚠️ Limiting aircraft display to ${AIRCRAFT_CONFIG.MAX_AIRCRAFT_DISPLAY} out of ${positions.length} aircraft for performance`);
  }

  return (
    <>
      {limitedPositions.map((position) => {
        const latLng = getLatLngFromH3(position.h3_index);
        if (!latLng) return null;
        
        const [lat, lng] = latLng;
        const isGround = position.flight_phase === FlightPhaseEnum.GROUND;
        const track = position.track || 0;
        const zIndex = getAltitudeZIndex(position.alt_baro, isGround);
        
        return (
          <Marker
            key={position.hex}
            position={[lat, lng]}
            icon={createAircraftIcon(position, track, isGround)}
            zIndexOffset={zIndex}
          >
            <Popup className="aircraft-popup">
              <div className="space-y-3" style={{ minWidth: UI_CONFIG.POPUP_MIN_WIDTH }}>
                {/* Header */}
                <div className="flex items-center justify-between border-b pb-2">
                  <div>
                    <h3 className="font-semibold text-lg text-gray-900">
                      {position.flight || position.hex}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {getAircraftTypeDisplay(position)}
                    </p>
                  </div>
                  <div className="flex items-center text-sm">
                    <Plane className="w-4 h-4 mr-1 text-blue-600" />
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      isGround 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {position.flight_phase}
                    </span>
                  </div>
                </div>
                
                {/* Flight data grid */}
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="space-y-2">
                    <div>
                      <span className="font-medium text-gray-700">Hex:</span>
                      <div className="text-gray-900 font-mono">{position.hex}</div>
                    </div>
                    {position.squawk && (
                      <div>
                        <span className="font-medium text-gray-700">Squawk:</span>
                        <div className="text-gray-900 font-mono">{position.squawk}</div>
                      </div>
                    )}
                    <div>
                      <span className="font-medium text-gray-700">Altitude:</span>
                      <div className="text-gray-900">{formatAltitude(position.alt_baro)}</div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div>
                      <span className="font-medium text-gray-700">Speed:</span>
                      <div className="text-gray-900">{formatSpeed(position.gs)}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Track:</span>
                      <div className="text-gray-900">{formatTrack(position.track)}</div>
                    </div>
                    {position.category && (
                      <div>
                        <span className="font-medium text-gray-700">Category:</span>
                        <div className="text-gray-900">{position.category}</div>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Vertical rate indicator */}
                {position.baro_rate && Math.abs(position.baro_rate) > UI_CONFIG.VERTICAL_RATE_THRESHOLD && (
                  <div className="flex items-center text-sm border-t pt-2">
                    {position.baro_rate > 0 ? (
                      <TrendingUp className="w-4 h-4 mr-2 text-green-600" />
                    ) : (
                      <TrendingDown className="w-4 h-4 mr-2 text-red-600" />
                    )}
                    <span className="font-medium text-gray-700">Vertical Rate:</span>
                    <span className={`ml-1 font-medium ${
                      position.baro_rate > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {position.baro_rate > 0 ? '+' : ''}{position.baro_rate} ft/min
                    </span>
                  </div>
                )}
                
                {/* Footer with timestamp and trace info */}
                <div className="text-xs text-gray-500 border-t pt-2">
                  <div className="flex justify-between items-center">
                    <span>Last seen:</span>
                    <span className="font-mono">
                      {new Date(position.time).toLocaleTimeString()}
                    </span>
                  </div>
                  {position.trace && position.trace.length > 0 && (
                    <div className="flex justify-between items-center mt-1">
                      <span>Trail points:</span>
                      <span className="font-mono">
                        {position.trace.length}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </Popup>
          </Marker>
        );
      })}
    </>
  );
}

export default AircraftMarkers; 