// Central configuration for aircraft tracking and map display
export const AIRCRAFT_CONFIG = {
  // Simple refresh settings
  REFRESH_INTERVAL_MS: 5000, // Fetch aircraft positions every 5 seconds
  DEFAULT_LOOKBACK_SECONDS: 40, // How far back to look for aircraft positions
  
  // Map interaction settings
  BOUNDS_DEBOUNCE_DELAY: 1000, // Wait 1 second after bounds stop changing before fetching
  
  // Performance settings
  MAX_AIRCRAFT_DISPLAY: 500, // Maximum aircraft to display on map (prevent performance issues)
  ICON_CACHE_SIZE: 200, // Maximum number of cached aircraft icons
  
  // Trail settings
  MAX_TRAIL_LENGTH: 50, // Maximum number of trace points to display per aircraft
  MIN_TRAIL_POINTS: 2, // Minimum points needed to show a trail
  TRAIL_WEIGHT: 2, // Line thickness for trails
  TRAIL_DASH_PATTERN: '5, 5', // Dash pattern for trails
  
  // Visual settings
  AIRCRAFT_ICON_SIZES: {
    small: 36,
    medium: 48,
    large: 60
  },
  
  // Altitude-based color scheme (in feet)
  ALTITUDE_COLORS: {
    ground: '#6b7280',    // Gray
    low: '#ff6b6b',       // Red (< 2000 ft)
    medium: '#feca57',    // Yellow (2000-10000 ft)
    high: '#48dbfb',      // Light blue (10000-25000 ft)
    veryHigh: '#ff9ff3'   // Pink (> 25000 ft)
  },
  
  // Trail opacity based on length
  TRAIL_OPACITY: {
    short: 0.8,   // < 10 points
    medium: 0.7,  // 10-20 points
    long: 0.6     // > 20 points
  },
  
  // Speed thresholds (in knots)
  SPEED_THRESHOLDS: {
    stationary: 5,        // Below this is considered stationary
    slow: 50,             // Slow aircraft
    normal: 200,          // Normal cruising speed
    fast: 400             // High speed aircraft
  }
} as const;

// Type for refresh interval validation
export type RefreshInterval = number;

// Helper function to validate and clamp refresh interval
export function validateRefreshInterval(interval: number): RefreshInterval {
  return Math.max(interval, 1000); // Minimum 1 second
}

// Helper function to get altitude color
export function getAltitudeColor(altitude?: number, isOnGround: boolean = false): string {
  if (isOnGround) return AIRCRAFT_CONFIG.ALTITUDE_COLORS.ground;
  if (!altitude) return AIRCRAFT_CONFIG.ALTITUDE_COLORS.high;
  
  if (altitude < 2000) return AIRCRAFT_CONFIG.ALTITUDE_COLORS.low;
  if (altitude < 10000) return AIRCRAFT_CONFIG.ALTITUDE_COLORS.medium;
  if (altitude < 25000) return AIRCRAFT_CONFIG.ALTITUDE_COLORS.high;
  return AIRCRAFT_CONFIG.ALTITUDE_COLORS.veryHigh;
}

// Helper function to determine aircraft size based on type and category
export function getAircraftIconSize(scale: number = 1): number {
  const baseSize = AIRCRAFT_CONFIG.AIRCRAFT_ICON_SIZES.medium;
  const scaledSize = baseSize * scale;
  
  return Math.min(
    Math.max(scaledSize, AIRCRAFT_CONFIG.AIRCRAFT_ICON_SIZES.small),
    AIRCRAFT_CONFIG.AIRCRAFT_ICON_SIZES.large
  );
}

// Helper function to calculate z-index based on altitude
// Higher altitude aircraft should appear on top when overlapping
export function getAltitudeZIndex(altitude?: number, isOnGround: boolean = false): number {
  const baseZIndex = 400; // Base z-index for aircraft
  
  if (isOnGround) return baseZIndex; // Ground aircraft at base level
  if (!altitude) return baseZIndex + 100; // Unknown altitude gets medium priority
  
  // Scale altitude to z-index (every 1000ft = +1 z-index)
  return baseZIndex + Math.floor(altitude / 1000);
} 