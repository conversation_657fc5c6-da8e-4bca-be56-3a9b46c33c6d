import { useState, useEffect, useCallback } from 'react';
import { apiClient } from '../api-client';
import { RunwayEventResponse } from '../../types/api';

interface UseAirportEventsReturn {
  events: RunwayEventResponse[];
  lastUpdated: Date | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

interface UseAirportEventsOptions {
  /**
   * Polling interval in milliseconds
   * @default 5000 (5 seconds)
   */
  pollingInterval?: number;
  
  
  /**
   * Whether to start polling automatically
   * @default true
   */
  autoStart?: boolean;
  
  
  /**
   * Time range to fetch events for (in milliseconds)
   * @default 3600000 (1 hour)
   */
  timeRange?: number;
}

/**
 * Custom hook for fetching airport events with automatic polling
 * 
 * Fetches takeoff and landing events from the last hour and automatically
 * refreshes every 5 seconds to show real-time activity.
 */
export function useAirportEvents(
  icao: string, 
  options: UseAirportEventsOptions = {}
): UseAirportEventsReturn {
  const { 
    pollingInterval = 5000, // 5 seconds
    autoStart = true,
    timeRange = 3600000 // 1 hour in milliseconds
  } = options;

  const [events, setEvents] = useState<RunwayEventResponse[]>([]);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchEvents = useCallback(async () => {
    if (!icao) {
      setError('ICAO code is required');
      setLoading(false);
      return;
    }

    try {
      // Calculate time range (default: last hour)
      const endTime = new Date();
      const startTime = new Date(endTime.getTime() - timeRange);
      
      console.log(`🛫 Fetching events for ${icao} from ${startTime.toISOString()} to ${endTime.toISOString()}`);
      
      const response = await apiClient.getAirportEvents(icao, {
        start_time: startTime.toISOString(),
        end_time: endTime.toISOString()
      });
      
      if (response.data) {
        setEvents(response.data.events);
        setLastUpdated(new Date());
        setError(null);
        console.log(`✅ Fetched ${response.data.events.length} events for ${icao}`);
      } else if (response.error) {
        const errorMessage = typeof response.error.detail === 'string' 
          ? response.error.detail 
          : response.error.error;
        setError(errorMessage);
        console.error(`❌ Error fetching events for ${icao}:`, errorMessage);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch airport events';
      setError(errorMessage);
      console.error(`❌ Exception fetching events for ${icao}:`, err);
    } finally {
      setLoading(false);
    }
  }, [icao, timeRange]);

  // Initial fetch
  useEffect(() => {
    if (autoStart) {
      fetchEvents();
    }
  }, [fetchEvents, autoStart]);

  // Set up polling
  useEffect(() => {
    if (!autoStart || !icao) return;

    const intervalId = setInterval(() => {
      // Only poll if not currently loading (avoid overlapping requests)
      setLoading(false); // Don't show loading spinner for polling updates
      fetchEvents();
    }, pollingInterval);

    return () => clearInterval(intervalId);
  }, [fetchEvents, pollingInterval, autoStart, icao]);

  const refetch = useCallback(async () => {
    setLoading(true);
    await fetchEvents();
  }, [fetchEvents]);

  return {
    events,
    lastUpdated,
    loading,
    error,
    refetch
  };
} 