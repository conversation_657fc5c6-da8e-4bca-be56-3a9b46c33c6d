import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { AircraftPositionResponse, OptimizedAircraftPosition } from '../../types/api';
import { apiClient } from '../api-client';
import { API_CONFIG, MAP_CONFIG } from '../constants';

interface MapBounds {
  north: number;
  east: number;
  south: number;
  west: number;
}

interface UseAircraftPositionsOptions {
  bounds?: MapBounds;
  enabled?: boolean;
  lookbackSeconds?: number;
}

interface UseAircraftPositionsReturn {
  positions: AircraftPositionResponse[];
  loading: boolean;
  error: string | null;
  lastUpdate: Date | null;
  refetch: () => Promise<void>;
}

export function useAircraftPositions(options: UseAircraftPositionsOptions = {}): UseAircraftPositionsReturn {
  const {
    bounds,
    enabled = true,
    lookbackSeconds = API_CONFIG.DEFAULT_LOOKBACK_SECONDS
  } = options;

  const [positions, setPositions] = useState<AircraftPositionResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Track active request and timer
  const activeRequestRef = useRef<AbortController | null>(null);
  const isActiveRef = useRef(true);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup on unmount
  useEffect(() => {
    isActiveRef.current = true;
    return () => {
      isActiveRef.current = false;
      if (activeRequestRef.current) {
        activeRequestRef.current.abort();
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Memoize bounds to prevent unnecessary re-fetches
  const memoizedBounds = useMemo(() => {
    if (!bounds) return null;
    return {
      north: Number(bounds.north.toFixed(MAP_CONFIG.BOUNDS_PRECISION)),
      east: Number(bounds.east.toFixed(MAP_CONFIG.BOUNDS_PRECISION)),
      south: Number(bounds.south.toFixed(MAP_CONFIG.BOUNDS_PRECISION)),
      west: Number(bounds.west.toFixed(MAP_CONFIG.BOUNDS_PRECISION)),
    };
  }, [bounds]);

  // Simple fetch function
  const fetchPositions = useCallback(async () => {
    if (!enabled || !isActiveRef.current) return;

    // Prevent overlapping requests
    if (activeRequestRef.current) {
      if (process.env.NODE_ENV === 'development') {
        console.log('⏳ Request already in progress, skipping');
      }
      return;
    }

    // Create abort controller
    const abortController = new AbortController();
    activeRequestRef.current = abortController;
    
    setLoading(true);
    setError(null);

    try {
      const params: Record<string, string | number> = {
        lookback_seconds: lookbackSeconds,
      };

      // Add bounds if provided
      if (memoizedBounds) {
        params.north = memoizedBounds.north;
        params.east = memoizedBounds.east;
        params.south = memoizedBounds.south;
        params.west = memoizedBounds.west;
      }

      // Try optimized endpoint first, fallback to standard
      let response;
      try {
        response = await apiClient.getAircraftPositionsAtTimeOptimized(params);
        if (response.error) {
          throw new Error('Optimized endpoint failed');
        }
      } catch {
        if (process.env.NODE_ENV === 'development') {
          console.log('⚠️ Optimized endpoint failed, using standard endpoint');
        }
        response = await apiClient.getAircraftPositionsAtTime(params);
      }

      // Check if still active
      if (!isActiveRef.current || abortController.signal.aborted) {
        return;
      }

      if (response.data) {
        // Convert optimized positions to standard format if needed
        const rawPositions = response.data.positions;
        const timestamp = response.data.timestamp;
        
        // Add timestamp to positions if they don't have it (optimized format)
        const positions: AircraftPositionResponse[] = rawPositions.map((pos: AircraftPositionResponse | OptimizedAircraftPosition) => ({
          ...pos,
          time: 'time' in pos ? pos.time : timestamp // Use existing time or add timestamp
        }));
        
        setPositions(positions);
        setLastUpdate(new Date());
        
        if (process.env.NODE_ENV === 'development') {
          console.log(`✈️ Updated ${positions.length} aircraft positions`);
        }
      } else if (response.error) {
        const errorMessage = typeof response.error.detail === 'string' 
          ? response.error.detail 
          : response.error.error || 'Failed to fetch aircraft positions';
        setError(errorMessage);
        
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ Aircraft positions error:', errorMessage);
        }
      }
    } catch (err) {
      if (!abortController.signal.aborted && isActiveRef.current) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
        setError(errorMessage);
        
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ Aircraft positions fetch error:', errorMessage);
        }
      }
    } finally {
      if (isActiveRef.current && !abortController.signal.aborted) {
        setLoading(false);
      }
      
      // Clear active request
      if (activeRequestRef.current === abortController) {
        activeRequestRef.current = null;
      }
    }
  }, [memoizedBounds, enabled, lookbackSeconds]);

  // Set up interval when enabled
  useEffect(() => {
    if (!enabled) return;

    // Fetch immediately
    fetchPositions();

    // Set up 5-second interval
    intervalRef.current = setInterval(() => {
      fetchPositions();
    }, API_CONFIG.REFRESH_INTERVAL);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [enabled, fetchPositions]);

  return {
    positions,
    loading,
    error,
    lastUpdate,
    refetch: fetchPositions,
  };
} 