import { useState, useEffect } from 'react';
import { apiClient } from '../api-client';
import { AirportResponse } from '../../types/api';

interface UseAirportDataReturn {
  airport: AirportResponse | null;
  loading: boolean;
  error: string | null;
}

/**
 * Custom hook for fetching airport data
 * 
 * Simple hook that fetches airport data with loading and error states
 */
export function useAirportData(icao: string): UseAirportDataReturn {
  const [airport, setAirport] = useState<AirportResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAirport = async () => {
      if (!icao) {
        setError('ICAO code is required');
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);
      setAirport(null);

      try {
        const response = await apiClient.getAirport(icao);
        
        if (response.data) {
          setAirport(response.data);
        } else if (response.error) {
          const errorMessage = typeof response.error.detail === 'string' 
            ? response.error.detail 
            : response.error.error;
          setError(errorMessage);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch airport data');
      } finally {
        setLoading(false);
      }
    };

    fetchAirport();
  }, [icao]);

  return {
    airport,
    loading,
    error
  };
} 