import { AIRCRAFT_CONFIG, getAircraftIconSize } from './aircraft-config';

export interface AircraftShape {
  id: number;
  w: number;
  h: number;
  viewBox?: string;
  noRotate?: boolean;
  noAspect?: boolean;
  strokeScale?: number;
  accentMult?: number;
  path: string | string[];
  accent?: string;
}

// Aircraft shapes adapted from tar1090 - https://github.com/wiedehopf/tar1090
// Licensed under GPL v3.0
export const aircraftShapes: Record<string, AircraftShape> = {
  'airliner': {
    id: 0,
    w: 32,
    h: 32,
    viewBox: '-1 -2 34 34',
    strokeScale: 1.2,
    path: 'M16 1c-.17 0-.67.58-.9 1.03-.6 1.21-.6 1.15-.65 5.2-.04 2.97-.08 3.77-.18 3.9-.15.17-1.82 1.1-1.98 1.1-.08 0-.1-.25-.05-.83.03-.5.01-.92-.05-1.08-.1-.25-.13-.26-.71-.26-.82 0-.86.07-.78 1.5.03.6.08 1.17.11 1.25.05.12-.02.2-.25.33l-8 4.2c-.2.2-.18.1-.19 1.29 3.9-1.2 3.71-1.21 3.93-1.21.06 0 .1 0 .13.14.08.3.28.3.28-.04 0-.25.03-.27 1.16-.6.65-.2 1.22-.35 1.28-.35.05 0 .12.04.15.17.07.3.27.27.27-.08 0-.25.01-.27.7-.47.68-.1.98-.09 1.47-.1.18 0 .22 0 .26.18.06.34.22.35.27-.01.04-.2.1-.17 1.06-.14l1.07.02.05 4.2c.05 3.84.07 4.28.26 5.09.11.49.2.99.2 1.11 0 .19-.31.43-1.93 1.5l-1.93 1.26v1.02l4.13-.95.63 1.54c.05.07.12.09.19.09s.14-.02.19-.09l.63-1.54 4.13.95V29.3l-1.93-1.27c-1.62-1.06-1.93-1.3-1.93-1.49 0-.12.09-.62.2-1.11.19-.81.2-1.25.26-5.09l.05-4.2 1.07-.02c.96-.03 1.02-.05 1.06.14.05.36.21.35.27 0 .04-.17.08-.16.26-.16.49 0 .8-.02 1.48.1.68.2.69.21.69.46 0 .35.2.38.27.08.03-.13.1-.17.15-.17.06 0 .63.15 1.28.34 1.13.34 1.16.36 1.16.61 0 .35.2.34.28.04.03-.13.07-.14.13-.14.22 0 .03 0 3.93 1.2-.01-1.18.02-1.07-.19-1.27l-8-4.21c-.23-.12-.3-.21-.25-.33.03-.08.08-.65.11-1.25.08-1.43.04-1.5-.78-1.5-.58 0-.61.01-.71.26-.06.16-.08.58-.05 1.08.04.58.03.83-.05.83-.16 0-1.83-.93-1.98-1.1-.1-.13-.14-.93-.18-3.9-.05-4.05-.05-3.99-.65-5.2C16.67 1.58 16.17 1 16 1z',
  },
  'cessna': {
    id: 3,
    w: 26,
    h: 26,
    viewBox: '0 -1 32 31',
    strokeScale: 1.2,
    path: 'M16.36 20.96l2.57.27s.44.05.4.54l-.02.63s-.03.47-.45.54l-2.31.34-.44-.74-.22 1.63-.25-1.62-.38.73-2.35-.35s-.44-.1-.43-.6l-.02-.6s0-.5.48-.5l2.5-.27-.56-5.4-3.64-.1-5.83-1.02h-.45v-2.06s-.07-.37.46-.34l5.8-.17 3.55.12s-.1-2.52.52-2.82l-1.68-.04s-.1-.06 0-.14l1.94-.03s.35-1.18.7 0l1.91.04s.11.05 0 .14l-1.7.02s.62-.09.56 2.82l3.54-.1 5.81.17s.51-.04.48.35l-.01 2.06h-.47l-5.8 1-3.67.11z',
  },
  'heavy_2e': {
    id: 4,
    w: 34,
    h: 34,
    viewBox: '0 -3.2 64.2 64.2',
    strokeScale: 1.8,
    path: "m 31.414,2.728 c -0.314,0.712 -1.296,2.377 -1.534,6.133 l -0.086,13.379 c 0.006,0.400 -0.380,0.888 -0.945,1.252 l -2.631,1.729 c 0.157,-0.904 0.237,-3.403 -0.162,-3.850 l -2.686,0.006 c -0.336,1.065 -0.358,2.518 -0.109,4.088 h 0.434 L 24.057,26.689 8.611,36.852 7.418,38.432 7.381,39.027 8.875,38.166 l 8.295,-2.771 0.072,0.730 0.156,-0.004 0.150,-0.859 3.799,-1.234 0.074,0.727 0.119,0.004 0.117,-0.832 2.182,-0.730 h 1.670 l 0.061,0.822 h 0.176 l 0.062,-0.822 4.018,-0.002 v 13.602 c 0.051,1.559 0.465,3.272 0.826,4.963 l -6.836,5.426 c -0.097,0.802 -0.003,1.372 0.049,1.885 l 7.734,-2.795 0.477,1.973 h 0.232 l 0.477,-1.973 7.736,2.795 c 0.052,-0.513 0.146,-1.083 0.049,-1.885 l -6.836,-5.426 c 0.361,-1.691 0.775,-3.404 0.826,-4.963 V 33.193 l 4.016,0.002 0.062,0.822 h 0.178 L 38.875,33.195 h 1.672 l 2.182,0.730 0.117,0.832 0.119,-0.004 0.072,-0.727 3.799,1.234 0.152,0.859 0.154,0.004 0.072,-0.730 8.297,2.771 1.492,0.861 -0.037,-0.596 -1.191,-1.580 -15.447,-10.162 0.363,-1.225 H 41.125 c 0.248,-1.569 0.225,-3.023 -0.111,-4.088 l -2.686,-0.006 c -0.399,0.447 -0.317,2.945 -0.160,3.850 L 35.535,23.492 C 34.970,23.128 34.584,22.640 34.590,22.240 L 34.504,8.910 C 34.193,4.926 33.369,3.602 32.934,2.722 32.442,1.732 31.894,1.828 31.414,2.728 Z",
  },
  'c130': {
    id: 6,
    w: 33,
    h: 35,
    noAspect: true,
    strokeScale: 2,
    viewBox: '-1 -16 64 64',
    path: 'm 31,1 1,0 1,1 1,2 0,8 3,0 0,-3 1,-1 1,1 0,3 6,0 0,-3 1,-1 1,1 0,3 10,1 0,2 -1,1 -17,3 -5,0 0,10 -1,1 8,2 0,1 -1,1 -8,0 -1,1 -1,-1 -8,0 -1,-1 0,-1 8,-2 -1,-1 0,-10 -5,0 -17,-3 -1,-1 0,-2 10,-1 0,-3 1,-1 1,1 0,3 6,0 0,-3 1,-1 1,1 0,3 3,0 0,-8 1,-2 1,-1 z',
  },
  'hi_perf': {
    id: 7,
    w: 32,
    h: 32,
    strokeScale: 2.5,
    accentMult: 0.8,
    viewBox: '-7.8 0 80 80',
    path: "M 30.82,61.32 29.19,54.84 29.06,60.19 27.70,60.70 22.27,60.63 21.68,59.60 l -0.01,-2.71 6.26,-5.52 -0.03,-3.99 -13.35,-0.01 -3e-6,1.15 -1.94,0.00 -0.01,-1.31 0.68,-0.65 L 13.30,37.20 c -0.01,-0.71 0.57,-0.77 0.60,0 l 0.05,1.57 0.28,0.23 0.26,4.09 L 19.90,38.48 c 0,0 -0.04,-1.26 0.20,-1.28 0.16,-0.02 0.20,0.98 0.20,0.98 l 4.40,-3.70 c 0,0 0.02,-1.28 0.20,-1.28 0.14,-0.00 0.20,0.98 0.20,0.98 l 1.80,-1.54 C 27.02,28.77 28.82,25.58 29,21.20 c 0.06,-1.41 0.23,-3.34 0.86,-3.85 0.21,-4.40 1.32,-11.03 2.39,-11.03 1.07,0 2.17,6.64 2.39,11.03 0.63,0.51 0.80,2.45 0.86,3.85 0.18,4.38 1.98,7.57 2.10,11.44 l 1.80,1.54 c 0,0 0.06,-0.99 0.20,-0.98 0.18,0.01 0.20,1.28 0.20,1.28 l 4.40,3.70 c 0,0 0.04,-1.00 0.20,-0.98 0.24,0.03 0.20,1.28 0.20,1.28 l 5.41,4.60 0.26,-4.09 0.28,-0.23 L 50.59,37.20 c 0.03,-0.77 0.61,-0.71 0.60,0 l 0.02,9.37 0.68,0.65 -0.01,1.31 -1.94,-0.00 -3e-6,-1.15 -13.35,0.01 -0.03,3.99 6.26,5.52 L 42.81,59.60 42.22,60.63 36.79,60.70 35.43,60.19 35.30,54.84 33.67,61.32 Z",
    accent: "m 32.30,49.38 -0.02,13.99",
  },
  'helicopter': {
    id: 75,
    w: 28,
    h: 28,
    viewBox: '-1 -2 34 34',
    strokeScale: 1.0,
    path: 'M15.87 29s-.66-.15-.43-2.73l-.02-.43h-1.86v.68h-.19v-1.78h2l-.41-8.6-.57-2.79h-.9v1.09s-.15.4-.25 0v-2.08s-.34.09-.58.2l-10.34-.41-.01-.7 10.93.4v-4.6s.11-.28.24 0v2.38h.55V8.31s.27-2.84 1.95-2.99c0 0 1.62-.27 2.04 2.71L23.04.48l.6.36-4.81 7.33v6.24s-.14.32-.26 0v-1.04h-.88l-.18 1.02s.29.38.56.52l4.67 9.28-.6.24-4.77-9.3-.38 1.1-.5 8.53h1.98v1.73h-.19v-.65h-1.8l-.09.5s.05 2.78-.52 2.66z',
  },
  'uav': {
    id: 28,
    w: 28,
    h: 28,
    viewBox: '0 1 32 32',
    strokeScale: 0.9,
    path: 'M16.8 15.57v-1.08s1.8-5.07-.74-5.58c-2.44.59-.67 5.58-.67 5.58h0v1.08l-2.6.15-10.23.54v1.08l10.26.46 2.55.07v3.86h-3.05v1.38h3.25v.34l-1.81.25v.14h1.98c.15.49.35.47.35.47s.2.02.35-.47h1.99v-.14l-1.81-.25v-.34h3.24v-1.38h-3.05v-3.86l2.55-.07 10.26-.46v-1.08l-10.22-.54z',
  },
  'glider': {
    id: 41,
    w: 32,
    h: 32,
    viewBox: '0 0 32 32',
    strokeScale: 1.5,
    path: 'M16 8c-1 0-1.5.5-1.5 1.5v3l-9 4c-1 .5-1 1.5 0 2l9 4v3c0 1 .5 1.5 1.5 1.5s1.5-.5 1.5-1.5v-3l9-4c1-.5 1-1.5 0-2l-9-4v-3c0-1-.5-1.5-1.5-1.5z',
  },
  'balloon': {
    id: 2,
    w: 13,
    h: 17,
    noRotate: true,
    viewBox: '-2 -2 13 17',
    path: 'M3.56,12.75a.49.49,0,0,1-.46-.34L2.63,11a.51.51,0,0,1,.07-.44l.1-.1-2-3.68a.48.48,0,0,1-.05-.17,4.39,4.39,0,0,1-.48-2A4.29,4.29,0,0,1,4.5.25,4.29,4.29,0,0,1,8.75,4.58a4.39,4.39,0,0,1-.48,2,.45.45,0,0,1-.05.17l-2,3.68a.44.44,0,0,1,.1.1.51.51,0,0,1,.07.45L5.9,12.41a.49.49,0,0,1-.46.34Zm1.6-2.43L6.1,8.59A4.22,4.22,0,0,1,5,8.88v1.44ZM4,10.32V8.88A4.22,4.22,0,0,1,2.9,8.59l.94,1.73Z',
  },
  // Default fallback icon
  'default': {
    id: 99,
    w: 24,
    h: 24,
    viewBox: '0 0 24 24',
    strokeScale: 1.0,
    path: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z',
  }
};

// Type designator mappings - maps ICAO aircraft type codes to aircraft shapes
// Adapted from tar1090's comprehensive database
export const typeDesignatorIcons: Record<string, [string, number]> = {
  // Cessna aircraft
  'C150': ['cessna', 1],
  'C152': ['cessna', 1],
  'C172': ['cessna', 1],
  'C182': ['cessna', 1],
  
  // Piper aircraft
  'PA28': ['cessna', 1],
  'PA44': ['cessna', 1.1],
  
  // Cirrus aircraft
  'SR20': ['cessna', 1],
  'SR22': ['cessna', 1],
  'S22T': ['cessna', 1],
  
  // Large aircraft
  'A319': ['airliner', 1],
  'A320': ['airliner', 1],
  'A321': ['airliner', 1.1],
  'A330': ['heavy_2e', 1],
  'A340': ['heavy_2e', 1.1],
  'A350': ['heavy_2e', 1],
  'A380': ['heavy_2e', 1.2],
  
  'B737': ['airliner', 1],
  'B738': ['airliner', 1],
  'B739': ['airliner', 1],
  'B747': ['heavy_2e', 1.2],
  'B757': ['airliner', 1.1],
  'B767': ['heavy_2e', 1],
  'B777': ['heavy_2e', 1.1],
  'B787': ['heavy_2e', 1],
  
  // Military aircraft
  'C130': ['c130', 1],
  'F16': ['hi_perf', 1],
  'F18': ['hi_perf', 1],
  'F22': ['hi_perf', 1],
  'F35': ['hi_perf', 1],
  
  // Helicopters
  'R22': ['helicopter', 1],
  'R44': ['helicopter', 1],
  'R66': ['helicopter', 1],
  'AS50': ['helicopter', 1],
  'AS55': ['helicopter', 1],
  'B407': ['helicopter', 1],
  'UH60': ['helicopter', 1.2],
  
  // UAVs
  'MQ9': ['uav', 1],
  'MQ1': ['uav', 1],
  'GLBL': ['uav', 1.2],
  
  // Ground vehicles
  'GND': ['default', 0.8],
  'GRND': ['default', 0.8],
  'SERV': ['default', 0.8],
  'TWR': ['default', 0.6],
};

// Type description mappings - maps ICAO type description codes to aircraft shapes
export const typeDescriptionIcons: Record<string, [string, number]> = {
  'H': ['helicopter', 1], // Helicopter
  'G': ['glider', 1], // Gyrocopter/Glider
  
  'L1P': ['cessna', 1], // Land plane, 1 engine, piston
  'A1P': ['cessna', 1], // Amphibian, 1 engine, piston
  'L1T': ['cessna', 1], // Land plane, 1 engine, turboprop
  'L1J': ['hi_perf', 0.92], // Land plane, 1 engine, jet
  
  'L2P': ['cessna', 1.1], // Land plane, 2 engines, piston
  'A2P': ['cessna', 1.1], // Amphibian, 2 engines, piston
  'L2T': ['airliner', 0.96], // Land plane, 2 engines, turboprop
  'A2T': ['airliner', 0.96], // Amphibian, 2 engines, turboprop
  
  'L2J-L': ['airliner', 1], // < 7t
  'L2J-M': ['airliner', 1], // < 136t
  'L2J-H': ['heavy_2e', 0.96], // > 136t
  
  'L3J-H': ['heavy_2e', 1], // > 136t
  
  'L4T-M': ['c130', 1],
  'L4T-H': ['c130', 1.07],
  'L4T': ['c130', 0.96],
  
  'L4J-H': ['heavy_2e', 1],
  'L4J-M': ['airliner', 0.9],
  'L4J': ['airliner', 0.8],
};

// Category mappings - maps ADS-B category codes to aircraft shapes
export const categoryIcons: Record<string, [string, number]> = {
  "A1": ['cessna', 1], // < 7t
  "A2": ['airliner', 0.94], // < 34t
  "A3": ['airliner', 0.96], // < 136t
  "A4": ['airliner', 1], // < 136t
  "A5": ['heavy_2e', 0.92], // > 136t
  "A6": ['hi_perf', 0.94], // High performance
  "A7": ['helicopter', 1], // Helicopter
  'B1': ['glider', 1], // Glider
  "B2": ['balloon', 1], // Balloon
  'B6': ['uav', 1], // UAV
  'C0': ['default', 1], // Ground vehicle - unknown
  'C1': ['default', 1], // Ground vehicle - emergency
  'C2': ['default', 1], // Ground vehicle - service
  'C3': ['default', 1], // Ground vehicle - tower
};

export function getAircraftIcon(
  category?: string, 
  typeDesignator?: string, 
  typeDescription?: string, 
  wtc?: string
): [string, number] {
  // Check type designator first (most specific)
  if (typeDesignator && typeDesignator in typeDesignatorIcons) {
    return typeDesignatorIcons[typeDesignator];
  }
  
  // Check type description with wake turbulence category
  if (typeDescription && wtc) {
    const typeDescriptionWithWtc = `${typeDescription}-${wtc}`;
    if (typeDescriptionWithWtc in typeDescriptionIcons) {
      return typeDescriptionIcons[typeDescriptionWithWtc];
    }
  }
  
  // Check type description without wake turbulence category
  if (typeDescription && typeDescription in typeDescriptionIcons) {
    return typeDescriptionIcons[typeDescription];
  }
  
  // Check basic type (first character of type description)
  if (typeDescription && typeDescription.length > 0) {
    const basicType = typeDescription[0];
    if (basicType in typeDescriptionIcons) {
      return typeDescriptionIcons[basicType];
    }
  }
  
  // Check category
  if (category && category in categoryIcons) {
    return categoryIcons[category];
  }
  
  // Default fallback
  return ['default', 1];
}

export function createSvgIcon(
  shapeName: string, 
  rotation: number = 0, 
  color: string = AIRCRAFT_CONFIG.ALTITUDE_COLORS.high,
  scale: number = 1,
  isOnGround: boolean = false
): string {
  const shape = aircraftShapes[shapeName] || aircraftShapes['default'];
  const size = getAircraftIconSize(scale);
  const strokeWidth = (shape.strokeScale || 1) * (isOnGround ? 0.8 : 1.2);
  const finalColor = isOnGround ? AIRCRAFT_CONFIG.ALTITUDE_COLORS.ground : color;
  
  // Create gradient ID based on color to make it unique
  const gradientId = `gradient-${color.replace('#', '')}`;
  
  // Create a lighter version of the color for gradient
  const lighterColor = adjustColorBrightness(finalColor, 0.3);
  
  // Handle multi-path shapes
  const paths = Array.isArray(shape.path) ? shape.path : [shape.path];
  const pathElements = paths.map((path, index) => 
    `<path d="${path}" fill="url(#${gradientId})" stroke="#000000" stroke-width="${strokeWidth}" opacity="${index === 0 ? 1 : 0.8}"/>`
  ).join('');
  
  // Add accent path if present
  const accentElement = shape.accent ? 
    `<path d="${shape.accent}" fill="none" stroke="#000000" stroke-width="${strokeWidth * (shape.accentMult || 1)}" opacity="0.9"/>` : '';
  
  const svgContent = `
    <svg viewBox="${shape.viewBox || `0 0 ${shape.w} ${shape.h}`}" 
         width="${size}" height="${size}" 
         xmlns="http://www.w3.org/2000/svg"
         style="transform: rotate(${shape.noRotate ? 0 : rotation}deg);">
      <defs>
        <linearGradient id="${gradientId}" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${lighterColor};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${finalColor};stop-opacity:1" />
        </linearGradient>
      </defs>
      ${pathElements}
      ${accentElement}
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(svgContent)}`;
}

// Helper function to adjust color brightness
function adjustColorBrightness(color: string, factor: number): string {
  // Remove # if present
  const hex = color.replace('#', '');
  
  // Parse RGB
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  // Increase brightness
  const newR = Math.min(255, Math.floor(r + (255 - r) * factor));
  const newG = Math.min(255, Math.floor(g + (255 - g) * factor));
  const newB = Math.min(255, Math.floor(b + (255 - b) * factor));
  
  // Convert back to hex
  return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
} 