import {
  AirportResponse,
  AirportSummary,
  AirportCreate,
  AirportUpdate,
  AirportEventsResponse,
  AircraftPositionsAtTimeResponse,
  OptimizedAircraftPositionsAtTimeResponse,
  H3CellsResponse,
  OccurrenceSearchRequest,
  OccurrenceSearchResponse,
  FlightTraceResponse,
  ApiResponse,
  ErrorResponse,
  ValidationErrorResponse,
  API_ENDPOINTS,
  HttpMethod,
} from '../types/api';

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl?: string) {
    this.baseUrl = baseUrl || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';
  }

  private async request<T>(
    endpoint: string,
    method: HttpMethod = 'GET',
    data?: unknown
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const config: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        config.body = JSON.stringify(data);
      }

      const response = await fetch(url, config);
      
      if (response.ok) {
        const responseData = method === 'DELETE' ? null : await response.json();
        return {
          data: responseData,
          status: response.status,
        };
      } else {
        const errorData = await response.json();
        return {
          error: errorData,
          status: response.status,
        };
      }
    } catch (error) {
      return {
        error: {
          error: 'Network Error',
          detail: error instanceof Error ? error.message : 'Unknown error occurred',
        },
        status: 0,
      };
    }
  }

  // Airport endpoints
  async getAirport(icao: string): Promise<ApiResponse<AirportResponse>> {
    return this.request<AirportResponse>(API_ENDPOINTS.AIRPORT_BY_ICAO(icao));
  }

  async getAirportEvents(icao: string, params?: {
    start_time?: string; // ISO 8601 datetime string
    end_time?: string;   // ISO 8601 datetime string
  }): Promise<ApiResponse<AirportEventsResponse>> {
    const searchParams = new URLSearchParams();
    
    if (params?.start_time) searchParams.append('start_time', params.start_time);
    if (params?.end_time) searchParams.append('end_time', params.end_time);

    const endpoint = `${API_ENDPOINTS.AIRPORT_EVENTS(icao)}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.request<AirportEventsResponse>(endpoint);
  }

  async getAirportSummary(icao: string): Promise<ApiResponse<AirportSummary>> {
    return this.request<AirportSummary>(API_ENDPOINTS.AIRPORT_SUMMARY(icao));
  }

  async listAirports(params?: {
    skip?: number;
    limit?: number;
    search?: string;
  }): Promise<ApiResponse<AirportResponse[]>> {
    const searchParams = new URLSearchParams();
    
    if (params?.skip !== undefined) searchParams.append('skip', params.skip.toString());
    if (params?.limit !== undefined) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    const endpoint = `${API_ENDPOINTS.AIRPORTS}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.request<AirportResponse[]>(endpoint);
  }

  async createAirport(airportData: AirportCreate): Promise<ApiResponse<AirportResponse>> {
    return this.request<AirportResponse>(API_ENDPOINTS.AIRPORTS, 'POST', airportData);
  }

  async updateAirport(icao: string, airportData: AirportUpdate): Promise<ApiResponse<AirportResponse>> {
    return this.request<AirportResponse>(API_ENDPOINTS.AIRPORT_BY_ICAO(icao), 'PUT', airportData);
  }

  async deleteAirport(icao: string): Promise<ApiResponse<null>> {
    return this.request<null>(API_ENDPOINTS.AIRPORT_BY_ICAO(icao), 'DELETE');
  }

  // Health check
  async healthCheck(): Promise<ApiResponse<{ status: string; version: string; environment: string }>> {
    return this.request(API_ENDPOINTS.HEALTH);
  }

  // Aircraft endpoints
  async getAircraftPositionsAtTime(params?: {
    timestamp?: string;
    lookback_seconds?: number;
    north?: number;
    east?: number;
    south?: number;
    west?: number;
  }): Promise<ApiResponse<AircraftPositionsAtTimeResponse>> {
    const searchParams = new URLSearchParams();
    
    if (params?.timestamp) searchParams.append('timestamp', params.timestamp);
    if (params?.lookback_seconds !== undefined) searchParams.append('lookback_seconds', params.lookback_seconds.toString());
    if (params?.north !== undefined) searchParams.append('north', params.north.toString());
    if (params?.east !== undefined) searchParams.append('east', params.east.toString());
    if (params?.south !== undefined) searchParams.append('south', params.south.toString());
    if (params?.west !== undefined) searchParams.append('west', params.west.toString());

    const endpoint = `${API_ENDPOINTS.AIRCRAFT_POSITIONS_AT_TIME}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    console.log('🚀 Calling aircraft positions API:', endpoint);
    return this.request<AircraftPositionsAtTimeResponse>(endpoint);
  }

  async getAircraftPositionsAtTimeOptimized(params?: {
    timestamp?: string;
    lookback_seconds?: number;
    north?: number;
    east?: number;
    south?: number;
    west?: number;
  }): Promise<ApiResponse<OptimizedAircraftPositionsAtTimeResponse>> {
    const searchParams = new URLSearchParams();
    
    if (params?.timestamp) searchParams.append('timestamp', params.timestamp);
    if (params?.lookback_seconds !== undefined) searchParams.append('lookback_seconds', params.lookback_seconds.toString());
    if (params?.north !== undefined) searchParams.append('north', params.north.toString());
    if (params?.east !== undefined) searchParams.append('east', params.east.toString());
    if (params?.south !== undefined) searchParams.append('south', params.south.toString());
    if (params?.west !== undefined) searchParams.append('west', params.west.toString());

    const endpoint = `${API_ENDPOINTS.AIRCRAFT_POSITIONS_AT_TIME_OPTIMIZED}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    console.log('🚀 Calling optimized aircraft positions API:', endpoint);
    return this.request<OptimizedAircraftPositionsAtTimeResponse>(endpoint);
  }

  async getH3Cells(params: {
    north: number;
    east: number;
    south: number;
    west: number;
    resolution?: number;
  }): Promise<ApiResponse<H3CellsResponse>> {
    const searchParams = new URLSearchParams();
    
    searchParams.append('north', params.north.toString());
    searchParams.append('east', params.east.toString());
    searchParams.append('south', params.south.toString());
    searchParams.append('west', params.west.toString());
    if (params.resolution !== undefined) searchParams.append('resolution', params.resolution.toString());

    const endpoint = `${API_ENDPOINTS.AIRCRAFT_H3_CELLS}?${searchParams.toString()}`;
    return this.request<H3CellsResponse>(endpoint);
  }

  async searchOccurrences(searchData: OccurrenceSearchRequest): Promise<ApiResponse<OccurrenceSearchResponse>> {
    console.log('🔍 Searching occurrences with data:', searchData);
    return this.request<OccurrenceSearchResponse>(API_ENDPOINTS.AIRCRAFT_OCCURRENCES_SEARCH, 'POST', searchData);
  }

  async getFlightTrace(params: {
    hex: string;
    timestamp: string;
  }): Promise<ApiResponse<FlightTraceResponse>> {
    const searchParams = new URLSearchParams();
    
    searchParams.append('hex', params.hex);
    searchParams.append('timestamp', params.timestamp);

    const endpoint = `${API_ENDPOINTS.AIRCRAFT_TRACE}?${searchParams.toString()}`;
    console.log('✈️ Calling aircraft trace API:', endpoint);
    return this.request<FlightTraceResponse>(endpoint);
  }
}

// Default instance
export const apiClient = new ApiClient();

// Export class for custom instances
export default ApiClient;

// Helper function to check if an error is a validation error
export function isValidationError(error: ErrorResponse | ValidationErrorResponse): error is ValidationErrorResponse {
  return error.error === 'Validation Error';
}

// Helper function to format validation errors
export function formatValidationErrors(error: ValidationErrorResponse): string[] {
  return error.detail.map(err => {
    const field = err.loc.join('.');
    return `${field}: ${err.msg}`;
  });
}

// Helper function to handle API responses
export async function handleApiResponse<T>(
  apiCall: () => Promise<ApiResponse<T>>,
  onSuccess?: (data: T) => void,
  onError?: (error: ErrorResponse | ValidationErrorResponse) => void
): Promise<T | null> {
  const response = await apiCall();
  
  if (response.data) {
    onSuccess?.(response.data);
    return response.data;
  } else if (response.error) {
    onError?.(response.error);
    return null;
  }
  
  return null;
} 