import { cellToLatLng } from 'h3-js';

/**
 * Converts H3 index to latitude and longitude coordinates
 * @param h3Index - The H3 cell index
 * @returns [lat, lng] coordinates or null if invalid
 */
export function getLatLngFromH3(h3Index?: string): [number, number] | null {
  if (!h3Index) return null;
  
  try {
    const [lat, lng] = cellToLatLng(h3Index);
    return [lat, lng];
  } catch (error) {
    console.warn(`Failed to convert H3 index ${h3Index} to coordinates:`, error);
    return null;
  }
} 