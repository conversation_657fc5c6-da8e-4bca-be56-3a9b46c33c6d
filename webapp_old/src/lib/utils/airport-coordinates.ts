import { AirportResponse } from '../../types/api';

/**
 * Extract coordinates from GeoJSON location string
 * 
 * Our API returns location as GeoJSON string like:
 * '{"type":"Point","coordinates":[-73.77691515,40.640318175]}'
 * 
 * This function safely parses it and returns [lat, lng] for Leaflet
 */
export function extractCoordinatesFromGeoJSON(location: string | null | undefined): [number, number] | null {
  if (!location) return null;
  
  try {
    const geoJSON = JSON.parse(location);
    
    if (geoJSON.type === 'Point' && Array.isArray(geoJSON.coordinates) && geoJSON.coordinates.length === 2) {
      const [lng, lat] = geoJSON.coordinates;
      return [lat, lng]; // Leaflet expects [lat, lng]
    }
  } catch (error) {
    console.warn('Failed to parse GeoJSON location:', location, error);
  }
  
  return null;
}

/**
 * Get airport coordinates from API response data
 * Returns null if no valid coordinates found
 */
export function getAirportCoordinates(airport: AirportResponse | null): [number, number] | null {
  if (!airport) return null;
  
  return extractCoordinatesFromGeoJSON(airport.location);
}

/**
 * Parse runway centerline GeoJSON into coordinate array
 * Returns coordinates for drawing runway lines on map
 */
export function parseRunwayCenterline(centerline: string | null | undefined): [number, number][] {
  if (!centerline) return [];
  
  try {
    const geoJSON = JSON.parse(centerline);
    
    if (geoJSON.type === 'LineString' && Array.isArray(geoJSON.coordinates)) {
      return geoJSON.coordinates.map(([lng, lat]: [number, number]) => [lat, lng]);
    }
  } catch (error) {
    console.warn('Failed to parse runway centerline:', centerline, error);
  }
  
  return [];
} 