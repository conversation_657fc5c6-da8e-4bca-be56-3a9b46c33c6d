/**
 * Formats altitude for display
 * @param alt - Altitude in feet
 * @returns Formatted altitude string
 */
export function formatAltitude(alt?: number): string {
  if (alt === null || alt === undefined) return 'N/A';
  return `${alt.toLocaleString()} ft`;
}

/**
 * Formats speed for display
 * @param speed - Speed in knots
 * @returns Formatted speed string
 */
export function formatSpeed(speed?: number): string {
  if (speed === null || speed === undefined) return 'N/A';
  return `${speed} kt`;
}

/**
 * Formats track/heading for display
 * @param track - Track in degrees
 * @returns Formatted track string
 */
export function formatTrack(track?: number): string {
  if (track === null || track === undefined) return 'N/A';
  return `${track}°`;
} 