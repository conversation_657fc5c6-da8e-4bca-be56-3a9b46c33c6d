import * as h3 from 'h3-js';

/**
 * Calculate the optimal H3 resolution for a given polygon
 * Based on the polygon's bounding box size
 */
export function calculateOptimalResolution(polygon: [number, number][]): number {
  if (polygon.length === 0) return 7; // Default resolution
  
  // Calculate bounding box
  const lats = polygon.map(point => point[0]);
  const lngs = polygon.map(point => point[1]);
  
  const minLat = Math.min(...lats);
  const maxLat = Math.max(...lats);
  const minLng = Math.min(...lngs);
  const maxLng = Math.max(...lngs);
  
  // Calculate the diagonal distance of the bounding box in degrees
  const latDiff = maxLat - minLat;
  const lngDiff = maxLng - minLng;
  const diagonalDegrees = Math.sqrt(latDiff * latDiff + lngDiff * lngDiff);
  
  // Resolution mapping based on approximate area size
  // These values are empirically determined for good performance/accuracy balance
  if (diagonalDegrees > 10) return 4;    // Very large areas (countries, large regions)
  if (diagonalDegrees > 5) return 5;     // Large areas (states, provinces)
  if (diagonalDegrees > 2) return 6;     // Medium-large areas (cities)
  if (diagonalDegrees > 1) return 7;     // Medium areas (neighborhoods)
  if (diagonalDegrees > 0.5) return 8;   // Small-medium areas
  if (diagonalDegrees > 0.1) return 9;   // Small areas
  if (diagonalDegrees > 0.05) return 10; // Very small areas
  return 11; // Tiny areas
}

/**
 * Convert a polygon to H3 cell indexes
 * Automatically determines the optimal resolution if not specified
 */
export function polygonToH3Cells(
  polygon: [number, number][], 
  resolution?: number
): string[] {
  if (polygon.length < 3) {
    throw new Error('Polygon must have at least 3 points');
  }
  
  const res = resolution ?? calculateOptimalResolution(polygon);
  
  try {
    // Convert polygon to H3 cells using h3-js
    // The polygon format expected by h3-js is [[lat, lng], [lat, lng], ...]
    const h3Cells = h3.polygonToCells(polygon, res, false); // false = not GeoJSON format
    return h3Cells;
  } catch (error) {
    console.error('Error converting polygon to H3 cells:', error);
    throw new Error(`Failed to convert polygon to H3 cells: ${error}`);
  }
}

/**
 * Convert a radius search to H3 cell indexes
 * Creates a polygon approximation of the circle, then uses the same polygon logic
 */
export function radiusToH3Cells(
  center: [number, number], 
  radiusMeters: number,
  resolution?: number,
  polygonPoints: number = 32
): string[] {
  try {
    // Convert radius to polygon approximation
    const circlePolygon = radiusToPolygon(center, radiusMeters, polygonPoints);
    
    // Use the same polygon-to-cells logic for consistency
    return polygonToH3Cells(circlePolygon, resolution);
  } catch (error) {
    console.error('Error converting radius to H3 cells:', error);
    throw new Error(`Failed to convert radius to H3 cells: ${error}`);
  }
}

/**
 * Convert a radius (circle) to a polygon approximation
 * @param center - [lat, lng] center point
 * @param radiusMeters - radius in meters
 * @param points - number of points to generate around the circle (default 32)
 * @returns Array of [lat, lng] points forming a polygon
 */
function radiusToPolygon(
  center: [number, number], 
  radiusMeters: number, 
  points: number = 32
): [number, number][] {
  const [centerLat, centerLng] = center;
  const earthRadius = 6371000; // Earth's radius in meters
  
  const polygon: [number, number][] = [];
  
  for (let i = 0; i < points; i++) {
    const angle = (2 * Math.PI * i) / points;
    
    // Calculate offset in degrees
    // For latitude: simple conversion
    const latOffset = (radiusMeters / earthRadius) * (180 / Math.PI);
    
    // For longitude: account for latitude compression
    const lngOffset = (radiusMeters / earthRadius) * (180 / Math.PI) / Math.cos(centerLat * Math.PI / 180);
    
    // Calculate the point on the circle
    const lat = centerLat + latOffset * Math.cos(angle);
    const lng = centerLng + lngOffset * Math.sin(angle);
    
    polygon.push([lat, lng]);
  }
  
  return polygon;
}

/**
 * Get information about H3 resolution and cell count for debugging
 */
export function getH3Info(cells: string[]): {
  count: number;
  resolution: number | null;
  minResolution: number | null;
  maxResolution: number | null;
} {
  if (cells.length === 0) {
    return { count: 0, resolution: null, minResolution: null, maxResolution: null };
  }
  
  const resolutions = cells.map(cell => h3.getResolution(cell));
  const uniqueResolutions = [...new Set(resolutions)];
  
  return {
    count: cells.length,
    resolution: uniqueResolutions.length === 1 ? uniqueResolutions[0] : null,
    minResolution: Math.min(...resolutions),
    maxResolution: Math.max(...resolutions)
  };
} 