// API types for the webapp
// These types should be kept in sync with the Pydantic schemas in the API

export enum RunwaySurfaceEnum {
  ASPHALT = "ASPHALT",
  CONCRETE = "CONCRETE", 
  GRASS = "GRASS",
  GRAVEL = "GRAVEL",
  DIRT = "DIRT",
  WATER = "WATER",
  UNKNOWN = "UNKNOWN",
  OTHER = "OTHER",
}

export enum AircraftCategoryEnum {
  A0 = "A0",
  A1 = "A1",
  A2 = "A2",
  A3 = "A3",
  A4 = "A4",
  A5 = "A5",
  A6 = "A6",
  A7 = "A7",
  B0 = "B0",
  B1 = "B1",
  B2 = "B2",
  B3 = "B3",
  B4 = "B4",
  B5 = "B5",
  B6 = "B6",
  B7 = "B7",
  C0 = "C0",
  C1 = "C1",
  C2 = "C2",
  C3 = "C3",
  C4 = "C4",
  C5 = "C5",
  C6 = "C6",
  C7 = "C7",
  D0 = "D0",
  D1 = "D1",
  D2 = "D2",
  D3 = "D3",
  D4 = "D4",
  D5 = "D5",
  D6 = "D6",
  D7 = "D7",
  UNKNOWN = "UNKNOWN",
}

export enum WakeTurbulenceCategoryEnum {
  L = "L", // Light (< 7,000 kg)
  M = "M", // Medium (7,000 - 136,000 kg)
  H = "H", // Heavy (> 136,000 kg)
  J = "J", // Super (A380, AN-225)
}

export enum FlightPhaseEnum {
  GROUND = "ground",
  TAKEOFF = "takeoff",
  LANDING = "landing",
  AIRBORNE = "airborne",
}

// Aircraft base interface
export interface AircraftBase {
  hex: string;
  category?: AircraftCategoryEnum;
  registration?: string;
}

// Enhanced Aircraft interface with TAR1090 fields
export interface Aircraft extends AircraftBase {
  // TAR1090 enhanced fields
  icao_type_designator?: string; // ICAO aircraft type designator (e.g., A320, B738)
  type_description?: string; // ICAO type description code (e.g., L2J)
  wake_turbulence_category?: WakeTurbulenceCategoryEnum;
  aircraft_description?: string; // Full aircraft description/model name
  operator?: string; // Aircraft owner/operator name
  year_built?: number; // Year the aircraft was manufactured

  // Flag fields
  is_military: boolean; // True if this is a military aircraft
  is_interesting: boolean; // True if this is a special/interesting aircraft
  is_pia: boolean; // True if Privacy ICAO Address (blocked registration)
  is_ladd: boolean; // True if Limiting Aircraft Data Displayed

  // Metadata fields
  country_code?: string; // Country code derived from ICAO hex ranges
  database_source?: string; // Source of the aircraft data
  last_updated?: string; // ISO 8601 datetime
  created_at?: string; // ISO 8601 datetime
}

export interface RunwayBase {
  name: string;
  length_ft?: number;
  width_ft?: number;
  surface: RunwaySurfaceEnum;
}

export type RunwayResponse = RunwayBase;
// Note: RunwayResponse in the API doesn't include airport_icao, created_at, updated_at
// Keeping the interface simple to match API schema

export interface RunwayCreate extends RunwayBase {
  airport_icao: string;
  centerline?: string; // GeoJSON LineString
}

export interface RunwayUpdate {
  length_ft?: number;
  width_ft?: number;
  surface?: RunwaySurfaceEnum;
  centerline?: string; // GeoJSON LineString
}

export interface AirportBase {
  icao: string;
  iata?: string;
  name?: string;
  elevation_ft?: number;
}

export interface AirportResponse extends AirportBase {
  location?: string; // GeoJSON Point
  runways: RunwayResponse[];
}

export interface AirportCreate extends AirportBase {
  location?: string; // GeoJSON Point
}

export interface AirportUpdate {
  iata?: string;
  name?: string;
  elevation_ft?: number;
  location?: string; // GeoJSON Point
}

export interface AirportSummary {
  icao: string;
  iata?: string;
  name?: string;
  elevation_ft?: number;
  location?: string; // GeoJSON Point
  runway_count: number;
}

// Airport Events Types
export interface RunwayEventResponse {
  time: string; // ISO 8601 datetime string
  event_type: FlightPhaseEnum; // takeoff or landing
  registration?: string;
  category?: AircraftCategoryEnum;
  hex: string;
  runway: string;
  flight?: string;
  alt_baro?: number;
  track?: number;
}

export interface AirportEventsResponse {
  icao: string;
  start_time: string; // ISO 8601 datetime string
  end_time: string; // ISO 8601 datetime string
  events: RunwayEventResponse[];
  count: number;
}

// Error response types
export interface ErrorResponse {
  error: string;
  detail?: string;
}

export interface ValidationErrorResponse {
  error: "Validation Error";
  detail: Array<{
    loc: (string | number)[];
    msg: string;
    type: string;
  }>;
}

// API endpoints configuration
export const API_ENDPOINTS = {
  AIRPORTS: "/api/v1/airports",
  AIRPORT_BY_ICAO: (icao: string) => `/api/v1/airports/${icao}`,
  AIRPORT_EVENTS: (icao: string) => `/api/v1/airports/${icao}/events`,
  AIRPORT_SUMMARY: (icao: string) => `/api/v1/airports/${icao}/summary`,
  AIRCRAFT_POSITIONS_AT_TIME: "/api/v1/aircraft/positions/at-time",
  AIRCRAFT_POSITIONS_AT_TIME_OPTIMIZED: "/api/v1/aircraft/positions/at-time/optimized",
  AIRCRAFT_H3_CELLS: "/api/v1/aircraft/h3-cells",
  AIRCRAFT_TRACE: "/api/v1/aircraft/trace",
  AIRCRAFT_OCCURRENCES_SEARCH: "/api/v1/aircraft/occurrences/search",
  HEALTH: "/health",
} as const;

// HTTP methods
export type HttpMethod = "GET" | "POST" | "PUT" | "DELETE" | "PATCH";

// Generic API response wrapper
export interface ApiResponse<T> {
  data?: T;
  error?: ErrorResponse | ValidationErrorResponse;
  status: number;
}

// Aircraft trace point - represents a historical position (simplified for optimal payload)
export interface AircraftTracePoint {
  h3_index: string;
  // Only keeping h3_index for geographic position - all other fields removed to minimize payload
}

export interface AircraftPositionResponse {
  time: string;
  hex: string;
  alt_baro?: number;
  alt_geom?: number;
  gs?: number; // Ground speed
  track?: number;
  baro_rate?: number;
  squawk?: number;
  flight?: string;
  h3_index?: string;
  flight_phase?: FlightPhaseEnum;
  h3_res4?: string;
  // Aircraft information
  category?: AircraftCategoryEnum;
  registration?: string;
  // Historical positions within the lookback window (ordered chronologically, oldest first)
  trace?: AircraftTracePoint[];
  // Future fields (will be added to API soon)
  // type_code?: string; // ICAO aircraft type designator (e.g., "B737", "A320")
}

// Extended interface for when type_code is added to the API
export interface AircraftPositionResponseWithTypeCode extends AircraftPositionResponse {
  type_code?: string; // ICAO aircraft type designator (e.g., "B737", "A320")
}

export interface AircraftPositionsAtTimeResponse {
  timestamp: string;
  positions: AircraftPositionResponse[];
  count: number;
}

// Optimized response format with timestamp only once
export interface OptimizedAircraftPositionsAtTimeResponse {
  timestamp: string; // Single timestamp for all positions
  actual_time: string; // The actual time this data represents
  positions: OptimizedAircraftPosition[]; // Positions without individual time fields
  count: number;
}

// Aircraft position without individual time field (optimized format)
export interface OptimizedAircraftPosition {
  hex: string;
  alt_baro?: number;
  alt_geom?: number;
  gs?: number; // Ground speed
  track?: number;
  baro_rate?: number;
  squawk?: number;
  flight?: string;
  h3_index?: string;
  flight_phase?: FlightPhaseEnum;
  h3_res4?: string;
  // Aircraft information
  category?: AircraftCategoryEnum;
  registration?: string;
  // Historical positions within the lookback window (ordered chronologically, oldest first)
  trace?: AircraftTracePoint[];
}

export interface H3CellsResponse {
  north: number;
  east: number;
  south: number;
  west: number;
  resolution: number;
  cells: string[];
  count: number;
}

// Aircraft Occurrences Types
export interface OccurrenceSearchRequest {
  area: string[]; // Array of H3 cell indexes (required)
  startTime?: string | null; // ISO 8601 datetime string
  endTime?: string | null; // ISO 8601 datetime string
  occurrencePeriod?: number; // Max time gap in seconds, default 11
  flightPhase?: string[] | null; // "ground", "takeoff", "landing", "airborne"
  altMin?: number | null; // Minimum altitude in feet
  altMax?: number | null; // Maximum altitude in feet
  heading?: number[] | null; // Array of heading values 0-359
  type?: string[] | null; // Array of aircraft types
  class?: string[] | null; // Array of aircraft classes
  squawk?: string[] | null; // Array of squawk codes
  registration?: string[] | null; // Array of aircraft registrations
}

export interface OccurrenceResponse {
  hex: string; // Aircraft hex identifier
  firstseen: string; // ISO 8601 datetime
  lastseen: string; // ISO 8601 datetime
  flight?: string; // Flight number/callsign
  category?: string; // Primary flight phase for this occurrence
  registration?: string; // Aircraft registration
  aircraft_category?: string; // Aircraft category (A0-A7, B0-B7, etc.)
  position_count: number; // Number of positions in this occurrence
}

export interface OccurrenceSearchResponse {
  search_parameters: OccurrenceSearchRequest; // Echo back the request
  occurrences: OccurrenceResponse[];
  count: number;
  execution_time_ms: number;
}

// Flight trace types for the aircraft trace API
export interface FlightTracePoint {
  time: string; // ISO 8601 datetime
  alt_baro?: number; // Barometric altitude in feet
  gs?: number; // Ground speed in knots
  flight_phase?: FlightPhaseEnum; // Flight phase
  flight?: string; // Flight number/callsign
  track?: number; // Track angle in degrees
  h3_index?: string; // H3 index for position
  squawk?: number; // Squawk code
}

export interface FlightTraceResponse {
  hex: string; // Aircraft hex identifier
  search_time: string; // Original search timestamp
  time_range_start: string; // Start time of the search window
  time_range_end: string; // End time of the search window
  trace_points: FlightTracePoint[]; // Flight trace points ordered chronologically
  count: number; // Number of trace points returned
  registration?: string; // Aircraft registration
  category?: AircraftCategoryEnum; // Aircraft category
} 