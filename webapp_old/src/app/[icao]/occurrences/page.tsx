'use client';

import { use<PERSON>arams } from 'next/navigation';
import { useState, useCallback, useMemo, useEffect } from 'react';
import { Search as SearchIcon, BarChart3 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import AppNavbar from '../../../components/AppNavbar';
import OccurrencesMap from '../../../components/OccurrencesMap';
import OccurrenceResults from '../../../components/OccurrenceResults';
import OccurrenceSearchFilters, { SearchFilters, SectionKey } from '../../../components/OccurrenceSearchFilters';
import FlightDataChart from '../../../components/FlightDataChart';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '../../../components/ui/tabs';
import { Badge } from '../../../components/ui/badge';
import { polygonToH3Cells, radiusToH3Cells, getH3Info } from '../../../lib/utils/h3-utils';
import { apiClient } from '../../../lib/api-client';
import { OccurrenceSearchRequest, OccurrenceSearchResponse, OccurrenceResponse, FlightTraceResponse } from '../../../types/api';

const INITIAL_FILTERS: SearchFilters = {
  dateTimeFrom: '',
  dateTimeTo: '',
  altitudeFilterMode: 'groundAirborne',
  includeGroundAircraft: true,
  includeAirborneAircraft: true,
  minAltitude: 0,
  maxAltitude: 10000,
  flightEvents: {
    landing: false,
    takeoff: false,
    goAround: false,
    touchAndGo: false
  },
  radiusCenter: null,
  radiusDistance: 5000,
  headingStart: 0,
  headingEnd: 90,
  occurrenceType: '',
  aircraftClass: '',
  registration: '',
  squawk: ''
};

export default function OccurrencesPage() {
  const params = useParams();
  const icao = params.icao as string;
  
  // ===== STATE MANAGEMENT =====
  const [filters, setFilters] = useState<SearchFilters>(INITIAL_FILTERS);
  const [enabledFilters, setEnabledFilters] = useState<Set<SectionKey>>(new Set());
  const [openSection, setOpenSection] = useState<SectionKey | null>('timeRange');
  const [isSearching, setIsSearching] = useState(false);
  const [searchMethod, setSearchMethod] = useState<'radius' | 'draw'>('radius');
  const [drawnPolygon, setDrawnPolygon] = useState<[number, number][] | null>(null);
  const [currentPolygonPoints, setCurrentPolygonPoints] = useState<[number, number][]>([]);
  const [searchResults, setSearchResults] = useState<OccurrenceSearchResponse | null>(null);
  const [activeTab, setActiveTab] = useState<'search' | 'results'>('search');
  const [selectedOccurrence, setSelectedOccurrence] = useState<OccurrenceResponse | null>(null);
  const [flightTrace, setFlightTrace] = useState<FlightTraceResponse | null>(null);
  const [isLoadingTrace, setIsLoadingTrace] = useState(false);
  const [hoveredTimestamp, setHoveredTimestamp] = useState<string | null>(null);

  // Simplified search area confirmation state
  const [isRadiusConfirmed, setIsRadiusConfirmed] = useState<boolean>(false);

  // ===== COMPUTED VALUES =====
  const isSearchAreaValid = useMemo(() => {
    if (searchMethod === 'radius') {
      return Boolean(filters.radiusCenter && isRadiusConfirmed);
    }
    return Boolean(drawnPolygon && drawnPolygon.length >= 3);
  }, [searchMethod, filters.radiusCenter, isRadiusConfirmed, drawnPolygon]);

  // ===== UNIFIED HANDLERS =====
  const updateFilters = useCallback((updates: Partial<SearchFilters>) => {
    setFilters(prev => ({ ...prev, ...updates }));
    // Reset radius confirmation if center changes
    if (updates.radiusCenter !== undefined) {
      setIsRadiusConfirmed(false);
    }
  }, []);

  const setEndToNow = useCallback(() => {
    const now = new Date();
    const nyOffset = -5;
    const localTime = new Date(now.getTime() + (nyOffset * 60 * 60 * 1000));
    updateFilters({ dateTimeTo: localTime.toISOString().slice(0, 16) });
  }, [updateFilters]);
  
  const handleHeadingChange = useCallback((start: number, end: number) => {
    updateFilters({ headingStart: start, headingEnd: end });
  }, [updateFilters]);
  
  const handleMapClick = useCallback((lat: number, lng: number) => {
    if (searchMethod === 'radius') {
      updateFilters({ radiusCenter: [lat, lng] });
    }
  }, [searchMethod, updateFilters]);
  
  const handlePolygonPointAdded = useCallback((points: [number, number][]) => {
    setCurrentPolygonPoints(points);
  }, []);
  
  const handlePolygonPointDragged = useCallback((index: number, newPosition: [number, number]) => {
    setCurrentPolygonPoints(prev => {
      const newPoints = [...prev];
      newPoints[index] = newPosition;
      return newPoints;
    });
  }, []);
  
  const handlePolygonComplete = useCallback(() => {
    if (currentPolygonPoints.length >= 3) {
      setDrawnPolygon(currentPolygonPoints);
      setCurrentPolygonPoints([]);
    }
  }, [currentPolygonPoints]);
  
  const handleRadiusDistanceChange = useCallback((distance: number) => {
    updateFilters({ radiusDistance: distance });
  }, [updateFilters]);
  
  const handleRadiusConfirm = useCallback(() => {
    setIsRadiusConfirmed(true);
  }, []);
  
  const handleSearchAreaClear = useCallback(() => {
    if (searchMethod === 'radius') {
      updateFilters({ radiusCenter: null });
      setIsRadiusConfirmed(false);
    } else {
      setDrawnPolygon(null);
      setCurrentPolygonPoints([]);
    }
  }, [searchMethod, updateFilters]);
  
  const handleModifyRadius = useCallback(() => {
    setIsRadiusConfirmed(false);
  }, []);

  // ===== FILTER HANDLERS =====
  const toggleFilter = useCallback((filterId: SectionKey) => {
    setEnabledFilters(prev => {
      const newSet = new Set(prev);
      if (newSet.has(filterId)) {
        newSet.delete(filterId);
      } else {
        newSet.add(filterId);
      }
      return newSet;
    });
  }, []);

  const toggleSection = useCallback((sectionId: SectionKey) => {
    setOpenSection(current => current === sectionId ? null : sectionId);
  }, []);

  const handleSearch = useCallback(async () => {
    setIsSearching(true);
    
    try {
      // Calculate actual H3 cells for the search area
      let h3Cells: string[] = [];
      let areaInfo = null;
      
      if (searchMethod === 'draw' && drawnPolygon && drawnPolygon.length >= 3) {
        // Convert drawn polygon to H3 cells
        try {
          h3Cells = polygonToH3Cells(drawnPolygon);
          areaInfo = {
            type: 'polygon',
            points: drawnPolygon.length,
            ...getH3Info(h3Cells)
          };
        } catch (error) {
          console.error('Error converting polygon to H3 cells:', error);
          // Fall back to placeholder for now
          h3Cells = ["85264003fffffff", "85264017fffffff"];
          areaInfo = { type: 'polygon', error: error instanceof Error ? error.message : String(error) };
        }
      } else if (searchMethod === 'radius' && filters.radiusCenter && isRadiusConfirmed) {
        // Convert radius search to H3 cells (only if confirmed)
        try {
          h3Cells = radiusToH3Cells(filters.radiusCenter, filters.radiusDistance);
          areaInfo = {
            type: 'radius',
            center: filters.radiusCenter,
            radiusMeters: filters.radiusDistance,
            ...getH3Info(h3Cells)
          };
        } catch (error) {
          console.error('Error converting radius to H3 cells:', error);
          // Fall back to placeholder for now
          h3Cells = ["85264003fffffff", "85264017fffffff"];
          areaInfo = { type: 'radius', error: error instanceof Error ? error.message : String(error) };
        }
      } else {
        // No valid search area defined
        console.error('No valid search area defined');
        setIsSearching(false);
        return;
      }

      // Build API data structure with mandatory parameters
      const apiData: OccurrenceSearchRequest = {
        startTime: filters.dateTimeFrom ? new Date(filters.dateTimeFrom).toISOString() : null,
        endTime: filters.dateTimeTo ? new Date(filters.dateTimeTo).toISOString() : null,
        area: h3Cells, // Now using real H3 indexes
        occurrencePeriod: 11 // Hardcoded for now
      };

      // Only add altitude-related parameters if altitude filter is enabled
      if (enabledFilters.has('altitude')) {
        if (filters.altitudeFilterMode === 'flightEvents') {
          // Flight Events mode - return specific events, not ground/airborne
          const activeEvents = Object.entries(filters.flightEvents)
            .filter(([, isActive]) => isActive)
            .map(([event]) => {
              // Map UI event names to database enum values
              switch (event) {
                case 'landing': return 'landing';
                case 'takeoff': return 'takeoff';
                case 'goAround': return 'airborne';
                case 'touchAndGo': return 'takeoff';
                default: return event;
              }
            });
          
          if (activeEvents.length > 0) {
            apiData.flightPhase = activeEvents;
          }
          // Note: No altMin/altMax for flight events mode
        } else if (filters.altitudeFilterMode === 'groundAirborne') {
          // Ground & Airborne mode
          const phases = [];
          if (filters.includeGroundAircraft) phases.push('ground');
          if (filters.includeAirborneAircraft) phases.push('airborne');
          
          if (phases.length > 0 && phases.length < 2) {
            apiData.flightPhase = phases;
          }

          // Always include altMin and altMax if airborne is selected
          if (filters.includeAirborneAircraft) {
            // Only include altMin if ground aircraft are NOT included
            if (!filters.includeGroundAircraft) {
              apiData.altMin = filters.minAltitude;
            }
            // Always include altMax to filter out high-altitude aircraft
            apiData.altMax = filters.maxAltitude;
          }
        }
      }

      // Only add heading filter if heading filter is enabled
      if (enabledFilters.has('heading')) {
        // Generate array of headings within the range
        const headings = [];
        const start = Math.round(filters.headingStart);
        const end = Math.round(filters.headingEnd);
        
        // Handle range that crosses 360/0 boundary
        if (start <= end) {
          for (let h = start; h <= end; h++) {
            headings.push(h % 360);
          }
        } else {
          // Range crosses 360/0 boundary (e.g., 350 to 10)
          for (let h = start; h <= 360; h++) {
            headings.push(h % 360);
          }
          for (let h = 0; h <= end; h++) {
            headings.push(h);
          }
        }
        
        if (headings.length < 360) { // Only add if not all headings
          apiData.heading = headings;
        }
      }

      // Only add type/class/registration/squawk if that filter is enabled
      if (enabledFilters.has('typeClassReg')) {
        // Add aircraft type filter
        if (filters.occurrenceType) {
          // This would typically map to aircraft types, for now using as is
          apiData.type = [filters.occurrenceType];
        }

        // Add aircraft class filter
        if (filters.aircraftClass) {
          apiData.class = [filters.aircraftClass];
        }

        // Add squawk filter
        if (filters.squawk) {
          // Parse comma-separated squawk codes
          const squawkCodes = filters.squawk.split(',').map(s => s.trim()).filter(s => s);
          if (squawkCodes.length > 0) {
            apiData.squawk = squawkCodes;
          }
        }

        // Add registration filter
        if (filters.registration) {
          // Parse comma-separated registrations
          const registrations = filters.registration.split(',').map(r => r.trim()).filter(r => r);
          if (registrations.length > 0) {
            apiData.registration = registrations;
          }
        }
      }

      // Make the actual API call
      console.log('🔍 Searching occurrences with data:', apiData);
      console.log('📍 Area information:', areaInfo);
      
      const response = await apiClient.searchOccurrences(apiData);
      
      if (response.data) {
        console.log('✅ Search successful!');
        console.log(`📊 Found ${response.data.count} occurrences`);
        console.log(`⏱️ API execution time: ${response.data.execution_time_ms}ms`);
        console.log('🔍 Search results:', response.data.occurrences);
        
        setSearchResults(response.data);
        // Auto-switch to results tab after successful search
        setActiveTab('results');
      } else if (response.error) {
        console.error('❌ Search failed:', response.error);
        // TODO: Show user-friendly error message
      }
      
    } catch (error) {
      console.error('💥 Search error:', error);
      // TODO: Show user-friendly error message
    } finally {
      setIsSearching(false);
    }
  }, [filters, drawnPolygon, searchMethod, enabledFilters, isRadiusConfirmed]);

  const handleOccurrenceClick = useCallback(async (occurrence: OccurrenceResponse) => {
    // If clicking the same occurrence, deselect it
    if (selectedOccurrence?.hex === occurrence.hex && 
        selectedOccurrence?.firstseen === occurrence.firstseen) {
      setSelectedOccurrence(null);
      setFlightTrace(null);
      return;
    }

    setSelectedOccurrence(occurrence);
    setIsLoadingTrace(true);

    try {
      // Use the middle timestamp of the occurrence for the trace API call
      const startTime = new Date(occurrence.firstseen);
      const endTime = new Date(occurrence.lastseen);
      const middleTime = new Date((startTime.getTime() + endTime.getTime()) / 2);
      
      const response = await apiClient.getFlightTrace({
        hex: occurrence.hex,
        timestamp: middleTime.toISOString()
      });

      if (response.data) {
        setFlightTrace(response.data);
        console.log('✈️ Flight trace loaded:', response.data);
      } else {
        // Improved error handling
        const errorMessage = response.error?.detail || response.error?.error || 'Unknown error occurred';
        console.error('Failed to load flight trace:', errorMessage);
        console.error('Full error response:', response.error);
        setFlightTrace(null);
        // TODO: Show user-friendly error toast/notification
      }
    } catch (error) {
      console.error('Error loading flight trace:', error);
      setFlightTrace(null);
      // TODO: Show user-friendly error toast/notification
    } finally {
      setIsLoadingTrace(false);
    }
  }, [selectedOccurrence]);

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header using consistent navbar */}
      <AppNavbar
        icao={icao}
        title={`Occurrences - ${icao?.toUpperCase()}`}
        subtitle="Search and analyze aviation occurrences for this airport"
      />

      {/* Main Content */}
      <div className="flex-1 flex min-h-0">
        <div className="w-1/2 border-r bg-card flex flex-col">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'search' | 'results')} className="h-full flex flex-col">
            <div className="p-4 border-b">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="search" className="flex items-center gap-2">
                  <SearchIcon className="h-4 w-4" />
                  Search
                </TabsTrigger>
                <TabsTrigger value="results" className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Results
                  {searchResults && searchResults.count > 0 && (
                    <Badge className="ml-1 px-1.5 py-0.5 text-xs">
                      {searchResults.count}
                    </Badge>
                  )}
                </TabsTrigger>
              </TabsList>
            </div>
            
            <TabsContent value="search" className="flex-1 overflow-y-auto">
              <OccurrenceSearchFilters
                filters={filters}
                enabledFilters={enabledFilters}
                openSection={openSection}
                searchMethod={searchMethod}
                drawnPolygon={drawnPolygon}
                isSearching={isSearching}
                onFiltersChange={updateFilters}
                onEnabledFiltersChange={toggleFilter}
                onOpenSectionChange={toggleSection}
                onSearchMethodChange={setSearchMethod}
                onDeletePolygon={handleSearchAreaClear}
                onSetEndToNow={setEndToNow}
                onHeadingChange={handleHeadingChange}
                onSearch={handleSearch}
                isRadiusConfirmed={isRadiusConfirmed}
                onModifyRadius={handleModifyRadius}
                isSearchAreaValid={isSearchAreaValid}
              />
            </TabsContent>
            
            <TabsContent value="results" className="flex-1 overflow-y-auto">
              <div className="p-4">
                <OccurrenceResults 
                  results={searchResults} 
                  isLoading={isSearching}
                  onOccurrenceClick={handleOccurrenceClick}
                  selectedOccurrence={selectedOccurrence}
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="w-1/2 flex flex-col">
          <div className="flex-1 relative">
            <OccurrencesMap 
              icao={icao} 
              drawingMode={searchMethod === 'draw' && !drawnPolygon} 
              onPolygonPointAdded={handlePolygonPointAdded}
              onPolygonPointDragged={handlePolygonPointDragged}
              existingPolygon={drawnPolygon}
              currentPoints={currentPolygonPoints}
              radiusMode={searchMethod === 'radius'}
              onMapClick={handleMapClick}
              radiusCenter={filters.radiusCenter}
              radiusDistance={filters.radiusDistance}
              flightTrace={flightTrace}
              showFlightTrace={!isLoadingTrace}
              hoveredTimestamp={hoveredTimestamp}
            />
            
            {/* Flight Data Chart - Toggleable at bottom */}
            <FlightDataChart 
              flightTrace={flightTrace}
              isVisible={!!flightTrace && !isLoadingTrace}
              selectedOccurrence={selectedOccurrence}
              onTimeHover={setHoveredTimestamp}
            />
            
            {/* Simple instruction overlays */}
            {searchMethod === 'draw' && !drawnPolygon && (
              <div className="absolute top-4 left-4 bg-white border rounded-lg shadow-lg p-3 z-[1000]">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Draw Search Area</span>
                  <Badge variant="outline" className="text-xs">
                    {currentPolygonPoints.length} points
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground mb-3">
                  Click map to add points. Drag points to adjust position.
                </p>
                <div className="flex gap-2">
                  {currentPolygonPoints.length >= 3 && (
                    <Button onClick={handlePolygonComplete} size="sm" className="bg-green-600 hover:bg-green-700">
                      Confirm Area
                    </Button>
                  )}
                  {currentPolygonPoints.length > 0 && (
                    <Button 
                      onClick={handleSearchAreaClear} 
                      size="sm" 
                      variant="outline"
                    >
                      Clear Points
                    </Button>
                  )}
                </div>
              </div>
            )}
            
            {/* Radius Search Controls */}
            {searchMethod === 'radius' && filters.radiusCenter && !isRadiusConfirmed && (
              <div className="absolute top-4 left-4 bg-white border rounded-lg shadow-lg p-3 z-[1000] min-w-64">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Search Radius</span>
                  <Badge variant="outline" className="text-xs">
                    {filters.radiusDistance >= 1000 
                      ? `${(filters.radiusDistance / 1000).toFixed(1)} km`
                      : `${filters.radiusDistance} m`
                    }
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground mb-3">
                  Adjust radius and confirm search area.
                </p>
                
                {/* Radius Slider */}
                <div className="space-y-2 mb-3">
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>100m</span>
                    <span>50km</span>
                  </div>
                  <div className="px-1">
                    <input
                      type="range"
                      min="100"
                      max="50000"
                      step="100"
                      value={filters.radiusDistance}
                      onChange={(e) => handleRadiusDistanceChange(Number(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer 
                                 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
                                 [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-4 
                                 [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:rounded-full 
                                 [&::-webkit-slider-thumb]:bg-blue-500 [&::-webkit-slider-thumb]:cursor-pointer
                                 [&::-webkit-slider-thumb]:shadow-md [&::-webkit-slider-thumb]:transition-all
                                 [&::-webkit-slider-thumb]:hover:bg-blue-600 [&::-webkit-slider-thumb]:hover:scale-110
                                 [&::-moz-range-thumb]:h-4 [&::-moz-range-thumb]:w-4 [&::-moz-range-thumb]:rounded-full 
                                 [&::-moz-range-thumb]:bg-blue-500 [&::-moz-range-thumb]:cursor-pointer 
                                 [&::-moz-range-thumb]:border-none [&::-moz-range-thumb]:shadow-md"
                    />
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button onClick={handleRadiusConfirm} size="sm" className="bg-green-600 hover:bg-green-700">
                    Confirm Radius
                  </Button>
                  <Button 
                    onClick={handleSearchAreaClear} 
                    size="sm" 
                    variant="outline"
                  >
                    Clear Area
                  </Button>
                </div>
              </div>
            )}
            
            {searchMethod === 'radius' && !filters.radiusCenter && (
              <div className="absolute top-4 left-4 bg-white border rounded-lg shadow-lg px-3 py-2 z-[1000]">
                <p className="text-sm">Click map to set search center</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 