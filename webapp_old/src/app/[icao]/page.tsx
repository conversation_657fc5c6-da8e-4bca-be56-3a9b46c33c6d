'use client';

import React from 'react';
import AppNavbar from '../../components/AppNavbar';
import { useAirportData } from '../../lib/hooks/useAirportData';

interface AirportPageProps {
  params: Promise<{ icao: string }>;
}

export default function AirportPage({ params }: AirportPageProps) {
  const resolvedParams = React.use(params);
  const { icao } = resolvedParams;
  
  // Fetch airport information
  const { airport, loading: airportLoading, error: airportError } = useAirportData(icao);
  
  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header - Uses the same AppNavbar as the live page */}
      <AppNavbar
        airport={airport}
        airportLoading={airportLoading}
        airportError={airportError}
        icao={icao}
      />

      {/* Main Content - Clean and minimal for now */}
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-4">
            {airport?.name || icao.toUpperCase()}
          </h1>
          <p className="text-muted-foreground">
            Airport dashboard coming soon
          </p>
        </div>
      </div>
    </div>
  );
} 