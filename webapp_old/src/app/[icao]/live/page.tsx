'use client';

import React from 'react';
import { PlaneIcon, PlaneLanding, PlaneTakeoff } from 'lucide-react';
import AppNavbar from '../../../components/AppNavbar';
import { useAirportData } from '../../../lib/hooks/useAirportData';
import { useAirportEvents } from '../../../lib/hooks/useAirportEvents';
import { RunwayEventResponse, FlightPhaseEnum } from '../../../types/api';
import dynamic from 'next/dynamic';

// Dynamically import the map to avoid SSR issues
const AirportMap = dynamic(() => import('../../../components/AirportMap'), { 
  ssr: false,
  loading: () => (
    <div className="h-full w-full bg-muted rounded-lg border-2 border-dashed border-muted-foreground/25 flex items-center justify-center">
      <div className="text-center text-muted-foreground">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-2"></div>
        <p className="text-sm">Loading Map...</p>
      </div>
    </div>
  )
});

interface Flight {
  id: string;
  time: string;
  callSign: string;
  aircraft: string;
  runway: string;
  type: 'arrival' | 'departure';
  date: string;
}

interface TimelineEntry extends Flight {
  stackedFlights?: Flight[];
  position: 'left' | 'right';
}

interface DateSection {
  date: string;
  label: string;
  entries: TimelineEntry[];
}

// Convert API runway event to Flight interface
const convertEventToFlight = (event: RunwayEventResponse): Flight => {
  const eventDate = new Date(event.time);
  const time = eventDate.toLocaleTimeString('en-US', { 
    hour12: false, 
    hour: '2-digit', 
    minute: '2-digit' 
  });
  const date = eventDate.toISOString().split('T')[0];
  
  return {
    id: `${event.hex}-${event.time}`, // Use hex + timestamp as unique ID
    time,
    callSign: event.registration || event.hex, // Use registration if available, otherwise hex
    aircraft: event.flight || event.category || 'UNK', // Use flight number, category, or unknown
    runway: event.runway,
    type: event.event_type === FlightPhaseEnum.TAKEOFF ? 'departure' : 'arrival',
    date
  };
};

function FlightCard({ flight, stackedCount }: { flight: Flight; stackedCount?: number }) {
  const isArrival = flight.type === 'arrival';
  const hasStack = stackedCount && stackedCount > 1;
  
  return (
    <div className="relative">
      {/* Speech bubble card with fixed dimensions */}
      <div className="relative bg-card border rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 w-80 h-12 flex items-center">
        {/* Arrow pointer with matching border */}
        <div className={`absolute top-1/2 -translate-y-1/2 ${
          isArrival 
            ? 'right-0 translate-x-full' 
            : 'left-0 -translate-x-full'
        }`}>
          <div className={`w-0 h-0 ${
            isArrival
              ? 'border-l-[12px] border-l-card border-y-[6px] border-y-transparent'
              : 'border-r-[12px] border-r-card border-y-[6px] border-y-transparent'
          }`} />
          <div className={`absolute top-1/2 -translate-y-1/2 w-0 h-0 ${
            isArrival
              ? '-left-px border-l-[11px] border-l-border border-y-[5px] border-y-transparent'
              : '-right-px border-r-[11px] border-r-border border-y-[5px] border-y-transparent'
          }`} />
        </div>
        
        {/* Fixed positioning for perfect alignment */}
        <div className="relative w-full px-3">
          {/* Time - left aligned at position 0 */}
          <div className="absolute left-3 top-1/2 -translate-y-1/2 font-bold text-lg">
            {flight.time}
          </div>
          
          {/* Call Sign - left aligned at fixed offset */}
          <div className={`absolute left-20 top-1/2 -translate-y-1/2 font-bold text-lg ${
            isArrival ? 'text-blue-600' : 'text-green-600'
          }`}>
            {flight.callSign}
          </div>
          
          {/* Aircraft - left aligned at fixed offset */}
          <div className="absolute left-44 top-1/2 -translate-y-1/2 font-semibold text-base text-muted-foreground">
            {flight.aircraft}
          </div>
          
          {/* Runway - right aligned */}
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <div className="bg-muted px-2 py-0.5 rounded-full text-xs font-medium text-muted-foreground whitespace-nowrap">
              {flight.runway}
            </div>
          </div>
        </div>
      </div>
      
      {/* Stack count bubble */}
      {hasStack && (
        <div className="absolute -top-2 -right-2 bg-muted-foreground text-background text-sm rounded-full w-6 h-6 flex items-center justify-center font-bold shadow-sm">
          {stackedCount}
        </div>
      )}
    </div>
  );
}

function DateBubble({ label }: { label: string }) {
  return (
    <div className="flex justify-center mb-6">
      <div className="bg-muted px-3 py-1 rounded-full text-sm font-medium text-muted-foreground">
        {label}
      </div>
    </div>
  );
}

// Utility functions
const getTimeInMinutes = (timeString: string): number => {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
};

const timeDifferenceInMinutes = (time1: string, time2: string): number => {
  return Math.abs(getTimeInMinutes(time1) - getTimeInMinutes(time2));
};

const getDateLabel = (date: string): string => {
  const today = new Date().toISOString().split('T')[0];
  const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];
  
  if (date === today) return 'Today';
  if (date === yesterday) return 'Yesterday';
  return new Date(date).toLocaleDateString('en-US', { 
    weekday: 'long',
    month: 'short', 
    day: 'numeric' 
  });
};

// Process flights into timeline entries with stacking logic
const processFlightsIntoTimelineEntries = (flights: Flight[]): TimelineEntry[] => {
  const entries: TimelineEntry[] = [];
  const processedFlightIds = new Set<string>();
  
  for (const flight of flights) {
    if (processedFlightIds.has(flight.id)) continue;
    
    // Find subsequent flights for same aircraft within 15 minutes
    const stackedFlights: Flight[] = [];
    for (const candidateFlight of flights.slice(flights.indexOf(flight) + 1)) {
      if (candidateFlight.callSign === flight.callSign && 
          candidateFlight.date === flight.date &&
          timeDifferenceInMinutes(flight.time, candidateFlight.time) <= 15) {
        stackedFlights.push(candidateFlight);
        processedFlightIds.add(candidateFlight.id);
      }
    }
    
    entries.push({
      ...flight,
      stackedFlights: stackedFlights.length > 0 ? stackedFlights : undefined,
      position: flight.type === 'arrival' ? 'left' : 'right'
    });
    
    processedFlightIds.add(flight.id);
  }
  
  return entries;
};

// Group entries by date
const groupEntriesByDate = (entries: TimelineEntry[]): DateSection[] => {
  const grouped = entries.reduce((acc, entry) => {
    if (!acc[entry.date]) {
      acc[entry.date] = [];
    }
    acc[entry.date].push(entry);
    return acc;
  }, {} as Record<string, TimelineEntry[]>);
  
  return Object.entries(grouped).map(([date, entries]) => ({
    date,
    label: getDateLabel(date),
    entries
  }));
};

// Calculate spacing for timeline entries with proper minimum gaps
const calculateTimelineSpacing = (sections: DateSection[]): Record<string, number[]> => {
  const spacing: Record<string, number[]> = {};
  const CARD_HEIGHT = 48; // Fixed height (h-12 = 48px)
  const MIN_GAP_SAME_SIDE = 10; // Gap for same-side consecutive cards
  const MIN_GAP_OPPOSITE_SIDE = 32; // Minimum gap even for opposite sides
  const DATE_SECTION_GAP = 80;
  
  let globalOffset = 0;
  
  for (const section of sections) {
    const sectionSpacing: number[] = [];
    
    for (let i = 0; i < section.entries.length; i++) {
      if (i === 0) {
        sectionSpacing.push(globalOffset);
      } else {
        const prevEntry = section.entries[i - 1];
        const currentEntry = section.entries[i];
        const prevPosition = sectionSpacing[i - 1];
        
        // Always ensure minimum spacing, even for opposite sides
        const nextPosition = prevEntry.position === currentEntry.position
          ? prevPosition + CARD_HEIGHT + MIN_GAP_SAME_SIDE // Same side
          : prevPosition + MIN_GAP_OPPOSITE_SIDE; // Opposite side with proper minimum
        
        sectionSpacing.push(nextPosition);
      }
    }
    
    spacing[section.date] = sectionSpacing;
    
    if (sectionSpacing.length > 0) {
      globalOffset = Math.max(...sectionSpacing) + CARD_HEIGHT + DATE_SECTION_GAP;
    }
  }
  
  return spacing;
};

interface AirportDashboardProps {
  params: Promise<{ icao: string }>;
}

/**
 * Airport Dashboard Page - Now Powered by Real API Data!
 * 
 * This component demonstrates the power of our monorepo structure:
 * 
 * 🎯 **Type Safety Across the Stack**
 * - API defines AirportResponse and AirportEventsResponse schemas in Pydantic
 * - Webapp TypeScript types mirror the API schema exactly
 * - Frontend components use the same types
 * - Compile-time errors if API and frontend get out of sync
 * 
 * 🔄 **Single Source of Truth**
 * - No hardcoded airport coordinates or flight data
 * - No duplicate data structures
 * - Changes to API schema automatically flow to frontend
 * 
 * 🚀 **Developer Experience**
 * - IntelliSense and autocomplete for API data
 * - Refactoring works across API and frontend
 * - Clear error messages with proper typing
 * 
 * 🛠️ **Maintenance Benefits**
 * - Add new airport/event fields in one place (API schema)
 * - Frontend automatically gets new fields with proper types
 * - Real-time data updates every 5 seconds
 * 
 * 📡 **Real-time Features**
 * - Automatic polling every 5 seconds
 * - Shows live takeoff/landing events from the last hour
 * - Intelligent runway detection using H3 spatial indexing
 */
export default function AirportDashboard({ params }: AirportDashboardProps) {
  const resolvedParams = React.use(params);
  const { icao } = resolvedParams;
  
  // Fetch airport information
  const { airport, loading: airportLoading, error: airportError } = useAirportData(icao);
  
  // Fetch live airport events (polling every 5 seconds)
  const { 
    events, 
    lastUpdated, 
    loading: eventsLoading, 
    error: eventsError 
  } = useAirportEvents(icao);
  
  // Convert API events to Flight interface and sort by time (newest first)
  const flights: Flight[] = React.useMemo(() => {
    return events
      .map(convertEventToFlight)
      .sort((a, b) => {
        // Sort by date first, then by time (newest first)
        if (a.date !== b.date) {
          return b.date.localeCompare(a.date);
        }
        return b.time.localeCompare(a.time);
      });
  }, [events]);
  
  const timelineEntries = processFlightsIntoTimelineEntries(flights);
  const dateSections = groupEntriesByDate(timelineEntries);
  const spacing = calculateTimelineSpacing(dateSections);
  
  const totalHeight = Math.max(...Object.values(spacing).flat(), 300) + 150;
  
  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header - Now uses reusable AppNavbar! */}
      <AppNavbar
        airport={airport}
        airportLoading={airportLoading}
        airportError={airportError}
        icao={icao}
        lastUpdated={lastUpdated || undefined}
        isLoading={eventsLoading}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col lg:flex-row min-h-0">
        {/* Timeline Section */}
        <div className="flex-1 lg:w-1/2 overflow-auto">
          <div className="p-4 lg:p-6">
            {/* Events error display */}
            {eventsError && (
              <div className="mb-4 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
                <p className="text-destructive text-sm">
                  Error loading events: {eventsError}
                </p>
              </div>
            )}
            
            {/* Loading state for initial load */}
            {eventsLoading && events.length === 0 && (
              <div className="flex items-center justify-center py-8">
                <div className="text-center text-muted-foreground">
                  <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-2"></div>
                  <p className="text-sm">Loading events...</p>
                </div>
              </div>
            )}
            
            {/* No events message */}
            {!eventsLoading && events.length === 0 && !eventsError && (
              <div className="flex items-center justify-center py-8">
                <div className="text-center text-muted-foreground">
                  <PlaneIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">No recent activity</p>
                  <p className="text-sm">No takeoffs or landings in the last hour</p>
                </div>
              </div>
            )}
            
            {/* Column Headers */}
            {events.length > 0 && (
              <div className="hidden lg:flex justify-between items-center mb-8 max-w-4xl mx-auto">
                <div className="w-1/2 text-center">
                  <div className="flex items-center justify-center gap-2">
                    <PlaneLanding className="h-5 w-5 text-blue-600" />
                    <h3 className="text-lg font-semibold text-blue-600">Arrivals</h3>
                  </div>
                </div>
                <div className="w-8" />
                <div className="w-1/2 text-center">
                  <div className="flex items-center justify-center gap-2">
                    <PlaneTakeoff className="h-5 w-5 text-green-600" />
                    <h3 className="text-lg font-semibold text-green-600">Departures</h3>
                  </div>
                </div>
              </div>
            )}
            
            {/* Timeline Content */}
            {events.length > 0 && (
              <div className="relative max-w-4xl mx-auto">
                {/* Timeline Line */}
                <div className="hidden lg:block absolute left-1/2 transform -translate-x-px top-0 w-px bg-border"
                     style={{ height: `${totalHeight}px` }} />
                
                {/* Timeline Content */}
                <div className="space-y-8 lg:space-y-0">
                  {dateSections.map((section) => (
                    <div key={section.date}>
                      {/* Date Bubble */}
                      <div className="lg:absolute lg:left-1/2 lg:transform lg:-translate-x-1/2"
                           style={{ top: `${Math.min(...spacing[section.date]) - 40}px` }}>
                        <DateBubble label={section.label} />
                      </div>
                      
                      {/* Entries for this date */}
                      {section.entries.map((entry, index) => {
                        const topSpacing = spacing[section.date][index];
                        const stackedCount = entry.stackedFlights ? entry.stackedFlights.length + 1 : undefined;
                        
                        return (
                          <div key={entry.id} className="relative">
                            {/* Mobile Layout */}
                            <div className="lg:hidden mb-4">
                              <FlightCard flight={entry} stackedCount={stackedCount} />
                            </div>
                            
                            {/* Desktop Layout */}
                            <div className="hidden lg:flex items-center absolute w-full"
                                 style={{ top: `${topSpacing}px` }}>
                              {/* Arrivals Column */}
                              <div className="w-1/2 pr-4 flex justify-end">
                                {entry.position === 'left' && (
                                  <FlightCard flight={entry} stackedCount={stackedCount} />
                                )}
                              </div>
                              
                              {/* Timeline Dot */}
                              <div className="absolute left-1/2 transform -translate-x-1/2 top-1/2 -translate-y-1/2">
                                <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                              </div>
                              
                              {/* Departures Column */}
                              <div className="w-1/2 pl-4 flex justify-start">
                                {entry.position === 'right' && (
                                  <FlightCard flight={entry} stackedCount={stackedCount} />
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Map Section - Now displays real airport location and runways! */}
        <div className="lg:w-1/2 lg:border-l bg-card">
          <div className="p-2 lg:p-3 h-64 lg:h-full">
            {/* AirportMap now receives real airport data with coordinates and runways */}
            <AirportMap airport={airport} className="h-full" />
          </div>
        </div>
      </div>
    </div>
  );
} 