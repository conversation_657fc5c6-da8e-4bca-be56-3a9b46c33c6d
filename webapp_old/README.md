# TarmacTrack Webapp

This is the Next.js frontend application for TarmacTrack, containerized and production-ready.

## Development

### Local Development
```bash
npm install
npm run dev
```

### Docker Development (with hot reload)
```bash
# From project root
docker-compose up -d webapp

# Or use make command
make webapp
```

## Production

### Docker Production Build
```bash
# From project root
docker-compose -f docker-compose.yml up -d webapp

# Or use make command
make prod-up
```

### Manual Production Build
```bash
npm run build
npm start
```

## Environment Variables

- `NEXT_PUBLIC_API_URL`: API endpoint URL
  - Development: `http://localhost:8001`
  - Docker: `http://api:8000` (internal network)

## Features

- **Dynamic Airport Routes**: Access any airport dashboard via `/[ICAO]` (e.g., `/KORD`, `/KJFK`)
- **Interactive Timeline**: Visual timeline showing arrivals (left) and departures (right)
- **Flight Cards**: Detailed flight information including time, call sign, aircraft type, and runway
- **Map Integration**: Real-time airport maps with runway overlays
- **Responsive Design**: Optimized for desktop with full-screen no-scroll layout
- **Modern UI**: Built with Tailwind CSS and Shadcn/ui components

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS
- **Components**: Shadcn/ui
- **Icons**: Lucide React
- **Language**: TypeScript
- **Maps**: React Leaflet

## Docker Features

- **Multi-stage build**: Optimized production images
- **Non-root user**: Security best practices
- **Health checks**: Container monitoring
- **Hot reload**: Development productivity
- **Standalone output**: Minimal runtime dependencies

## Architecture

The webapp uses:
- Next.js 15 with App Router
- TypeScript for type safety
- Tailwind CSS for styling
- React Leaflet for mapping
- Optimized Docker builds for production

Visit [http://localhost:3000](http://localhost:3000) when running.
