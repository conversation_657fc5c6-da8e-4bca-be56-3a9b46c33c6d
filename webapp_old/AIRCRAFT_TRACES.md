# Aircraft Traces Feature

## Overview

The aircraft traces feature displays flight paths for aircraft by showing their historical positions over a specified time window. This provides valuable insights into aircraft movement patterns, approach paths, and flight trajectories.

## API Changes Required

### Updated Response Structure

The `AircraftPositionResponse` now includes an optional `trace` field:

```typescript
export interface AircraftPositionResponse {
  // ... existing fields ...
  trace?: AircraftTracePoint[];
}

export interface AircraftTracePoint {
  time: string;
  alt_baro?: number;
  alt_geom?: number;
  gs?: number;
  ias?: number;
  track?: number;
  baro_rate?: number;
  h3_index?: string;
  flight_phase?: FlightPhaseEnum;
}
```

### Expected API Behavior

- The `lookback_seconds` parameter controls how far back to include trace points
- Each aircraft's `trace` array contains historical positions ordered chronologically (oldest first)
- The current position is the main `AircraftPositionResponse` object
- Trace points exclude aircraft-level fields (hex, flight, squawk, category) that don't change

## Frontend Features

### Trail Visualization

- **Trail Display**: Dashed lines connecting historical positions to current position
- **Color Coding**: Trails use the same altitude-based color scheme as aircraft icons
- **Opacity Management**: Longer trails are more transparent to reduce visual clutter
- **Performance Limits**: Trails are limited to 50 points maximum for performance

### User Controls

- **Trail Toggle**: Eye icon in map controls to show/hide all trails
- **Status Indicator**: Shows trail visibility status in the top-left indicator
- **Per-Aircraft Info**: Aircraft popups show number of available trail points

### Configuration

All trail settings are centralized in `aircraft-config.ts`:

```typescript
// Trail settings
MAX_TRAIL_LENGTH: 50,        // Maximum trace points per aircraft
MIN_TRAIL_POINTS: 2,         // Minimum points needed to show trail
TRAIL_WEIGHT: 2,             // Line thickness
TRAIL_DASH_PATTERN: '5, 5',  // Dash pattern
TRAIL_OPACITY: {
  short: 0.8,   // < 10 points
  medium: 0.7,  // 10-20 points  
  long: 0.6     // > 20 points
}
```

## Technical Implementation

### Components

1. **AircraftTrails**: Renders trail polylines on the map
2. **AircraftMarkers**: Updated to show trace count in popups
3. **AirportMap**: Integrated trail toggle controls

### Performance Optimizations

- Trail length limits prevent excessive DOM elements
- Coordinate validation prevents invalid H3 indices from breaking trails
- Configurable opacity reduces visual complexity
- Trails can be toggled off entirely for better performance

### Error Handling

- Invalid H3 indices are logged as warnings but don't break the trail
- Missing trace data gracefully falls back to showing only current position
- Minimum point requirements prevent single-point "trails"

## Usage Examples

### Basic Trail Display

```typescript
<AircraftTrails 
  positions={aircraftPositions} 
  showTrails={true}
  maxTrailLength={30}
/>
```

### Custom Configuration

Modify `aircraft-config.ts` to adjust trail appearance:

```typescript
// Shorter, more opaque trails
MAX_TRAIL_LENGTH: 20,
TRAIL_OPACITY: {
  short: 0.9,
  medium: 0.8,
  long: 0.7
}
```

## Future Enhancements

- **Altitude Visualization**: Trail color could change along the path based on altitude changes
- **Speed Visualization**: Trail thickness could vary based on ground speed
- **Time-based Filtering**: Show only trails from specific time ranges
- **Trail Clustering**: Merge nearby trails for busy airspace
- **Historical Playback**: Animate aircraft movement through their trails

## Notes for Backend Implementation

When implementing the trace feature in your API:

1. Ensure traces are ordered chronologically (oldest first)
2. Consider adding a `max_trace_points` parameter to limit response size
3. Include H3 indices for all trace points for coordinate consistency
4. Consider caching trace data for frequently requested areas
5. Monitor response sizes as traces can significantly increase payload 