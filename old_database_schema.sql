--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Ubuntu 17.5-1.pgdg22.04+1)
-- Dumped by pg_dump version 17.5 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: -
--

-- *not* creating schema, since initdb creates it


--
-- Name: timescaledb; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS timescaledb WITH SCHEMA public;


--
-- Name: EXTENSION timescaledb; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION timescaledb IS 'Enables scalable inserts and complex queries for time-series data (Community Edition)';


--
-- Name: timescale_functions; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA timescale_functions;


--
-- Name: SCHEMA timescale_functions; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON SCHEMA timescale_functions IS 'This schema contains helper functions for Timescale Cloud';


--
-- Name: timescaledb_toolkit; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS timescaledb_toolkit WITH SCHEMA public;


--
-- Name: EXTENSION timescaledb_toolkit; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION timescaledb_toolkit IS 'Library of analytical hyperfunctions, time-series pipelining, and other SQL utilities';


--
-- Name: h3; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS h3 WITH SCHEMA public;


--
-- Name: EXTENSION h3; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION h3 IS 'H3 bindings for PostgreSQL';


--
-- Name: pg_buffercache; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_buffercache WITH SCHEMA public;


--
-- Name: EXTENSION pg_buffercache; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_buffercache IS 'examine the shared buffer cache';


--
-- Name: pg_stat_statements; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA public;


--
-- Name: EXTENSION pg_stat_statements; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_stat_statements IS 'track planning and execution statistics of all SQL statements executed';


--
-- Name: postgis; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS postgis WITH SCHEMA public;


--
-- Name: EXTENSION postgis; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION postgis IS 'PostGIS geometry and geography spatial types and functions';


--
-- Name: postgres_fdw; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS postgres_fdw WITH SCHEMA public;


--
-- Name: EXTENSION postgres_fdw; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION postgres_fdw IS 'foreign-data wrapper for remote PostgreSQL servers';


--
-- Name: aircraft_category_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.aircraft_category_enum AS ENUM (
    'A0',
    'A1',
    'A2',
    'A3',
    'A4',
    'A5',
    'A6',
    'A7',
    'B0',
    'B1',
    'B2',
    'B3',
    'B4',
    'B5',
    'B6',
    'B7',
    'C0',
    'C1',
    'C2',
    'C3',
    'C4',
    'C5',
    'C6',
    'C7',
    'D0',
    'D1',
    'D2',
    'D3',
    'D4',
    'D5',
    'D6',
    'D7',
    'UNKNOWN'
);


--
-- Name: flight_phase_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.flight_phase_enum AS ENUM (
    'ground',
    'takeoff',
    'landing',
    'airborne'
);


--
-- Name: runway_surface_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.runway_surface_enum AS ENUM (
    'ASPHALT',
    'CONCRETE',
    'GRASS',
    'GRAVEL',
    'DIRT',
    'WATER',
    'UNKNOWN',
    'OTHER'
);


--
-- Name: create_playground(regclass, boolean); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_playground(src_hypertable regclass, compressed boolean DEFAULT false) RETURNS text
    LANGUAGE plpgsql
    SET search_path TO 'pg_catalog', 'pg_temp'
    AS $_$
DECLARE
    _table_name NAME;
    _schema_name NAME;
    _src_relation NAME;
    _playground_table_fqn NAME;
    _chunk_name NAME;
    _chunk_check BOOL;
    _playground_schema_check BOOL;
    _next_id INTEGER;
    _dimension TEXT;
    _interval TEXT;
    _segmentby_cols TEXT;
    _orderby_cols TEXT;
BEGIN
    SELECT EXISTS(SELECT 1 FROM information_schema.schemata
    WHERE schema_name = 'tsdb_playground') INTO _playground_schema_check;

    IF NOT _playground_schema_check THEN
        RAISE EXCEPTION '"tsdb_playground" schema must be created before running this';
    END IF;

    -- get schema and table name
    SELECT n.nspname, c.relname INTO _schema_name, _table_name
    FROM pg_class c
    INNER JOIN pg_namespace n ON (n.oid = c.relnamespace)
    INNER JOIN timescaledb_information.hypertables i ON (i.hypertable_name = c.relname )
    WHERE c.oid = src_hypertable;

    IF _table_name IS NULL THEN
        RAISE EXCEPTION '% is not a hypertable', src_hypertable;
    END IF;

    SELECT EXISTS(SELECT 1 FROM timescaledb_information.chunks WHERE hypertable_name = _table_name AND hypertable_schema = _schema_name) INTO _chunk_check;

    IF NOT _chunk_check THEN
        RAISE EXCEPTION '% has no chunks for playground testing', src_hypertable;
    END IF;

    EXECUTE pg_catalog.format($$ CREATE SEQUENCE IF NOT EXISTS tsdb_playground.%I $$, _table_name||'_seq');
    SELECT pg_catalog.nextval('tsdb_playground.' || pg_catalog.quote_ident(_table_name || '_seq')) INTO _next_id;

    SELECT pg_catalog.format('%I.%I', _schema_name, _table_name) INTO _src_relation;

    SELECT pg_catalog.format('tsdb_playground.%I', _table_name || '_' || _next_id::text) INTO _playground_table_fqn;

    EXECUTE pg_catalog.format(
        $$ CREATE TABLE %s (like %s including comments including constraints including defaults including indexes) $$
        , _playground_table_fqn, _src_relation
        );

    -- get dimension column from src ht for partitioning playground ht
    SELECT column_name, time_interval INTO _dimension, _interval FROM timescaledb_information.dimensions WHERE hypertable_name = _table_name AND hypertable_schema = _schema_name LIMIT 1;

    PERFORM public.create_hypertable(_playground_table_fqn::REGCLASS, _dimension::NAME, chunk_time_interval := _interval::interval);

    -- Ideally, it should pick up the latest complete chunk (second last chunk) from this hypertable.
    -- If num_chunks > 1 then it will get true, converted into 1, taking the second row, otherwise it'll get false converted to 0 and get no offset.
    SELECT
        format('%I.%I',chunk_schema,chunk_name)
    INTO STRICT
        _chunk_name
    FROM
        timescaledb_information.chunks
    WHERE
        hypertable_schema = _schema_name AND
        hypertable_name = _table_name
    ORDER BY
        chunk_creation_time DESC OFFSET (
            SELECT
                (num_chunks > 1)::integer
            FROM timescaledb_information.hypertables
            WHERE
                hypertable_name = _table_name)
    LIMIT 1;
	EXECUTE pg_catalog.format($$ INSERT INTO %s SELECT * FROM %s $$, _playground_table_fqn, _chunk_name);

    IF compressed THEN
        --retrieve compression settings from source hypertable
        SELECT segmentby INTO _segmentby_cols
        FROM timescaledb_information.hypertable_compression_settings 
        WHERE hypertable = _src_relation::REGCLASS;

		SELECT orderby INTO _orderby_cols
		FROM timescaledb_information.hypertable_compression_settings 
        WHERE hypertable = _src_relation::REGCLASS;

        IF (_segmentby_cols IS NOT NULL) AND (_orderby_cols IS NOT NULL) THEN
            EXECUTE pg_catalog.format(
                $$ ALTER TABLE %s SET(timescaledb.compress, timescaledb.compress_segmentby = %I, timescaledb.compress_orderby = %I) $$
                , _playground_table_fqn, _segmentby_cols, _orderby_cols
                );
        ELSE
            EXECUTE pg_catalog.format(
                $$ ALTER TABLE %s SET(timescaledb.compress) $$
                , _playground_table_fqn
                );
        END IF;
        -- get playground chunk and compress
    PERFORM public.compress_chunk(public.show_chunks(_playground_table_fqn::REGCLASS));
    END IF;

	RETURN _playground_table_fqn;
END
$_$;


--
-- Name: derive_track_data(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.derive_track_data() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    prev_time timestamptz;
    prev_h3_index public.h3index;
    prev_track int2;
    prev_point point;
    current_point point;
    prev_lat float8;
    prev_lon float8;
    current_lat float8;
    current_lon float8;
    grid_distance int4;
    calculated_track float8;
BEGIN
    -- This function will be called BEFORE INSERT.
    -- NEW refers to the row that is about to be inserted.

    -- Only process if track is NULL and h3_index is available
    IF NEW.track IS NULL AND NEW.h3_index IS NOT NULL THEN
        
        RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - Track is NULL, attempting to derive.', NEW.hex, NEW.time;

        -- Get the most recent position data for this aircraft
        SELECT "time", h3_index, track
        INTO prev_time, prev_h3_index, prev_track
        FROM public.aircraft_pos
        WHERE hex = NEW.hex
          AND "time" < NEW."time" -- Strictly older records
          AND "time" >= (NEW."time" - INTERVAL '30 seconds') -- Within 30-second window
          AND h3_index IS NOT NULL -- Must have h3_index for grid distance calculation
        ORDER BY "time" DESC
        LIMIT 1;

        -- If a previous record was found
        IF FOUND THEN
            RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - Found previous record at time:%.', NEW.hex, NEW.time, prev_time;
            
            -- Calculate grid distance using h3_grid_distance function
            grid_distance := h3_grid_distance(prev_h3_index, NEW.h3_index);
            
            RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - Grid distance: %.', NEW.hex, NEW.time, grid_distance;
            
            -- If grid distance is at least 2, calculate track based on bearing
            IF grid_distance >= 2 THEN
                -- Get lat/lon coordinates from h3 indices using h3_cell_to_lat_lng
                prev_point := h3_cell_to_lat_lng(prev_h3_index);
                current_point := h3_cell_to_lat_lng(NEW.h3_index);
                
                -- Extract lat/lon from PostgreSQL point type (x=longitude, y=latitude)
                prev_lat := prev_point[1];  -- y component = latitude
                prev_lon := prev_point[0];  -- x component = longitude
                current_lat := current_point[1];  -- y component = latitude
                current_lon := current_point[0];  -- x component = longitude
                
                -- Calculate bearing in degrees using atan2
                -- Formula: θ = atan2(sin(Δlong).cos(lat2), cos(lat1).sin(lat2) − sin(lat1).cos(lat2).cos(Δlong))
                -- Convert result from radians to degrees and normalize to 0-360 range
                calculated_track := degrees(
                    atan2(
                        sin(radians(current_lon - prev_lon)) * cos(radians(current_lat)),
                        cos(radians(prev_lat)) * sin(radians(current_lat)) - 
                        sin(radians(prev_lat)) * cos(radians(current_lat)) * cos(radians(current_lon - prev_lon))
                    )
                );
                
                -- Normalize to 0-360 range
                IF calculated_track < 0 THEN
                    calculated_track := calculated_track + 360;
                END IF;
                
                -- Round to nearest integer for track field (int2)
                NEW.track := round(calculated_track)::int2;
                
                RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - Calculated track: % degrees.', NEW.hex, NEW.time, NEW.track;
                
            -- If grid distance is less than 2, copy previous track
            ELSIF prev_track IS NOT NULL THEN
                NEW.track := prev_track;
                RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - Copied previous track: % degrees.', NEW.hex, NEW.time, NEW.track;
            ELSE
                RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - Previous track is NULL, cannot copy.', NEW.hex, NEW.time;
            END IF;
            
        ELSE
            RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - No previous record found in 30-second window.', NEW.hex, NEW.time;
        END IF;
        
    ELSIF NEW.track IS NOT NULL THEN
        RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - Track data provided: % degrees.', NEW.hex, NEW.time, NEW.track;
    ELSE
        RAISE NOTICE '[TRACK_DERIVATION] For hex:%, time:% - No h3_index available, cannot derive track.', NEW.hex, NEW.time;
    END IF;

    RETURN NEW; -- Return the (potentially modified) row
END;
$$;


--
-- Name: detect_takeoff_landing(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.detect_takeoff_landing() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    prev_time timestamptz;
    prev_phase public.flight_phase_enum;
BEGIN
    -- Only query if we need to (when inserting airborne or ground)
    IF NEW.flight_phase = 'airborne' OR NEW.flight_phase = 'ground' THEN
        -- Select only what we need instead of entire record
        SELECT "time", flight_phase INTO prev_time, prev_phase
        FROM aircraft_positions 
        WHERE hex = NEW.hex 
          AND "time" > (NEW.time - interval '11 seconds')
          AND "time" < NEW.time
        ORDER BY "time" DESC 
        LIMIT 1;
        
        -- If previous record exists, check for state transitions
        IF FOUND THEN
            -- Detect takeoff: ground → airborne
            IF NEW.flight_phase = 'airborne' AND prev_phase = 'ground' THEN
                NEW.flight_phase := 'takeoff';
            
            -- Detect landing: airborne → ground
            ELSIF NEW.flight_phase = 'ground' AND prev_phase = 'airborne' THEN
                -- Update the previous record to be 'landing' instead of 'airborne'
                UPDATE aircraft_positions 
                SET flight_phase = 'landing'
                WHERE hex = NEW.hex AND "time" = prev_time;
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;


--
-- Name: detect_takeoff_landing_unified(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.detect_takeoff_landing_unified() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    prev_time timestamptz;
    prev_phase public.flight_phase_enum;
BEGIN
    -- This function will be called BEFORE INSERT.
    -- NEW refers to the row that is about to be inserted.

    -- Only query if the new row's flight_phase needs evaluation
    IF NEW.flight_phase = 'airborne' OR NEW.flight_phase = 'ground' THEN

        RAISE NOTICE '[UNIFIED_TRIGGER] For NEW hex:%, time:%, incoming_phase:% - Evaluating.', NEW.hex, NEW.time, NEW.flight_phase;

        -- Select the time and flight_phase of the single most recent record *prior* to the NEW record
        SELECT "time", flight_phase
        INTO prev_time, prev_phase
        FROM public.aircraft_pos -- Ensure this is your current table name
        WHERE hex = NEW.hex
          AND "time" < NEW."time" -- Strictly older records
          AND "time" >= (NEW."time" - INTERVAL '11 seconds') -- Within the 11-second window
        ORDER BY "time" DESC
        LIMIT 1;

        -- If a previous record was found within the window
        IF FOUND THEN
            RAISE NOTICE '[UNIFIED_TRIGGER] For NEW hex:%, time:% - Found previous record (time:%, phase:%).', NEW.hex, NEW.time, prev_time, prev_phase;

            -- Detect takeoff: current is 'airborne', previous was 'ground'
            IF NEW.flight_phase = 'airborne' AND prev_phase = 'ground' THEN
                RAISE NOTICE '[UNIFIED_TRIGGER] For NEW hex:%, time:% - TAKEOFF detected. Changing NEW.flight_phase to ''takeoff''.', NEW.hex, NEW.time;
                NEW.flight_phase := 'takeoff'; -- Modify the NEW row being inserted

            -- Detect landing: current is 'ground', previous was 'airborne'
            ELSIF NEW.flight_phase = 'ground' AND prev_phase = 'airborne' THEN
                RAISE NOTICE '[UNIFIED_TRIGGER] For NEW hex:%, time:% - LANDING detected. Updating previous row (time:%) to ''landing''.', NEW.hex, NEW.time, prev_time;
                -- Update the PREVIOUS record to be 'landing'
                UPDATE public.aircraft_pos -- Ensure this is your current table name
                SET flight_phase = 'landing'
                WHERE hex = NEW.hex AND "time" = prev_time; -- Target the specific previous record
            ELSE
                 RAISE NOTICE '[UNIFIED_TRIGGER] For NEW hex:%, time:% - No takeoff/landing condition met with previous phase ''%''.', NEW.hex, NEW.time, prev_phase;
            END IF;
        ELSE
            RAISE NOTICE '[UNIFIED_TRIGGER] For NEW hex:%, time:% - NO previous record found in window.', NEW.hex, NEW.time;
        END IF;
    END IF;

    RETURN NEW; -- Crucial for a BEFORE trigger, return the (potentially modified) row
END;
$$;


--
-- Name: grant_tsdbadmin_to_role(text); Type: FUNCTION; Schema: timescale_functions; Owner: -
--

CREATE FUNCTION timescale_functions.grant_tsdbadmin_to_role(rolename text) RETURNS void
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'pg_catalog', 'pg_temp'
    AS $$
BEGIN
  IF pg_catalog.current_setting('server_version_num')::integer < 160000 THEN
    RAISE EXCEPTION 'this function should be called only on pg version 16 and higher, your version is %s', pg_catalog.version();
  END IF;

  IF pg_catalog.to_regrole(rolename) IS NULL THEN
    RAISE EXCEPTION 'role %s does not exist', rolename;
  END IF;

  IF pg_catalog.to_regrole('tsdbadmin') IS NULL THEN
    RAISE EXCEPTION 'role tsdbadmin does not exist';
  END IF;

  EXECUTE pg_catalog.format('GRANT tsdbadmin TO %I WITH INHERIT TRUE, SET TRUE', rolename);
END;
$$;


--
-- Name: FUNCTION grant_tsdbadmin_to_role(rolename text); Type: COMMENT; Schema: timescale_functions; Owner: -
--

COMMENT ON FUNCTION timescale_functions.grant_tsdbadmin_to_role(rolename text) IS 'This role grants tsdbadmin (database owner) to another role';


--
-- Name: move_createrole(regrole, regrole); Type: FUNCTION; Schema: timescale_functions; Owner: -
--

CREATE FUNCTION timescale_functions.move_createrole(old_role regrole, new_role regrole) RETURNS void
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'pg_catalog', 'pg_temp'
    AS $$
DECLARE
  role_member RECORD;
BEGIN
  -- sanity checks
  IF pg_catalog.current_setting('server_version_num')::integer < 160000 THEN
    RAISE EXCEPTION 'this function should be called only on pg version 16 and higher, your version is %s', pg_catalog.version();
  END IF;

  IF pg_catalog.to_regrole(old_role::text) IS NULL
  THEN
    RAISE EXCEPTION 'old role % does not exist', old_role;
  END IF;
  IF pg_catalog.to_regrole(new_role::text) IS NULL
  THEN
    RAISE EXCEPTION 'new role % does not exist', new_role;
  END IF;

  IF (SELECT rolcreaterole FROM pg_catalog.pg_roles where rolname = old_role::text) IS FALSE
  THEN
    RAISE EXCEPTION 'role % does is not CREATEROLE', old_role;
  END IF;
  IF (SELECT rolcreaterole FROM pg_catalog.pg_roles where rolname = new_role::text) IS TRUE
  THEN
    RAISE EXCEPTION 'role % is already CREATEROLE', new_role;
  END IF;
  -- for each member role of the old role, move the members to the new role
  -- so that new role will be able to administer roles created by the old role
  FOR role_member IN
    SELECT
      roleid::regrole,
      admin_option,
      inherit_option,
      set_option
  FROM
    pg_catalog.pg_auth_members
  WHERE
    member = old_role AND
    roleid::regrole != old_role AND
    roleid::regrole != new_role
  LOOP
      EXECUTE format(
          'GRANT %I TO %I WITH %s, %s, %s',
          role_member.roleid,
          new_role,
          CASE WHEN role_member.admin_option THEN 'ADMIN OPTION' ELSE 'ADMIN FALSE' END,
          CASE WHEN role_member.inherit_option THEN 'INHERIT TRUE' ELSE 'INHERIT FALSE' END,
          CASE WHEN role_member.set_option THEN 'SET TRUE' ELSE 'SET FALSE' END
              );
      EXECUTE format('REVOKE %I FROM %I', role_member.roleid, old_role
              );
    END LOOP;
    -- without revoking new role from the old one, the roles created by the new role
    -- will still contain (indirectly) the old one, and the old one (tsdbadmin) would
    -- be non-grantable to them.
    EXECUTE format('REVOKE %I FROM %I', new_role, old_role);

    EXECUTE format('ALTER ROLE %I CREATEROLE', new_role);
    EXECUTE format('ALTER ROLE %I NOCREATEROLE', old_role);
    -- make sure new role can transfer its CREATEROLE, but old role not.
    EXECUTE format('GRANT EXECUTE ON FUNCTION timescale_functions.move_createrole TO %I', new_role);
    EXECUTE format('REVOKE EXECUTE ON FUNCTION timescale_functions.move_createrole FROM %I', old_role);
    EXECUTE format('REVOKE EXECUTE ON FUNCTION timescale_functions.grant_tsdbadmin_to_role FROM %I', old_role);
    EXECUTE format('GRANT EXECUTE ON FUNCTION timescale_functions.grant_tsdbadmin_to_role TO %I', new_role);
    EXECUTE format('REVOKE EXECUTE ON FUNCTION timescale_functions.revoke_tsdbadmin_from_role FROM %I', old_role);
    EXECUTE format('GRANT EXECUTE ON FUNCTION timescale_functions.revoke_tsdbadmin_from_role TO %I', new_role);
END;
$$;


--
-- Name: FUNCTION move_createrole(old_role regrole, new_role regrole); Type: COMMENT; Schema: timescale_functions; Owner: -
--

COMMENT ON FUNCTION timescale_functions.move_createrole(old_role regrole, new_role regrole) IS 'This function allows moving createrole privileges between existing createrole, and another user';


--
-- Name: revoke_tsdbadmin_from_role(text); Type: FUNCTION; Schema: timescale_functions; Owner: -
--

CREATE FUNCTION timescale_functions.revoke_tsdbadmin_from_role(rolename text) RETURNS void
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'pg_catalog', 'pg_temp'
    AS $$
BEGIN
  IF pg_catalog.current_setting('server_version_num')::integer < 160000 THEN
    RAISE EXCEPTION 'this function should be called only on pg version 16 and higher, your version is %s', pg_catalog.version();
  END IF;

  IF pg_catalog.to_regrole(rolename) IS NULL THEN
    RAISE EXCEPTION 'role %s does not exist', rolename;
  END IF;

  IF pg_catalog.to_regrole('tsdbadmin') IS NULL THEN
    RAISE EXCEPTION 'role tsdbadmin does not exist';
  END IF;

  EXECUTE pg_catalog.format('REVOKE tsdbadmin FROM %I', rolename);
END;
$$;


--
-- Name: FUNCTION revoke_tsdbadmin_from_role(rolename text); Type: COMMENT; Schema: timescale_functions; Owner: -
--

COMMENT ON FUNCTION timescale_functions.revoke_tsdbadmin_from_role(rolename text) IS 'This role revokes tsdbadmin (database owner) to another role';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _compressed_hypertable_4; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._compressed_hypertable_4 (
);


--
-- Name: _compressed_hypertable_6; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._compressed_hypertable_6 (
);


--
-- Name: aircraft_positions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.aircraft_positions (
    "time" timestamp with time zone NOT NULL,
    hex text NOT NULL,
    alt_baro integer,
    alt_geom integer,
    gs real,
    ias real,
    track real,
    baro_rate integer,
    category text,
    squawk text,
    lat double precision,
    lon double precision,
    flight text,
    geom public.geography(Point,4326),
    h3_index public.h3index,
    flight_phase public.flight_phase_enum
);


--
-- Name: _hyper_1_1_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_1_1_chunk (
    CONSTRAINT constraint_1 CHECK ((("time" >= '2025-05-08 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-15 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_positions);


--
-- Name: _hyper_1_2_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_1_2_chunk (
    CONSTRAINT constraint_2 CHECK ((("time" >= '2025-05-15 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-22 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_positions);


--
-- Name: aircraft_pos; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.aircraft_pos (
    "time" timestamp with time zone NOT NULL,
    hex text NOT NULL,
    alt_baro integer,
    alt_geom integer,
    gs smallint,
    ias smallint,
    track smallint,
    baro_rate smallint,
    squawk smallint,
    flight text,
    h3_index public.h3index,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index GENERATED ALWAYS AS (
CASE
    WHEN (h3_index IS NOT NULL) THEN public.h3_cell_to_parent(h3_index, 4)
    ELSE NULL::public.h3index
END) STORED
);


--
-- Name: _hyper_2_192_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_192_chunk (
    CONSTRAINT constraint_139 CHECK ((("time" >= '2025-05-29 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-30 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_193_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_193_chunk (
    CONSTRAINT constraint_140 CHECK ((("time" >= '2025-05-30 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-31 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_195_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_195_chunk (
    CONSTRAINT constraint_141 CHECK ((("time" >= '2025-05-31 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-06-01 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_197_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_197_chunk (
    CONSTRAINT constraint_142 CHECK ((("time" >= '2025-06-01 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-06-02 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_199_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_199_chunk (
    CONSTRAINT constraint_143 CHECK ((("time" >= '2025-06-02 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-06-03 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_201_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_201_chunk (
    CONSTRAINT constraint_144 CHECK ((("time" >= '2025-06-03 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-06-04 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_203_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_203_chunk (
    CONSTRAINT constraint_145 CHECK ((("time" >= '2025-06-04 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-06-05 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_205_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_205_chunk (
    CONSTRAINT constraint_146 CHECK ((("time" >= '2025-06-05 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-06-06 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_207_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_207_chunk (
    CONSTRAINT constraint_147 CHECK ((("time" >= '2025-06-06 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-06-07 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_209_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_209_chunk (
    CONSTRAINT constraint_148 CHECK ((("time" >= '2025-06-07 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-06-08 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_23_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_23_chunk (
    CONSTRAINT constraint_10 CHECK ((("time" >= '2025-05-20 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-21 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_24_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_24_chunk (
    CONSTRAINT constraint_11 CHECK ((("time" >= '2025-05-21 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-22 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_3_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_3_chunk (
    CONSTRAINT constraint_3 CHECK ((("time" >= '2025-05-13 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-14 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_42_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_42_chunk (
    CONSTRAINT constraint_12 CHECK ((("time" >= '2025-05-22 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-23 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_4_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_4_chunk (
    CONSTRAINT constraint_4 CHECK ((("time" >= '2025-05-14 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-15 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_54_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_54_chunk (
    CONSTRAINT constraint_13 CHECK ((("time" >= '2025-05-23 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-24 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_5_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_5_chunk (
    CONSTRAINT constraint_5 CHECK ((("time" >= '2025-05-15 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-16 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_60_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_60_chunk (
    CONSTRAINT constraint_14 CHECK ((("time" >= '2025-05-24 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-25 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_62_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_62_chunk (
    CONSTRAINT constraint_15 CHECK ((("time" >= '2025-05-25 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-26 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_64_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_64_chunk (
    CONSTRAINT constraint_16 CHECK ((("time" >= '2025-05-26 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-27 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_66_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_66_chunk (
    CONSTRAINT constraint_17 CHECK ((("time" >= '2025-05-27 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-28 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_69_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_69_chunk (
    CONSTRAINT constraint_18 CHECK ((("time" >= '2025-05-28 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-29 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_6_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_6_chunk (
    CONSTRAINT constraint_6 CHECK ((("time" >= '2025-05-16 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-17 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_7_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_7_chunk (
    CONSTRAINT constraint_7 CHECK ((("time" >= '2025-05-17 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-18 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_8_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_8_chunk (
    CONSTRAINT constraint_8 CHECK ((("time" >= '2025-05-18 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-19 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: _hyper_2_9_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal._hyper_2_9_chunk (
    CONSTRAINT constraint_9 CHECK ((("time" >= '2025-05-19 00:00:00+00'::timestamp with time zone) AND ("time" < '2025-05-20 00:00:00+00'::timestamp with time zone)))
)
INHERITS (public.aircraft_pos);


--
-- Name: compress_hyper_4_16_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_4_16_chunk (
    _ts_meta_count integer,
    hex text,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    category _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    lat _timescaledb_internal.compressed_data,
    lon _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    geom _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data,
    _ts_meta_v2_min_flight_phase public.flight_phase_enum,
    _ts_meta_v2_max_flight_phase public.flight_phase_enum,
    flight_phase _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN category SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN category SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN squawk SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN lat SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN lon SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN geom SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN _ts_meta_v2_min_flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN _ts_meta_v2_max_flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN flight_phase SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_4_16_chunk ALTER COLUMN flight_phase SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_194_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_194_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_194_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_196_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_196_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_196_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_198_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_198_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_198_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_200_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_200_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_200_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_202_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_202_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_202_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_204_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_204_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_204_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_206_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_206_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_206_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_208_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_208_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_208_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_210_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_210_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_210_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_33_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_33_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_33_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_34_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_34_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_34_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_35_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_35_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_35_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_36_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_36_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_36_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_37_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_37_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_37_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_38_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_38_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_38_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_39_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_39_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_39_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_41_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_41_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_41_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_53_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_53_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_53_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_59_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_59_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_59_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_63_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_63_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_63_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_65_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_65_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_65_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_68_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_68_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_68_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_70_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_70_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_70_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: compress_hyper_6_71_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: -
--

CREATE TABLE _timescaledb_internal.compress_hyper_6_71_chunk (
    _ts_meta_count integer,
    hex text,
    flight_phase public.flight_phase_enum,
    h3_res4 public.h3index,
    _ts_meta_min_1 timestamp with time zone,
    _ts_meta_max_1 timestamp with time zone,
    "time" _timescaledb_internal.compressed_data,
    alt_baro _timescaledb_internal.compressed_data,
    alt_geom _timescaledb_internal.compressed_data,
    gs _timescaledb_internal.compressed_data,
    ias _timescaledb_internal.compressed_data,
    track _timescaledb_internal.compressed_data,
    baro_rate _timescaledb_internal.compressed_data,
    squawk _timescaledb_internal.compressed_data,
    flight _timescaledb_internal.compressed_data,
    h3_index _timescaledb_internal.compressed_data
)
WITH (toast_tuple_target='128');
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN _ts_meta_count SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN hex SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN flight_phase SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN h3_res4 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN _ts_meta_min_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN _ts_meta_max_1 SET STATISTICS 1000;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN "time" SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN alt_baro SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN alt_geom SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN gs SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN ias SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN track SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN baro_rate SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN squawk SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN flight SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN flight SET STORAGE EXTENDED;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN h3_index SET STATISTICS 0;
ALTER TABLE ONLY _timescaledb_internal.compress_hyper_6_71_chunk ALTER COLUMN h3_index SET STORAGE EXTENDED;


--
-- Name: aircraft; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.aircraft (
    hex text NOT NULL,
    category public.aircraft_category_enum,
    registration text
);


--
-- Name: airports; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.airports (
    icao character varying(4) NOT NULL,
    iata character varying(3),
    name character varying(255),
    location public.geography(Point,4326),
    elevation_ft integer,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: runways; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.runways (
    airport_icao character varying(4) NOT NULL,
    name character varying(10) NOT NULL,
    length_ft integer,
    width_ft integer,
    surface public.runway_surface_enum NOT NULL,
    centerline public.geography(LineString,4326),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


--
-- Name: runways_old; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.runways_old (
    id integer NOT NULL,
    airport_icao character varying(4),
    name character varying(10) NOT NULL,
    length_ft integer NOT NULL,
    width_ft integer NOT NULL,
    surface character varying(50) NOT NULL,
    centerline public.geography(LineString,4326),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: runways_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.runways_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: runways_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.runways_id_seq OWNED BY public.runways_old.id;


--
-- Name: runways_old id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.runways_old ALTER COLUMN id SET DEFAULT nextval('public.runways_id_seq'::regclass);


--
-- Name: _hyper_2_192_chunk 192_141_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_192_chunk
    ADD CONSTRAINT "192_141_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_193_chunk 193_142_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_193_chunk
    ADD CONSTRAINT "193_142_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_195_chunk 195_143_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_195_chunk
    ADD CONSTRAINT "195_143_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_197_chunk 197_144_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_197_chunk
    ADD CONSTRAINT "197_144_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_199_chunk 199_145_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_199_chunk
    ADD CONSTRAINT "199_145_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_1_1_chunk 1_3_unique_hex_time; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_1_1_chunk
    ADD CONSTRAINT "1_3_unique_hex_time" UNIQUE (hex, "time");


--
-- Name: _hyper_2_201_chunk 201_146_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_201_chunk
    ADD CONSTRAINT "201_146_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_203_chunk 203_147_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_203_chunk
    ADD CONSTRAINT "203_147_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_205_chunk 205_148_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_205_chunk
    ADD CONSTRAINT "205_148_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_207_chunk 207_149_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_207_chunk
    ADD CONSTRAINT "207_149_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_209_chunk 209_150_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_209_chunk
    ADD CONSTRAINT "209_150_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_23_chunk 23_12_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_23_chunk
    ADD CONSTRAINT "23_12_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_24_chunk 24_13_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_24_chunk
    ADD CONSTRAINT "24_13_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_1_2_chunk 2_4_unique_hex_time; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_1_2_chunk
    ADD CONSTRAINT "2_4_unique_hex_time" UNIQUE (hex, "time");


--
-- Name: _hyper_2_3_chunk 3_5_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_3_chunk
    ADD CONSTRAINT "3_5_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_42_chunk 42_14_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_42_chunk
    ADD CONSTRAINT "42_14_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_4_chunk 4_6_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_4_chunk
    ADD CONSTRAINT "4_6_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_54_chunk 54_15_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_54_chunk
    ADD CONSTRAINT "54_15_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_5_chunk 5_7_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_5_chunk
    ADD CONSTRAINT "5_7_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_60_chunk 60_16_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_60_chunk
    ADD CONSTRAINT "60_16_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_62_chunk 62_17_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_62_chunk
    ADD CONSTRAINT "62_17_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_64_chunk 64_18_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_64_chunk
    ADD CONSTRAINT "64_18_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_66_chunk 66_19_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_66_chunk
    ADD CONSTRAINT "66_19_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_69_chunk 69_20_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_69_chunk
    ADD CONSTRAINT "69_20_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_6_chunk 6_8_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_6_chunk
    ADD CONSTRAINT "6_8_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_7_chunk 7_9_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_7_chunk
    ADD CONSTRAINT "7_9_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_8_chunk 8_10_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_8_chunk
    ADD CONSTRAINT "8_10_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: _hyper_2_9_chunk 9_11_unique_hex_time_pos2; Type: CONSTRAINT; Schema: _timescaledb_internal; Owner: -
--

ALTER TABLE ONLY _timescaledb_internal._hyper_2_9_chunk
    ADD CONSTRAINT "9_11_unique_hex_time_pos2" UNIQUE (hex, "time");


--
-- Name: aircraft aircraft_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.aircraft
    ADD CONSTRAINT aircraft_pkey PRIMARY KEY (hex);


--
-- Name: aircraft aircraft_registration_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.aircraft
    ADD CONSTRAINT aircraft_registration_key UNIQUE (registration);


--
-- Name: airports airports_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.airports
    ADD CONSTRAINT airports_pkey PRIMARY KEY (icao);


--
-- Name: runways_old runways_airport_icao_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.runways_old
    ADD CONSTRAINT runways_airport_icao_name_key UNIQUE (airport_icao, name);


--
-- Name: runways runways_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.runways
    ADD CONSTRAINT runways_pkey PRIMARY KEY (airport_icao, name);


--
-- Name: aircraft_positions unique_hex_time; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.aircraft_positions
    ADD CONSTRAINT unique_hex_time UNIQUE (hex, "time");


--
-- Name: aircraft_pos unique_hex_time_pos2; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.aircraft_pos
    ADD CONSTRAINT unique_hex_time_pos2 UNIQUE (hex, "time");


--
-- Name: _hyper_1_1_chunk_aircraft_positions_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_1_1_chunk_aircraft_positions_time_idx ON _timescaledb_internal._hyper_1_1_chunk USING btree ("time" DESC);


--
-- Name: _hyper_1_1_chunk_idx_aircraft_positions_flight_phase_time; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_1_1_chunk_idx_aircraft_positions_flight_phase_time ON _timescaledb_internal._hyper_1_1_chunk USING btree (flight_phase, "time" DESC);


--
-- Name: _hyper_1_1_chunk_idx_aircraft_positions_h3_parent_time; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_1_1_chunk_idx_aircraft_positions_h3_parent_time ON _timescaledb_internal._hyper_1_1_chunk USING btree (public.h3_cell_to_parent(h3_index, 6), "time" DESC);


--
-- Name: _hyper_1_1_chunk_idx_aircraft_positions_hex_time; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_1_1_chunk_idx_aircraft_positions_hex_time ON _timescaledb_internal._hyper_1_1_chunk USING btree (hex, "time");


--
-- Name: _hyper_1_1_chunk_idx_ap_h3parent_hex_time; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_1_1_chunk_idx_ap_h3parent_hex_time ON _timescaledb_internal._hyper_1_1_chunk USING btree (public.h3_cell_to_parent(h3_index, 6), hex, "time");


--
-- Name: _hyper_1_2_chunk_aircraft_positions_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_1_2_chunk_aircraft_positions_time_idx ON _timescaledb_internal._hyper_1_2_chunk USING btree ("time" DESC);


--
-- Name: _hyper_1_2_chunk_idx_aircraft_positions_flight_phase_time; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_1_2_chunk_idx_aircraft_positions_flight_phase_time ON _timescaledb_internal._hyper_1_2_chunk USING btree (flight_phase, "time" DESC);


--
-- Name: _hyper_1_2_chunk_idx_aircraft_positions_h3_parent_time; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_1_2_chunk_idx_aircraft_positions_h3_parent_time ON _timescaledb_internal._hyper_1_2_chunk USING btree (public.h3_cell_to_parent(h3_index, 6), "time" DESC);


--
-- Name: _hyper_1_2_chunk_idx_aircraft_positions_hex_time; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_1_2_chunk_idx_aircraft_positions_hex_time ON _timescaledb_internal._hyper_1_2_chunk USING btree (hex, "time");


--
-- Name: _hyper_1_2_chunk_idx_ap_h3parent_hex_time; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_1_2_chunk_idx_ap_h3parent_hex_time ON _timescaledb_internal._hyper_1_2_chunk USING btree (public.h3_cell_to_parent(h3_index, 6), hex, "time");


--
-- Name: _hyper_2_192_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_192_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_192_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_192_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_192_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_192_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_192_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_192_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_192_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_193_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_193_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_193_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_193_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_193_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_193_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_193_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_193_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_193_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_195_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_195_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_195_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_195_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_195_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_195_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_195_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_195_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_195_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_197_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_197_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_197_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_197_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_197_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_197_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_197_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_197_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_197_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_199_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_199_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_199_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_199_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_199_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_199_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_199_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_199_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_199_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_201_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_201_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_201_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_201_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_201_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_201_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_201_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_201_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_201_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_203_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_203_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_203_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_203_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_203_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_203_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_203_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_203_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_203_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_205_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_205_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_205_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_205_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_205_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_205_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_205_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_205_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_205_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_207_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_207_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_207_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_207_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_207_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_207_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_207_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_207_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_207_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_209_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_209_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_209_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_209_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_209_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_209_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_209_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_209_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_209_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_23_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_23_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_23_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_23_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_23_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_23_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_23_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_23_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_23_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_24_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_24_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_24_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_24_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_24_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_24_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_24_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_24_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_24_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_3_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_3_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_3_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_3_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_3_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_3_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_3_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_3_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_3_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_42_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_42_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_42_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_42_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_42_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_42_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_42_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_42_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_42_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_4_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_4_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_4_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_4_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_4_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_4_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_4_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_4_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_4_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_54_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_54_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_54_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_54_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_54_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_54_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_54_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_54_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_54_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_5_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_5_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_5_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_5_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_5_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_5_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_5_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_5_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_5_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_60_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_60_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_60_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_60_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_60_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_60_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_60_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_60_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_60_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_62_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_62_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_62_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_62_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_62_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_62_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_62_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_62_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_62_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_64_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_64_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_64_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_64_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_64_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_64_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_64_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_64_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_64_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_66_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_66_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_66_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_66_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_66_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_66_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_66_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_66_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_66_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_69_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_69_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_69_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_69_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_69_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_69_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_69_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_69_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_69_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_6_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_6_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_6_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_6_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_6_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_6_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_6_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_6_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_6_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_7_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_7_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_7_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_7_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_7_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_7_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_7_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_7_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_7_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_8_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_8_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_8_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_8_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_8_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_8_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_8_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_8_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_8_chunk USING btree ("time" DESC);


--
-- Name: _hyper_2_9_chunk_aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_9_chunk_aircraft_pos2_flight_phase_idx ON _timescaledb_internal._hyper_2_9_chunk USING btree (flight_phase);


--
-- Name: _hyper_2_9_chunk_aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_9_chunk_aircraft_pos2_h3_res4_idx ON _timescaledb_internal._hyper_2_9_chunk USING btree (h3_res4);


--
-- Name: _hyper_2_9_chunk_aircraft_pos2_time_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX _hyper_2_9_chunk_aircraft_pos2_time_idx ON _timescaledb_internal._hyper_2_9_chunk USING btree ("time" DESC);


--
-- Name: compress_hyper_4_16_chunk_hex__ts_meta_min_1__ts_meta_max_1_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_4_16_chunk_hex__ts_meta_min_1__ts_meta_max_1_idx ON _timescaledb_internal.compress_hyper_4_16_chunk USING btree (hex, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_194_chunk_hex_flight_phase_h3_res4__ts_met_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_194_chunk_hex_flight_phase_h3_res4__ts_met_idx ON _timescaledb_internal.compress_hyper_6_194_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_196_chunk_hex_flight_phase_h3_res4__ts_met_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_196_chunk_hex_flight_phase_h3_res4__ts_met_idx ON _timescaledb_internal.compress_hyper_6_196_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_198_chunk_hex_flight_phase_h3_res4__ts_met_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_198_chunk_hex_flight_phase_h3_res4__ts_met_idx ON _timescaledb_internal.compress_hyper_6_198_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_200_chunk_hex_flight_phase_h3_res4__ts_met_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_200_chunk_hex_flight_phase_h3_res4__ts_met_idx ON _timescaledb_internal.compress_hyper_6_200_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_202_chunk_hex_flight_phase_h3_res4__ts_met_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_202_chunk_hex_flight_phase_h3_res4__ts_met_idx ON _timescaledb_internal.compress_hyper_6_202_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_204_chunk_hex_flight_phase_h3_res4__ts_met_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_204_chunk_hex_flight_phase_h3_res4__ts_met_idx ON _timescaledb_internal.compress_hyper_6_204_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_206_chunk_hex_flight_phase_h3_res4__ts_met_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_206_chunk_hex_flight_phase_h3_res4__ts_met_idx ON _timescaledb_internal.compress_hyper_6_206_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_208_chunk_hex_flight_phase_h3_res4__ts_met_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_208_chunk_hex_flight_phase_h3_res4__ts_met_idx ON _timescaledb_internal.compress_hyper_6_208_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_210_chunk_hex_flight_phase_h3_res4__ts_met_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_210_chunk_hex_flight_phase_h3_res4__ts_met_idx ON _timescaledb_internal.compress_hyper_6_210_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_33_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_33_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_33_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_34_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_34_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_34_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_35_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_35_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_35_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_36_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_36_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_36_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_37_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_37_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_37_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_38_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_38_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_38_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_39_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_39_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_39_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_41_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_41_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_41_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_53_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_53_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_53_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_59_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_59_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_59_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_63_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_63_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_63_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_65_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_65_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_65_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_68_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_68_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_68_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_70_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_70_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_70_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: compress_hyper_6_71_chunk_hex_flight_phase_h3_res4__ts_meta_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: -
--

CREATE INDEX compress_hyper_6_71_chunk_hex_flight_phase_h3_res4__ts_meta_idx ON _timescaledb_internal.compress_hyper_6_71_chunk USING btree (hex, flight_phase, h3_res4, _ts_meta_min_1 DESC, _ts_meta_max_1 DESC);


--
-- Name: aircraft_pos2_flight_phase_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX aircraft_pos2_flight_phase_idx ON public.aircraft_pos USING btree (flight_phase);


--
-- Name: aircraft_pos2_h3_res4_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX aircraft_pos2_h3_res4_idx ON public.aircraft_pos USING btree (h3_res4);


--
-- Name: aircraft_pos2_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX aircraft_pos2_time_idx ON public.aircraft_pos USING btree ("time" DESC);


--
-- Name: aircraft_positions_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX aircraft_positions_time_idx ON public.aircraft_positions USING btree ("time" DESC);


--
-- Name: idx_aircraft_positions_flight_phase_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_aircraft_positions_flight_phase_time ON public.aircraft_positions USING btree (flight_phase, "time" DESC);


--
-- Name: idx_aircraft_positions_h3_parent_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_aircraft_positions_h3_parent_time ON public.aircraft_positions USING btree (public.h3_cell_to_parent(h3_index, 6), "time" DESC);


--
-- Name: idx_aircraft_positions_hex_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_aircraft_positions_hex_time ON public.aircraft_positions USING btree (hex, "time");


--
-- Name: idx_ap_h3parent_hex_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_ap_h3parent_hex_time ON public.aircraft_positions USING btree (public.h3_cell_to_parent(h3_index, 6), hex, "time");


--
-- Name: _hyper_1_1_chunk detect_takeoff_landing_trigger; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER detect_takeoff_landing_trigger BEFORE INSERT ON _timescaledb_internal._hyper_1_1_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing();


--
-- Name: _hyper_1_2_chunk detect_takeoff_landing_trigger; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER detect_takeoff_landing_trigger BEFORE INSERT ON _timescaledb_internal._hyper_1_2_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing();


--
-- Name: _hyper_2_192_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_192_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_193_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_193_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_195_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_195_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_197_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_197_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_199_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_199_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_201_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_201_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_203_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_203_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_205_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_205_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_207_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_207_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_209_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_209_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_23_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_23_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_24_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_24_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_3_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_3_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_42_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_42_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_4_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_4_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_54_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_54_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_5_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_5_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_60_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_60_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_62_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_62_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_64_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_64_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_66_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_66_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_69_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_69_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_6_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_6_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_7_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_7_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_8_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_8_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_9_chunk trg_derive_track_data; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON _timescaledb_internal._hyper_2_9_chunk FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: _hyper_2_192_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_192_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_193_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_193_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_195_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_195_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_197_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_197_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_199_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_199_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_201_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_201_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_203_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_203_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_205_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_205_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_207_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_207_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_209_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_209_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_23_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_23_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_24_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_24_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_3_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_3_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_42_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_42_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_4_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_4_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_54_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_54_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_5_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_5_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_60_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_60_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_62_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_62_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_64_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_64_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_66_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_66_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_69_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_69_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_6_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_6_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_7_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_7_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_8_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_8_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _hyper_2_9_chunk trg_detect_flight_phase_unified; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON _timescaledb_internal._hyper_2_9_chunk FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: _compressed_hypertable_4 ts_insert_blocker; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER ts_insert_blocker BEFORE INSERT ON _timescaledb_internal._compressed_hypertable_4 FOR EACH ROW EXECUTE FUNCTION _timescaledb_functions.insert_blocker();


--
-- Name: _compressed_hypertable_6 ts_insert_blocker; Type: TRIGGER; Schema: _timescaledb_internal; Owner: -
--

CREATE TRIGGER ts_insert_blocker BEFORE INSERT ON _timescaledb_internal._compressed_hypertable_6 FOR EACH ROW EXECUTE FUNCTION _timescaledb_functions.insert_blocker();


--
-- Name: aircraft_positions detect_takeoff_landing_trigger; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER detect_takeoff_landing_trigger BEFORE INSERT ON public.aircraft_positions FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing();


--
-- Name: aircraft_pos trg_derive_track_data; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trg_derive_track_data BEFORE INSERT ON public.aircraft_pos FOR EACH ROW EXECUTE FUNCTION public.derive_track_data();


--
-- Name: aircraft_pos trg_detect_flight_phase_unified; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trg_detect_flight_phase_unified BEFORE INSERT ON public.aircraft_pos FOR EACH ROW EXECUTE FUNCTION public.detect_takeoff_landing_unified();


--
-- Name: aircraft_pos ts_insert_blocker; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER ts_insert_blocker BEFORE INSERT ON public.aircraft_pos FOR EACH ROW EXECUTE FUNCTION _timescaledb_functions.insert_blocker();


--
-- Name: aircraft_positions ts_insert_blocker; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER ts_insert_blocker BEFORE INSERT ON public.aircraft_positions FOR EACH ROW EXECUTE FUNCTION _timescaledb_functions.insert_blocker();


--
-- Name: runways_old runways_airport_icao_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.runways_old
    ADD CONSTRAINT runways_airport_icao_fkey FOREIGN KEY (airport_icao) REFERENCES public.airports(icao);


--
-- Name: runways runways_airport_icao_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.runways
    ADD CONSTRAINT runways_airport_icao_fkey FOREIGN KEY (airport_icao) REFERENCES public.airports(icao) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

