# Aircraft Position API Query Documentation

## Database Schema

### `aircraft_pos` Table Structure
```sql
CREATE TABLE public.aircraft_pos (
    "time" timestamptz NOT NULL,
    hex text NOT NULL,
    alt_baro int4 NULL,
    alt_geom int4 NULL,
    gs int2 NULL,
    ias int2 NULL,
    track int2 NULL,
    baro_rate int2 NULL,
    squawk int2 NULL,
    flight text NULL,
    h3_index public.h3index NULL,
    flight_phase public."flight_phase_enum" NULL,
    h3_res4 public.h3index GENERATED ALWAYS AS (
        CASE
            WHEN h3_index IS NOT NULL THEN h3_cell_to_parent(h3_index, 4)
            ELSE NULL::h3index
        END) STORED NULL,
    CONSTRAINT unique_hex_time_pos2 UNIQUE (hex, "time")
);
```

### Key Indexes
- `aircraft_pos2_flight_phase_idx` - BTREE on `flight_phase`
- `aircraft_pos2_h3_res4_idx` - BTREE on `h3_res4` 
- `aircraft_pos2_time_idx` - BTREE on `time` (DESC)

## Critical Query Performance Lessons

### 1. Filter Order Optimization
**CRITICAL**: Always filter by `h3_res4` first, then other conditions. This leverages the indexed resolution 4 H3 cells for maximum efficiency.

```sql
-- ✅ CORRECT - Use h3_res4 index first
WHERE ap.h3_res4 = target_h3_res4
  AND ap.time >= start_time
  AND ap.time <= end_time
  AND ap.flight_phase = 'airborne'
  
-- ❌ WRONG - Scanning by flight_phase first processes millions of rows
WHERE ap.flight_phase = 'airborne'
  AND ap.h3_res4 = target_h3_res4
```

### 2. H3 Area Search Strategy
For geographic area searches:
1. **Calculate resolution 4 parent** of search area H3 cells
2. **Filter by h3_res4** to get broad geographic area (uses index)
3. **Apply granular H3 filtering** on the reduced dataset

### 3. Avoid Query Planner Confusion
- Use simple comma joins instead of CROSS JOINs when possible
- Pre-calculate H3 parameters in CTEs to avoid repeated calculations
- Structure queries to make index usage obvious to the planner

## Optimized Query Template

```sql
WITH search_area_h3 AS (
    -- Array of H3 indexes defining search area
    SELECT UNNEST(ARRAY[...]) AS h3_index
),
search_params AS (
    SELECT 
        h3_cell_to_parent((SELECT h3_index FROM search_area_h3 LIMIT 1), 4) AS target_h3_res4,
        h3_get_resolution((SELECT h3_index FROM search_area_h3 LIMIT 1)) AS search_resolution
),
filtered_positions AS (
    SELECT ap.*
    FROM aircraft_pos ap, search_params sp
    WHERE ap.h3_res4 = sp.target_h3_res4  -- ← CRITICAL: This filter first
      AND ap.time >= start_time
      AND ap.time <= end_time
      AND ap.flight_phase = desired_phase
      AND ap.h3_index IS NOT NULL
      AND h3_cell_to_parent(ap.h3_index, sp.search_resolution) IN (
        SELECT h3_index FROM search_area_h3
      )
)
-- Additional processing...
```

## Flight Segment Grouping Logic

### Continuous Flight Detection
Group positions by aircraft (`hex`) where time gaps ≤ 11 seconds represent continuous flight segments:

```sql
WITH with_time_gaps AS (
    SELECT *,
           EXTRACT(EPOCH FROM (time - LAG(time) OVER (PARTITION BY hex ORDER BY time))) AS time_gap
    FROM filtered_positions
),
with_group_boundaries AS (
    SELECT *,
           CASE WHEN time_gap > 11 OR time_gap IS NULL THEN 1 ELSE 0 END AS is_new_group
    FROM with_time_gaps
),
position_groups AS (
    SELECT *,
           SUM(is_new_group) OVER (PARTITION BY hex ORDER BY time ROWS UNBOUNDED PRECEDING) AS group_id
    FROM with_group_boundaries
)
SELECT 
    hex,
    MIN(time) AS firstseen,
    MAX(time) AS lastseen,
    (ARRAY_AGG(flight ORDER BY time))[1] AS flight,
    (ARRAY_AGG(flight_phase ORDER BY time))[1] AS category
FROM position_groups
GROUP BY hex, group_id
ORDER BY hex, firstseen;
```

## API Parameter Mapping

### Core Parameters
- `startTime`/`endTime` → `time` column range query
- `area` → H3 cell array for geographic filtering
- `flightPhase` → `flight_phase` enum column

### Aircraft Filters
- `altMin`/`altMax` → `alt_baro` or `alt_geom` range query
- `heading` → `track` column matching
- `squawk` → `squawk` column matching

### Aircraft Metadata (Requires Joins)
- `type` → Aircraft type lookup table
- `class` → Aircraft class lookup table  
- `registration` → Registration lookup table

## H3 Integration Best Practices

### Resolution Strategy
- **Input**: H3 cells at various resolutions (4-11)
- **Processing**: Calculate resolution 4 parents for efficient indexing
- **Filtering**: Use `h3_res4` for broad area, original cells for precision

### Geographic Coverage
```sql
-- Calculate unique resolution 4 cells from search area
WITH search_area_res4 AS (
    SELECT DISTINCT h3_cell_to_parent(h3_index, 4) AS h3_res4
    FROM search_area_h3
    WHERE h3_index IS NOT NULL
)
```

## Performance Benchmarks

### Query Execution Improvements
- **Before optimization**: 6.3 seconds (3.2M rows scanned)
- **After h3_res4 optimization**: 2.7 seconds (332K rows scanned)
- **Key improvement**: 57% faster execution, 90% fewer rows processed

### Optimal Filter Chain
1. `h3_res4` index scan (geography)
2. `time` index filter (temporal)
3. `flight_phase` index filter (operational state)
4. Granular H3 cell matching (precision)

## API Response Structure

### Individual Positions
```json
{
  "hex": "aircraft_identifier",
  "time": "2025-06-08T08:34:42Z",
  "alt_baro": 3500,
  "gs": 120,
  "track": 270,
  "flight": "UAL123",
  "flight_phase": "airborne"
}
```

### Flight Segments (Grouped)
```json
{
  "hex": "aircraft_identifier", 
  "firstseen": "2025-06-08T08:34:42Z",
  "lastseen": "2025-06-08T08:45:23Z",
  "flight": "UAL123",
  "category": "airborne"
}
```

## Implementation Notes

### TimescaleDB Considerations
- Table uses TimescaleDB hypertable partitioning
- Chunk-based scanning visible in execution plans
- Time-based partitioning optimizes temporal queries

### Index Usage Verification
Always verify index usage with `EXPLAIN ANALYZE`:
- Look for "Bitmap Index Scan on aircraft_pos2_h3_res4_idx"
- Avoid "Seq Scan" on large partitions
- Monitor "Rows Removed by Filter" to minimize waste

### Error Handling
- Validate H3 index format before querying
- Handle empty result sets gracefully
- Consider timeout limits for large area queries
- Validate time range parameters (start < end) 