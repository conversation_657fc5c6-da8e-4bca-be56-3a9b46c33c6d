version: '3.8'

services:
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    # The 'ports' section has been completely removed. This is correct!
    environment:
      - DATABASE_URL=postgresql+asyncpg://tsdbadmin:<EMAIL>:34087/tsdb?ssl=require
    restart: unless-stopped
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3
    #   start_period: 40s