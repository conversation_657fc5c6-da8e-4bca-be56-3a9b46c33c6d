-- =============================================================================
-- Setup script for new TimescaleDB instance with h3_postgis
-- =============================================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS h3;
CREATE EXTENSION IF NOT EXISTS h3_postgis;
CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;

-- Verify extensions are installed
\dx

-- Show extension versions
SELECT 
    name,
    default_version,
    installed_version
FROM pg_available_extensions 
WHERE name IN ('postgis', 'h3', 'h3_postgis', 'timescaledb')
ORDER BY name; 