# TarmacTrack Environment Variables for Coolify Deployment Test

# TimescaleDB Cloud Configuration
# Get this from your TimescaleDB Cloud dashboard
DATABASE_URL=postgresql+asyncpg://tsdbadmin:your_password@your_host.tsdb.cloud.timescale.com:35750/tsdb?ssl=require

# API Configuration  
SECRET_KEY=your-super-secret-key-change-this-in-production

# Note: Coolify will generate free domains for you automatically
# No need to configure custom domains for testing 