# TarmacTrack API

A streamlined FastAPI application for retrieving airport information and aircraft positions from a TimescaleDB database with PostGIS and H3 extensions.

## Overview

This API provides essential endpoints for aviation data:

1. **Get Airport by ICAO** - Retrieve detailed airport information including runways
2. **Get Airport Events** - Retrieve takeoff/landing events with intelligent runway detection
3. **Get Aircraft Positions at Time** - Retrieve aircraft positions with optional trace data and spatial filtering
4. **Get H3 Cells** - Utility endpoint for H3 hexagonal cell generation

## Key Features

- **Native H3 Support** - Uses PostgreSQL's native `h3index` type with proper Python H3 library integration
- **Trace Data** - Historical position trails using H3 cells for efficient geospatial tracking
- **Spatial Filtering** - Bounding box filtering using H3 spatial indexing
- **Runway Detection** - Intelligent runway assignment for takeoff/landing events using spatial scoring
- **Optimized Queries** - Single-query approach for aircraft positions with trace data
- **Database Compliance** - Models exactly match the TimescaleDB schema

## API Structure

### Core Components

```
api/
├── app/
│   ├── main.py              # FastAPI application entry point
│   ├── core/
│   │   └── config.py        # Application configuration
│   ├── models/              # SQLAlchemy ORM models
│   │   ├── airport.py       # Airport and runway models
│   │   └── aircraft.py      # Aircraft and position models (with H3Index type)
│   ├── schemas/             # Pydantic response models
│   │   ├── airport.py       # Airport API responses (with events)
│   │   └── aircraft.py      # Aircraft API responses (with trace support)
│   ├── services/            # Business logic layer
│   │   ├── airport_service.py
│   │   └── aircraft_service.py
│   ├── routers/             # API endpoints
│   │   ├── airports.py      # Airport and event endpoints
│   │   └── aircraft.py      # Aircraft endpoints (with spatial filtering)
│   └── db/                  # Database configuration
├── requirements.txt         # Python dependencies
├── Dockerfile              # Container configuration
└── README.md               # This file
```

## Database Schema Compliance

The API models exactly match the database schema defined in `DATABASE_SETUP.md`:

### Tables
- `airports` - Airport information with PostGIS location data
- `runways` - Runway information linked to airports
- `aircraft` - Aircraft registry information  
- `aircraft_pos` - Aircraft position data (TimescaleDB partitioned table)

### Enums
- `aircraft_category_enum` - A0-A7, B0-B7, C0-C7, D0-D7, UNKNOWN
- `runway_surface_enum` - ASPHALT, CONCRETE, GRASS, GRAVEL, DIRT, WATER, UNKNOWN, OTHER
- `flight_phase_enum` - ground, takeoff, landing, airborne

### H3 Integration
- Native PostgreSQL `h3index` type support
- Proper Python h3 library integration
- H3 spatial indexing for efficient geospatial queries

## API Endpoints

### 1. Get Airport by ICAO Code

```
GET /api/v1/airports/{icao}
```

Returns detailed airport information including all associated runways.

**Parameters:**
- `icao` (path) - 4-character ICAO airport code (e.g., "KJFK")

**Response:**
```json
{
  "icao": "KJFK",
  "iata": "JFK", 
  "name": "John F Kennedy International Airport",
  "elevation_ft": 13,
  "runways": [
    {
      "name": "04L/22R",
      "length_ft": 14572,
      "width_ft": 150,
      "surface": "ASPHALT"
    }
  ]
}
```

### 2. Get Airport Events

```
GET /api/v1/airports/{icao}/events
```

Returns takeoff and landing events at an airport with intelligent runway detection.

**Parameters:**
- `icao` (path) - 4-character ICAO airport code (e.g., "KJFK")
- `start_time` (query, optional) - Start time in ISO 8601 format (defaults to 1 hour ago)
- `end_time` (query, optional) - End time in ISO 8601 format (defaults to current time)

**Intelligent Runway Detection:**
- Uses H3 spatial filtering around runway endpoints for efficient event detection
- Calculates runway scores based on perpendicular distance and heading difference
- Score = Distance (meters) + Heading Difference (degrees)
- Events with scores > 30 are filtered out as poor matches
- Only the best matching runway per event is returned

**Response:**
```json
{
  "icao": "KJFK",
  "start_time": "2024-01-01T11:00:00Z",
  "end_time": "2024-01-01T12:00:00Z",
  "count": 15,
  "events": [
    {
      "time": "2024-01-01T11:45:23Z",
      "event_type": "takeoff",
      "registration": "N12345",
      "category": "A3",
      "hex": "A12345",
      "runway": "04L",
      "flight": "UAL123",
      "alt_baro": 145,
      "track": 042
    },
    {
      "time": "2024-01-01T11:42:15Z",
      "event_type": "landing",
      "registration": "N67890",
      "category": "A2",
      "hex": "B67890",
      "runway": "22R",
      "flight": "DAL456",
      "alt_baro": 67,
      "track": 223
    }
  ]
}
```

### 3. Get Aircraft Positions at Time

```
GET /api/v1/aircraft/positions/at-time
```

Returns all aircraft positions at a specific timestamp with optional trace data and spatial filtering.

**Parameters:**
- `timestamp` (query, optional) - ISO 8601 timestamp (defaults to current time)
- `lookback_seconds` (query, optional) - Seconds to look back for positions (default: 40, max: 300)
- `max_trace_points` (query, optional) - Maximum trace points per aircraft (default: 50, max: 200)
- `north`, `east`, `south`, `west` (query, optional) - Bounding box coordinates for spatial filtering

**Response:**
```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "count": 150,
  "positions": [
    {
      "time": "2024-01-01T12:00:00Z",
      "hex": "A12345",
      "alt_baro": 35000,
      "alt_geom": 35100,
      "gs": 450,
      "ias": 280,
      "track": 090,
      "baro_rate": 0,
      "squawk": 1200,
      "flight": "UAL123",
      "h3_index": "8a2a1072b5b7fff",
      "flight_phase": "airborne",
      "h3_res4": "842a1072fffffff",
      "category": "A3",
      "registration": "N12345",
      "trace": [
        {
          "h3_index": "8a2a1072b5b7ffe"
        },
        {
          "h3_index": "8a2a1072b5b7ffd"
        }
      ]
    }
  ]
}
```

### 4. Get H3 Cells in Bounding Box

```
GET /api/v1/aircraft/h3-cells
```

Returns H3 cells within a specified bounding box.

**Parameters:**
- `north`, `east`, `south`, `west` (query, required) - Bounding box coordinates
- `resolution` (query, optional) - H3 resolution level (default: 4)

**Response:**
```json
{
  "north": 40.7829,
  "east": -73.9441,
  "south": 40.6892,
  "west": -74.0445,
  "resolution": 4,
  "cells": ["842a1072fffffff", "842a1073fffffff"],
  "count": 2
}
```

## Running the API

### Local Development

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set environment variables in `.env`:
```env
DATABASE_URL=postgresql+asyncpg://user:pass@host:port/db
ENVIRONMENT=development
```

3. Run the server:
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Docker

```bash
docker build -t tarmactrack-api .
docker run -p 8000:8000 tarmactrack-api
```

## Features

- **Fast Performance** - Single-query approach for aircraft positions with trace data
- **Database Compliance** - Models exactly match the PostgreSQL schema with TimescaleDB partitioning
- **Type Safety** - Full Pydantic validation for requests and responses
- **Geospatial Support** - PostGIS geography types and native H3 hexagonal indexing
- **Aviation Standards** - Proper ICAO/IATA codes, aircraft categories, and flight phases
- **Error Handling** - Comprehensive validation and error responses
- **Documentation** - Auto-generated OpenAPI/Swagger docs at `/docs`

## H3 Integration Details

The API uses PostgreSQL's native `h3index` type for optimal performance:

- **Database Level**: Native H3 functions and indexing
- **API Level**: Custom SQLAlchemy type for proper h3index handling
- **Python Level**: h3-py library for H3 cell generation and manipulation
- **Performance**: No casting required - direct H3 operations

## Runway Detection Algorithm

The airport events endpoint uses a sophisticated algorithm to determine which runway was used:

1. **Spatial Filtering**: Get H3 resolution 7 cells around runway start/end points
2. **Neighbor Expansion**: Include grid disk distance 1 neighbors for approach/departure areas
3. **Parent Conversion**: Convert to H3 resolution 4 parents for efficient database filtering
4. **Event Detection**: Find takeoff/landing flight phases within filtered areas
5. **Runway Scoring**: Calculate score = perpendicular distance + heading difference
6. **Best Match**: Select runway with lowest score (< 30) for each event

## API Documentation

Interactive API documentation is available at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`
- OpenAPI JSON: `http://localhost:8000/api/v1/openapi.json`

## Health Check

The API includes a health check endpoint:
```
GET /health
```

Returns the API status, version, and environment information. 