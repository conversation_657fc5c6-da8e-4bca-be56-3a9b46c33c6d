# Aircraft Occurrences API

## Overview

The Aircraft Occurrences API provides a powerful endpoint for searching and analyzing aircraft flight segments (occurrences) within specified geographic areas and time periods. This API uses a single optimized SQL query with PostgreSQL H3 functions for maximum performance.

## Endpoint

```
POST /api/v1/aircraft/occurrences/search
```

## What Are Occurrences?

**Occurrences** are continuous flight segments grouped by aircraft and time gaps. The system groups aircraft positions where time gaps ≤ `occurrencePeriod` seconds represent continuous flight segments.

For example:
- Aircraft positions at 10:00:00, 10:00:05, 10:00:10 (gaps ≤ 11s) = 1 occurrence
- Aircraft positions at 10:00:00, 10:00:05, 10:00:20 (last gap > 11s) = 2 occurrences

## Key Features

- ✅ **Single SQL Query**: Uses PostgreSQL H3 functions for optimal performance
- ✅ **Flight Segment Grouping**: Groups positions by aircraft with configurable time gaps
- ✅ **H3 Spatial Filtering**: Efficient geographic filtering using indexed h3_res4 column
- ✅ **Multiple Filters**: Time, altitude, heading, flight phase, aircraft type, etc.
- ✅ **Delayed Join**: Aircraft metadata joined only for final results

## Request Format

### Required Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `area` | `string[]` | Array of H3 cell indexes defining the search area |

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `startTime` | `datetime` | `null` | Start time for search (ISO 8601 format) |
| `endTime` | `datetime` | `null` | End time for search (ISO 8601 format) |
| `occurrencePeriod` | `integer` | `11` | Max time gap in seconds to group continuous flight segments |
| `flightPhase` | `string[]` | `null` | Filter by flight phases: `ground`, `takeoff`, `landing`, `airborne` |
| `altMin` | `integer` | `null` | Minimum altitude in feet |
| `altMax` | `integer` | `null` | Maximum altitude in feet |
| `heading` | `integer[]` | `null` | Array of heading values in degrees (0-359) |
| `type` | `string[]` | `null` | Array of aircraft types |
| `class` | `string[]` | `null` | Array of aircraft classes |
| `squawk` | `string[]` | `null` | Array of squawk codes |
| `registration` | `string[]` | `null` | Array of aircraft registrations |

## Example Request

### Basic Search

```json
{
  "area": ["85264003fffffff", "85264017fffffff"]
}
```

### Advanced Search

```json
{
  "startTime": "2024-01-01T12:00:00Z",
  "endTime": "2024-01-01T13:00:00Z",
  "area": ["85264003fffffff", "85264017fffffff"],
  "occurrencePeriod": 11,
  "flightPhase": ["airborne", "landing"],
  "altMin": 1000,
  "altMax": 10000,
  "heading": [270, 271, 272, 273, 274, 275],
  "squawk": ["1200", "7700"],
  "registration": ["N123AB", "G-ABCD"]
}
```

## Response Format

```json
{
  "search_parameters": {
    // Original search request echoed back
  },
  "occurrences": [
    {
      "hex": "a1b2c3",
      "firstseen": "2024-01-01T12:15:30Z",
      "lastseen": "2024-01-01T12:45:22Z",
      "flight": "UAL123",
      "category": "airborne",
      "registration": "N12345",
      "aircraft_category": "A2",
      "position_count": 180
    }
  ],
  "count": 1,
  "execution_time_ms": 245.7
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `hex` | `string` | Aircraft hex identifier |
| `firstseen` | `datetime` | First position timestamp in this occurrence |
| `lastseen` | `datetime` | Last position timestamp in this occurrence |
| `flight` | `string` | Flight number/callsign |
| `category` | `string` | Primary flight phase for this occurrence |
| `registration` | `string` | Aircraft registration |
| `aircraft_category` | `string` | Aircraft category (A0-A7, B0-B7, etc.) |
| `position_count` | `integer` | Number of positions in this occurrence |

## H3 Spatial Indexing

### Area Parameter

The `area` parameter should contain H3 cell indexes at various resolutions (4-11). The system automatically:

1. Uses PostgreSQL `h3_cell_to_parent()` function to calculate resolution 4 parents for indexing
2. Filters by `h3_res4` indexed column for coarse geographic filtering
3. Applies `h3_cell_to_parent()` for precise geographic matching

### Example H3 Cells

```json
{
  "area": [
    "8b2a103b1304fff",  // Resolution 11 cell
    "8b2a103b1322fff",  // Resolution 11 cell  
    "8b2a103b1331fff"   // Resolution 11 cell
  ]
}
```

## Performance Optimization

### Query Strategy

The API uses PostgreSQL H3 functions for optimal performance:

1. **h3_res4 index scan** - Fast coarse geographic filtering
2. **h3_cell_to_parent()** - Precise geographic matching using SQL functions  
3. **Time and other filters** - Applied to reduced dataset
4. **Flight segment grouping** - Time gap analysis with window functions
5. **Aircraft metadata join** - Only for final occurrence results

### Typical Performance

- **Small areas**: 50-200ms for thousands of positions
- **Large areas**: 200-500ms for hundreds of thousands of positions
- **Complex filters**: Minimal additional overhead due to indexed filtering

## Example Usage Scenarios

### 1. Airport Traffic Analysis

Search for all aircraft occurrences around an airport:

```json
{
  "startTime": "2024-01-01T06:00:00Z",
  "endTime": "2024-01-01T18:00:00Z",
  "area": ["85264003fffffff", "85264017fffffff"],
  "flightPhase": ["takeoff", "landing"]
}
```

### 2. High Altitude Traffic

Find high-altitude commercial traffic:

```json
{
  "area": ["85264003fffffff"],
  "flightPhase": ["airborne"],
  "altMin": 30000,
  "altMax": 42000,
  "occurrencePeriod": 30
}
```

### 3. Emergency Squawk Detection

Search for emergency squawk codes:

```json
{
  "area": ["85264003fffffff"],
  "squawk": ["7700", "7600", "7500"]
}
```

### 4. Specific Aircraft Tracking

Track specific registrations:

```json
{
  "startTime": "2024-01-01T00:00:00Z",
  "endTime": "2024-01-01T23:59:59Z",
  "area": ["85264003fffffff"],
  "registration": ["N123AB", "G-ABCD"]
}
```

## Error Handling

### Common Errors

| Status Code | Error | Description |
|-------------|-------|-------------|
| `400` | Invalid search parameters | Missing area, invalid time range, etc. |
| `422` | Validation error | Invalid data types, malformed request |
| `500` | Database error | SQL execution error, connection issues |

### Example Error Response

```json
{
  "error": "Validation Error",
  "detail": "Search area (H3 cells) is required"
}
```

## Testing

Use the provided test script to verify the API:

```bash
cd api
python test_occurrences.py
```

The test script demonstrates:
- Basic API calls
- Parameter validation
- Response parsing
- Error handling

## Integration with Frontend

The frontend interface (`page.tsx`) automatically constructs the API request based on user inputs:

- **Time Range**: Maps to `startTime`/`endTime`
- **Search Area**: Converts radius/polygon to H3 cells for `area`
- **Altitude Filters**: Maps to `altMin`/`altMax` and `flightPhase`
- **Heading Range**: Converts heading range to array for `heading`
- **Aircraft Filters**: Maps to `type`, `class`, `squawk`, `registration`

## Database Schema Compatibility

The API works with the existing `aircraft_pos` table structure:

- ✅ Uses `h3_res4` index for performance
- ✅ Supports TimescaleDB hypertable partitioning  
- ✅ Compatible with all existing flight phase enums
- ✅ Leverages aircraft table joins for metadata

## Performance Monitoring

The API returns execution time in the response for monitoring:

```json
{
  "execution_time_ms": 245.7
}
```

Monitor this metric to identify:
- Slow queries (>1000ms)
- Performance regressions
- Optimal H3 cell sizing

## Best Practices

1. **Limit Search Areas**: Use specific H3 cells rather than large regions
2. **Use Time Ranges**: Always specify reasonable time windows
3. **Monitor Performance**: Watch execution times for optimization opportunities
4. **Validate H3 Cells**: Ensure H3 cell format is correct before requests
5. **Handle Empty Results**: Plan for scenarios with no matching occurrences 