# Aircraft Positions API Documentation

## Overview

The Aircraft Positions API provides real-time and historical aircraft position data. The API offers two endpoints:

1. **Standard Endpoint** (`/positions/at-time`): Traditional format with timestamp in each position
2. **Optimized Endpoint** (`/positions/at-time/optimized`): Efficient format with timestamp listed once

Both endpoints use intelligent lookback functionality to handle irregular position reporting.

## Endpoints

### Standard Format
```
GET /api/v1/aircraft/positions/at-time
```

### Optimized Format (Recommended)
```
GET /api/v1/aircraft/positions/at-time/optimized
```

*The optimized endpoint reduces payload size by 15-30% by avoiding timestamp repetition.*

## How It Works

### 1. **Lookback Logic**

Instead of requiring exact timestamp matches, the API uses a smart lookback mechanism:

- **Target Time**: The timestamp you're interested in (defaults to NOW() if not provided)
- **Lookback Window**: Configurable period (default: 40 seconds) to search for the most recent position
- **Per-Aircraft Selection**: For each aircraft, returns only the most recent position within the time window

### 2. **Database Query Strategy**

The API uses an optimized SQL query with window functions:

```sql
-- Step 1: Create subquery with row numbers for each aircraft
WITH ranked_positions AS (
    SELECT 
        ap.time, ap.hex, ap.alt_baro, ap.alt_geom, ap.gs, ap.ias, 
        ap.track, ap.baro_rate, ap.squawk, ap.flight, 
        ap.h3_index, ap.flight_phase, ap.h3_res4,
        a.category, a.registration,
        ROW_NUMBER() OVER (
            PARTITION BY ap.hex 
            ORDER BY ap.time DESC
        ) as rn
    FROM aircraft_pos ap
    JOIN aircraft a ON ap.hex = a.hex
    WHERE ap.time BETWEEN (target_time - interval '40 seconds') AND target_time
)
-- Step 2: Select only the most recent position per aircraft
SELECT * FROM ranked_positions WHERE rn = 1 ORDER BY hex;
```

### 3. **Data Enrichment**

Each position includes:
- **Position Data**: Altitude, speed, heading, climb rate
- **Aircraft Metadata**: Category, registration number
- **Flight Information**: Call sign, flight phase
- **Geospatial Data**: H3 indices for spatial queries

## Parameters

Both endpoints accept the same parameters:

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `timestamp` | datetime | No | NOW() | Target timestamp in ISO 8601 format |
| `lookback_seconds` | integer | No | 40 | Seconds to look back (1-300) |
| `max_trace_points` | integer | No | 50 | Maximum trace points per aircraft (0-200) |
| `north` | float | No | None | North boundary latitude for spatial filtering |
| `east` | float | No | None | East boundary longitude for spatial filtering |
| `south` | float | No | None | South boundary latitude for spatial filtering |
| `west` | float | No | None | West boundary longitude for spatial filtering |

## Usage Examples

### 1. **Current Positions (Optimized Format)**

Get all current aircraft positions with optimized response:

```bash
curl "http://localhost:8000/api/v1/aircraft/positions/at-time/optimized"
```

### 2. **Historical Positions (Standard Format)**

Get positions at a specific time with standard format:

```bash
curl "http://localhost:8000/api/v1/aircraft/positions/at-time?timestamp=2024-01-01T12:00:00Z"
```

### 3. **Custom Lookback Period (Optimized)**

Use a longer lookback window with optimized format:

```bash
curl "http://localhost:8000/api/v1/aircraft/positions/at-time/optimized?timestamp=2024-01-01T12:00:00Z&lookback_seconds=60"
```

### 4. **Spatial Filtering (Optimized)**

Get positions within a geographic bounding box:

```bash
curl "http://localhost:8000/api/v1/aircraft/positions/at-time/optimized?north=40.7&east=-73.9&south=40.6&west=-74.1"
```

## Response Formats

### Standard Format Response

```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "count": 150,
  "positions": [
    {
      "time": "2024-01-01T11:59:58Z",
      "hex": "ABC123",
      "alt_baro": 35000,
      "alt_geom": 35100,
      "gs": 450,
      "ias": 280,
      "track": 90,
      "baro_rate": 0,
      "squawk": 1200,
      "flight": "UAL123",
      "h3_index": "8a1fb46622dffff",
      "flight_phase": "airborne",
      "h3_res4": "841fb466fffffff",
      "category": "A3",
      "registration": "N12345",
      "trace": [
        {"h3_index": "8a1fb46622deeee"},
        {"h3_index": "8a1fb46622ddddd"}
      ]
    }
  ]
}
```

### Optimized Format Response (Recommended)

```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "actual_time": "2024-01-01T11:59:58Z",
  "count": 150,
  "positions": [
    {
      "hex": "ABC123",
      "alt_baro": 35000,
      "alt_geom": 35100,
      "gs": 450,
      "ias": 280,
      "track": 90,
      "baro_rate": 0,
      "squawk": 1200,
      "flight": "UAL123",
      "h3_index": "8a1fb46622dffff",
      "flight_phase": "airborne",
      "h3_res4": "841fb466fffffff",
      "category": "A3",
      "registration": "N12345",
      "trace": [
        {"h3_index": "8a1fb46622deeee"},
        {"h3_index": "8a1fb46622ddddd"}
      ]
    }
  ]
}
```

### Key Differences

| Feature | Standard Format | Optimized Format |
|---------|----------------|------------------|
| Individual `time` field | ✅ Present in each position | ❌ Removed from positions |
| Top-level `timestamp` | ✅ Requested timestamp | ✅ Requested timestamp |
| Top-level `actual_time` | ❌ Not provided | ✅ Actual data timestamp |
| Payload size | Larger (timestamp repeated) | **15-30% smaller** |
| Backward compatibility | ✅ Existing clients | ❌ Requires client updates |

## Field Descriptions

### Standard Response Fields
- **timestamp**: The requested timestamp
- **count**: Number of positions returned
- **positions**: Array of aircraft positions with individual timestamps

### Optimized Response Fields
- **timestamp**: The requested timestamp
- **actual_time**: The actual timestamp of the position data (may differ from requested due to lookback)
- **count**: Number of positions returned  
- **positions**: Array of aircraft positions WITHOUT individual timestamps

### Position Fields (Both Formats)
- **hex**: Unique aircraft identifier (ICAO 24-bit address)
- **alt_baro**: Barometric altitude in feet
- **alt_geom**: Geometric altitude in feet
- **gs**: Ground speed in knots
- **ias**: Indicated airspeed in knots
- **track**: Ground track in degrees (0-359)
- **baro_rate**: Barometric rate of climb/descent in feet/minute
- **squawk**: Transponder code

### Flight Information
- **flight**: Flight call sign/identifier
- **flight_phase**: Current flight phase (`ground`, `takeoff`, `airborne`, `landing`)

### Aircraft Metadata
- **category**: Aircraft category (A0-A7, B0-B7, C0-C7)
- **registration**: Aircraft registration number

### Geospatial
- **h3_index**: H3 geospatial index (resolution varies)
- **h3_res4**: H3 index at resolution 4 (for aggregation)

### Trace Data
- **trace**: Array of historical H3 cell positions (ordered chronologically, oldest first)

## Aircraft Categories

| Code | Description |
|------|-------------|
| A0 | No ADS-B Emitter Category Information |
| A1 | Light (< 15,500 lbs) |
| A2 | Small (15,500 to 75,000 lbs) |
| A3 | Large (75,000 to 300,000 lbs) |
| A4 | High Vortex Large (e.g., B-757) |
| A5 | Heavy (> 300,000 lbs) |
| A6 | High Performance (> 5g acceleration and 400 kts) |
| A7 | Rotorcraft |
| B1 | Glider / sailplane |
| B2 | Lighter-than-air |
| B3 | Parachutist / Skydiver |
| B4 | Ultralight / hang-glider / paraglider |
| B6 | Unmanned Aerial Vehicle |
| B7 | Space / Trans-atmospheric vehicle |
| C1 | Surface Vehicle – Emergency Vehicle |
| C2 | Surface Vehicle – Service Vehicle |
| C3 | Point Obstacle (includes tethered balloons) |

## Error Handling

### 400 Bad Request
Invalid timestamp format:
```json
{
  "error": "Validation Error",
  "detail": "Invalid timestamp format: time data '2024-13-01' does not match format"
}
```

### 500 Internal Server Error
Database or server errors:
```json
{
  "error": "Internal Server Error",
  "detail": "An unexpected error occurred"
}
```

## Performance Considerations

1. **TimescaleDB Optimization**: The API leverages TimescaleDB's time-based partitioning for efficient queries
2. **Index Usage**: Queries use indexes on `time`, `hex`, and `flight_phase`
3. **Result Limiting**: Each query returns at most one position per aircraft
4. **Memory Efficiency**: Streaming results processing for large datasets
5. **Payload Optimization**: Optimized endpoint reduces network transfer by 15-30%

## Migration Guide

### Migrating to Optimized Format

**Before (Standard Format):**
```javascript
// Client code for standard format
const response = await fetch('/api/v1/aircraft/positions/at-time');
const data = await response.json();

data.positions.forEach(position => {
  console.log(`Aircraft ${position.hex} at ${position.time}`);
});
```

**After (Optimized Format):**
```javascript
// Client code for optimized format
const response = await fetch('/api/v1/aircraft/positions/at-time/optimized');
const data = await response.json();

data.positions.forEach(position => {
  console.log(`Aircraft ${position.hex} at ${data.actual_time}`);
});
```

**Key Changes:**
1. Use `/optimized` endpoint
2. Replace `position.time` with `data.actual_time`
3. Optional: Use `data.timestamp` for requested time vs `data.actual_time` for actual data time

## Use Cases

### 1. **Real-time Air Traffic Monitoring (Optimized)**
```bash
# Get current positions every 10 seconds with optimized format
while true; do
  curl "http://localhost:8000/api/v1/aircraft/positions/at-time/optimized" | jq '.count'
  sleep 10
done
```

### 2. **Historical Traffic Analysis**
```bash
# Get positions for specific time periods
for hour in {00..23}; do
  timestamp="2024-01-01T${hour}:00:00Z"
  curl "http://localhost:8000/api/v1/aircraft/positions/at-time/optimized?timestamp=${timestamp}"
done
```

### 3. **Flight Tracking with Spatial Filter**
```bash
# Track aircraft in specific region with optimized format
curl "http://localhost:8000/api/v1/aircraft/positions/at-time/optimized?north=40.8&east=-73.7&south=40.6&west=-74.2" | \
  jq '.positions[] | select(.flight != null)'
```

## Rate Limiting

The API follows standard rate limiting practices:
- Maximum 100 requests per minute per IP
- Burst allowance of 10 requests
- Returns `429 Too Many Requests` when exceeded

## Recommendations

1. **Use Optimized Format**: For production applications, prefer `/optimized` endpoint for better performance
2. **Enable Trace Data**: Set `max_trace_points=50` for flight path visualization
3. **Spatial Filtering**: Use bounding box parameters for regional monitoring
4. **Reasonable Lookback**: Keep `lookback_seconds` between 10-60 for optimal performance
5. **Caching**: Cache responses for 1-2 seconds to avoid excessive API calls 