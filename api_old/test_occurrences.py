#!/usr/bin/env python3
"""
Simple test script for the aircraft occurrences API endpoint.

This script demonstrates how to call the new /aircraft/occurrences/search endpoint
with various search parameters.
"""

import asyncio
import json
from datetime import datetime, timedelta
import httpx


async def test_occurrences_endpoint():
    """Test the occurrences search endpoint."""
    
    # API endpoint
    base_url = "http://localhost:8000"
    endpoint = f"{base_url}/api/v1/aircraft/occurrences/search"
    
    # Example search request - adjust these parameters as needed
    search_request = {
        "startTime": (datetime.utcnow() - timedelta(hours=1)).isoformat() + "Z",
        "endTime": datetime.utcnow().isoformat() + "Z",
        "area": [
            "85264003fffffff",  # Example H3 cells - replace with actual cells for your area
            "85264017fffffff"
        ],
        "occurrencePeriod": 11,
        "flightPhase": ["airborne", "landing"],
        "altMin": 1000,
        "altMax": 10000
    }
    
    print("🚀 Testing Aircraft Occurrences API")
    print(f"📡 Endpoint: {endpoint}")
    print(f"📋 Request payload:")
    print(json.dumps(search_request, indent=2))
    print("\n" + "="*50 + "\n")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                endpoint,
                json=search_request,
                timeout=30.0
            )
            
            print(f"📊 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success! Found {data['count']} occurrences")
                print(f"⏱️  Execution time: {data['execution_time_ms']:.1f}ms")
                
                # Print first few occurrences as examples
                if data['occurrences']:
                    print(f"\n📋 Sample occurrences (showing first 3):")
                    for i, occurrence in enumerate(data['occurrences'][:3]):
                        print(f"\n{i+1}. Aircraft {occurrence['hex']}:")
                        print(f"   ✈️  Flight: {occurrence.get('flight', 'N/A')}")
                        print(f"   📍 Registration: {occurrence.get('registration', 'N/A')}")
                        print(f"   🕐 First seen: {occurrence['firstseen']}")
                        print(f"   🕑 Last seen: {occurrence['lastseen']}")
                        print(f"   📊 Positions: {occurrence['position_count']}")
                        print(f"   🛩️  Phase: {occurrence.get('category', 'N/A')}")
                else:
                    print("ℹ️  No occurrences found for the given criteria")
                    
            else:
                print(f"❌ Error: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"📝 Error details: {error_data}")
                except:
                    print(f"📝 Raw response: {response.text}")
                    
    except httpx.ConnectError:
        print("❌ Connection Error: Could not connect to the API server")
        print("💡 Make sure the FastAPI server is running on http://localhost:8000")
        print("   You can start it with: uvicorn app.main:app --reload")
        
    except httpx.TimeoutException:
        print("⏰ Timeout Error: The request took too long")
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")


def test_with_minimal_parameters():
    """Test with minimal required parameters only."""
    
    print("\n" + "="*50)
    print("🧪 Testing with minimal parameters...")
    
    minimal_request = {
        "area": ["85264003fffffff"]  # Only required parameter
    }
    
    return minimal_request


async def test_various_scenarios():
    """Test various search scenarios."""
    
    scenarios = [
        {
            "name": "Basic time range search",
            "params": {
                "startTime": (datetime.utcnow() - timedelta(hours=2)).isoformat() + "Z",
                "endTime": (datetime.utcnow() - timedelta(hours=1)).isoformat() + "Z",
                "area": ["85264003fffffff", "85264017fffffff"]
            }
        },
        {
            "name": "Ground aircraft only",
            "params": {
                "area": ["85264003fffffff"],
                "flightPhase": ["ground"],
                "occurrencePeriod": 30
            }
        },
        {
            "name": "High altitude airborne",
            "params": {
                "area": ["85264003fffffff"],
                "flightPhase": ["airborne"],
                "altMin": 20000,
                "altMax": 40000
            }
        }
    ]
    
    print("\n" + "="*60)
    print("🧪 Testing various scenarios...")
    
    for scenario in scenarios:
        print(f"\n📋 Scenario: {scenario['name']}")
        print(f"🔧 Parameters: {json.dumps(scenario['params'], indent=2)}")
        # In a real test, you would make the HTTP request here
        print("⏭️  (Skipping actual request for demo)")


if __name__ == "__main__":
    print("Aircraft Occurrences API Test Script")
    print("====================================")
    
    # Run the main test
    asyncio.run(test_occurrences_endpoint())
    
    # Show minimal parameters example
    minimal = test_with_minimal_parameters()
    print(f"📋 Minimal request: {json.dumps(minimal, indent=2)}")
    
    # Show various scenarios
    asyncio.run(test_various_scenarios())
    
    print("\n✅ Test script completed!")
    print("\n💡 Tips:")
    print("   - Adjust the H3 cells in 'area' to match your geographic region")
    print("   - Modify time ranges to match when you have data")
    print("   - Check the API docs at http://localhost:8000/docs") 