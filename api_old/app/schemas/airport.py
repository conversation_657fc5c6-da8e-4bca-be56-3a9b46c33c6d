from datetime import datetime
from typing import Optional, List, Tuple
from pydantic import BaseModel, Field, validator
from app.models.airport import RunwaySurfaceEnum
from app.models.aircraft import AircraftCategoryEnum, FlightPhaseEnum


class RunwayBase(BaseModel):
    """Base runway schema."""
    name: str = Field(..., max_length=10, description="Runway identifier (e.g., '09L', '27R')")
    length_ft: Optional[int] = Field(None, ge=0, description="Runway length in feet")
    width_ft: Optional[int] = Field(None, ge=0, description="Runway width in feet")
    surface: RunwaySurfaceEnum = Field(..., description="Runway surface type")


class RunwayCreate(RunwayBase):
    """Schema for creating a runway."""
    airport_icao: str = Field(..., min_length=4, max_length=4, description="Airport ICAO code")
    centerline: Optional[str] = Field(None, description="GeoJSON LineString for runway centerline")


class RunwayUpdate(BaseModel):
    """Schema for updating a runway."""
    length_ft: Optional[int] = Field(None, ge=0)
    width_ft: Optional[int] = Field(None, ge=0)
    surface: Optional[RunwaySurfaceEnum] = None
    centerline: Optional[str] = None


class RunwayResponse(BaseModel):
    """Runway response model."""
    name: str = Field(..., description="Runway name/identifier")
    length_ft: Optional[int] = Field(None, description="Runway length in feet")
    width_ft: Optional[int] = Field(None, description="Runway width in feet")
    surface: RunwaySurfaceEnum = Field(..., description="Runway surface type")
    
    class Config:
        from_attributes = True


class AirportBase(BaseModel):
    """Base airport schema."""
    icao: str = Field(..., min_length=4, max_length=4, description="ICAO airport code")
    iata: Optional[str] = Field(None, min_length=3, max_length=3, description="IATA airport code")
    name: Optional[str] = Field(None, max_length=255, description="Airport name")
    elevation_ft: Optional[int] = Field(None, description="Airport elevation in feet")

    @validator('icao')
    def icao_must_be_uppercase(cls, v):
        return v.upper() if v else v

    @validator('iata')
    def iata_must_be_uppercase(cls, v):
        return v.upper() if v else v


class AirportCreate(AirportBase):
    """Schema for creating an airport."""
    location: Optional[str] = Field(None, description="GeoJSON Point for airport location")


class AirportUpdate(BaseModel):
    """Schema for updating an airport."""
    iata: Optional[str] = Field(None, min_length=3, max_length=3)
    name: Optional[str] = Field(None, max_length=255)
    elevation_ft: Optional[int] = None
    location: Optional[str] = Field(None, description="GeoJSON Point for airport location")

    @validator('iata')
    def iata_must_be_uppercase(cls, v):
        return v.upper() if v else v


class AirportResponse(BaseModel):
    """Airport response model."""
    icao: str = Field(..., description="4-letter ICAO code")
    iata: Optional[str] = Field(None, description="3-letter IATA code")
    name: Optional[str] = Field(None, description="Airport name")
    elevation_ft: Optional[int] = Field(None, description="Airport elevation in feet")
    location: Optional[str] = Field(None, description="GeoJSON Point for airport location")
    runways: List[RunwayResponse] = Field(default_factory=list, description="List of runways")
    
    class Config:
        from_attributes = True


class AirportSummary(BaseModel):
    """Schema for airport summary (without runways)."""
    icao: str
    iata: Optional[str]
    name: Optional[str]
    elevation_ft: Optional[int]
    location: Optional[str]
    runway_count: int = Field(..., description="Number of runways at this airport")

    class Config:
        from_attributes = True


class RunwayEventResponse(BaseModel):
    """Airport runway event response model."""
    time: datetime = Field(..., description="Event timestamp")
    event_type: FlightPhaseEnum = Field(..., description="Event type (takeoff or landing)")
    registration: Optional[str] = Field(None, description="Aircraft registration")
    category: Optional[AircraftCategoryEnum] = Field(None, description="Aircraft category")
    hex: str = Field(..., description="Aircraft hex identifier")
    runway: str = Field(..., description="Runway identifier")
    flight: Optional[str] = Field(None, description="Flight number/callsign")
    alt_baro: Optional[int] = Field(None, description="Barometric altitude in feet")
    track: Optional[int] = Field(None, description="Aircraft track in degrees")
    
    class Config:
        from_attributes = True


class AirportEventsResponse(BaseModel):
    """Airport events response model."""
    icao: str = Field(..., description="Airport ICAO code")
    start_time: datetime = Field(..., description="Query start time")
    end_time: datetime = Field(..., description="Query end time")
    events: List[RunwayEventResponse] = Field(default_factory=list, description="List of runway events")
    count: int = Field(..., description="Number of events returned")
    
    class Config:
        from_attributes = True


# Error response schemas
class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(..., description="Error type")
    detail: str = Field(..., description="Error details")


class ValidationErrorResponse(BaseModel):
    """Validation error response."""
    error: str = "Validation Error"
    detail: List[dict] = Field(..., description="List of validation errors") 