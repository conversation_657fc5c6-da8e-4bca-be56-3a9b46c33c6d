from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, validator
from enum import Enum

from app.models.aircraft import AircraftCategoryEnum, FlightPhaseEnum


class FlightPhaseEnum(str, Enum):
    """Flight phase types - matches database enum exactly."""
    ground = "ground"
    takeoff = "takeoff"
    landing = "landing"
    airborne = "airborne"


class AircraftCategoryEnum(str, Enum):
    """Aircraft category types - matches database enum exactly."""
    A0 = "A0"
    A1 = "A1"
    A2 = "A2"
    A3 = "A3"
    A4 = "A4"
    A5 = "A5"
    A6 = "A6"
    A7 = "A7"
    B0 = "B0"
    B1 = "B1"
    B2 = "B2"
    B3 = "B3"
    B4 = "B4"
    B5 = "B5"
    B6 = "B6"
    B7 = "B7"
    C0 = "C0"
    C1 = "C1"
    C2 = "C2"
    C3 = "C3"
    C4 = "C4"
    C5 = "C5"
    C6 = "C6"
    C7 = "C7"
    D0 = "D0"
    D1 = "D1"
    D2 = "D2"
    D3 = "D3"
    D4 = "D4"
    D5 = "D5"
    D6 = "D6"
    D7 = "D7"
    UNKNOWN = "UNKNOWN"


class AircraftPositionBase(BaseModel):
    """Base model for aircraft position data."""
    alt_baro: Optional[int] = Field(None, description="Barometric altitude in feet")
    alt_geom: Optional[int] = Field(None, description="Geometric altitude in feet")
    gs: Optional[int] = Field(None, description="Ground speed in knots")
    track: Optional[int] = Field(None, description="Track angle in degrees")
    baro_rate: Optional[int] = Field(None, description="Barometric rate of climb/descent in feet per minute")
    squawk: Optional[int] = Field(None, description="Squawk code")
    flight: Optional[str] = Field(None, description="Flight number/callsign")
    h3_index: Optional[str] = Field(None, description="H3 geographic index for position")
    h3_res4: Optional[str] = Field(None, description="H3 geographic index at resolution 4")


class AircraftPositionResponse(AircraftPositionBase):
    """Response model for aircraft position data."""
    time: datetime = Field(..., description="Position timestamp")
    hex: str = Field(..., description="Aircraft hex identifier")
    registration: Optional[str] = Field(None, description="Aircraft registration")
    category: Optional[AircraftCategoryEnum] = Field(None, description="Aircraft category")
    trace: Optional[List[str]] = Field(None, description="Historical H3 cell positions (oldest first)")


class AircraftEventBase(AircraftPositionBase):
    """Base model for aircraft event data."""
    flight_phase: FlightPhaseEnum = Field(..., description="Current flight phase")
    airport_icao: Optional[str] = Field(None, description="Airport ICAO code")
    runway_name: Optional[str] = Field(None, description="Runway name")


class AircraftEventResponse(AircraftEventBase):
    """Response model for aircraft event data."""
    time: datetime = Field(..., description="Event timestamp")
    hex: str = Field(..., description="Aircraft hex identifier")
    registration: Optional[str] = Field(None, description="Aircraft registration")
    category: Optional[AircraftCategoryEnum] = Field(None, description="Aircraft category")


class AircraftPositionsAtTimeResponse(BaseModel):
    """Response model for aircraft positions at a specific time."""
    timestamp: datetime = Field(..., description="Requested timestamp")
    positions: List[AircraftPositionResponse] = Field(..., description="List of aircraft positions")
    count: int = Field(..., description="Number of positions returned")


class OptimizedAircraftPositionsAtTimeResponse(BaseModel):
    """Response model for optimized aircraft positions at a specific time."""
    timestamp: datetime = Field(..., description="Requested timestamp")
    actual_time: datetime = Field(..., description="Actual timestamp of the position data")
    positions: List[AircraftPositionResponse] = Field(..., description="List of aircraft positions")
    count: int = Field(..., description="Number of positions returned")


class H3CellsResponse(BaseModel):
    """Response model for H3 cells within a bounding box."""
    cells: List[str] = Field(..., description="List of H3 cell indexes")


class ErrorResponse(BaseModel):
    """Response model for error messages."""
    detail: str = Field(..., description="Error message")


class OccurrencesSearchRequest(BaseModel):
    """Request model for searching aircraft occurrences."""
    startTime: Optional[datetime] = Field(None, description="Start time for search")
    endTime: Optional[datetime] = Field(None, description="End time for search")
    area: List[str] = Field(..., description="List of H3 cell indexes defining search area")
    occurrencePeriod: int = Field(11, ge=1, le=300, description="Maximum time gap between positions to consider them part of the same occurrence (seconds)")
    flightPhase: Optional[List[FlightPhaseEnum]] = Field(None, description="Filter by flight phase")
    altMin: Optional[int] = Field(None, ge=0, description="Minimum altitude in feet")
    altMax: Optional[int] = Field(None, ge=0, description="Maximum altitude in feet")
    category: Optional[List[AircraftCategoryEnum]] = Field(None, description="Filter by aircraft category")

    @validator('endTime')
    def end_time_must_be_after_start_time(cls, v, values):
        if 'startTime' in values and v and values['startTime'] and v <= values['startTime']:
            raise ValueError('endTime must be after startTime')
        return v

    @validator('altMax')
    def alt_max_must_be_greater_than_alt_min(cls, v, values):
        if 'altMin' in values and v and values['altMin'] and v <= values['altMin']:
            raise ValueError('altMax must be greater than altMin')
        return v


class Occurrence(BaseModel):
    """Model for an aircraft occurrence (flight segment)."""
    hex: str = Field(..., description="Aircraft hex identifier")
    registration: Optional[str] = Field(None, description="Aircraft registration")
    category: Optional[AircraftCategoryEnum] = Field(None, description="Aircraft category")
    flight: Optional[str] = Field(None, description="Flight number/callsign")
    start_time: datetime = Field(..., description="Start time of occurrence")
    end_time: datetime = Field(..., description="End time of occurrence")
    flight_phase: FlightPhaseEnum = Field(..., description="Flight phase")
    airport_icao: Optional[str] = Field(None, description="Airport ICAO code")
    runway_name: Optional[str] = Field(None, description="Runway name")
    h3_cells: List[str] = Field(..., description="List of H3 cell indexes in occurrence")
    alt_min: Optional[int] = Field(None, description="Minimum altitude in feet")
    alt_max: Optional[int] = Field(None, description="Maximum altitude in feet")


class OccurrencesSearchResponse(BaseModel):
    """Response model for aircraft occurrences search."""
    search_parameters: OccurrencesSearchRequest = Field(..., description="Search parameters used")
    occurrences: List[Occurrence] = Field(..., description="List of occurrences found")
    count: int = Field(..., description="Number of occurrences found")
    execution_time_ms: float = Field(..., description="Query execution time in milliseconds")


class FlightTraceResponse(BaseModel):
    """Response model for aircraft flight trace."""
    hex: str = Field(..., description="Aircraft hex identifier")
    registration: Optional[str] = Field(None, description="Aircraft registration")
    category: Optional[AircraftCategoryEnum] = Field(None, description="Aircraft category")
    trace: List[AircraftPositionResponse] = Field(..., description="List of positions in trace")
    count: int = Field(..., description="Number of positions in trace") 