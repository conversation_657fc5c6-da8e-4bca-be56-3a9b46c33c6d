from typing import Optional, List
from datetime import datetime, timezone, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text
from sqlalchemy.orm import selectinload
from app.models.airport import Airport, Runway
from app.schemas.airport import AirportCreate, AirportUpdate, AirportResponse, AirportSummary, RunwayEventResponse
from geoalchemy2.functions import ST_AsGeoJSON


class AirportService:
    """Service layer for airport operations."""

    @staticmethod
    async def get_airport_by_icao(db: AsyncSession, icao: str) -> Optional[AirportResponse]:
        """Get airport by ICAO code with runways and location."""
        # First get the basic airport data with location
        airport_stmt = (
            select(
                Airport.icao,
                Airport.iata,
                Airport.name,
                Airport.elevation_ft,
                ST_AsGeoJSON(Airport.location).label('location')
            )
            .where(Airport.icao == icao.upper())
        )
        
        airport_result = await db.execute(airport_stmt)
        airport_row = airport_result.first()
        
        if not airport_row:
            return None
        
        # Then get the runways separately
        runways_stmt = (
            select(Runway)
            .where(Runway.airport_icao == icao.upper())
        )
        
        runways_result = await db.execute(runways_stmt)
        runways = runways_result.scalars().all()
        
        # Convert to response model
        airport_response = AirportResponse(
            icao=airport_row.icao,
            iata=airport_row.iata,
            name=airport_row.name,
            elevation_ft=airport_row.elevation_ft,
            location=airport_row.location,
            runways=[
                {
                    'name': runway.name,
                    'length_ft': runway.length_ft,
                    'width_ft': runway.width_ft,
                    'surface': runway.surface
                }
                for runway in runways
            ]
        )
        
        return airport_response

    @staticmethod
    async def get_airports(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None
    ) -> List[dict]:
        """Get list of airports with optional search."""
        stmt = select(
            Airport.icao,
            Airport.iata,
            Airport.name,
            Airport.elevation_ft,
            ST_AsGeoJSON(Airport.location).label('location'),
            Airport.created_at,
            Airport.updated_at
        ).offset(skip).limit(limit)
        
        if search:
            search_term = f"%{search.upper()}%"
            stmt = stmt.where(
                (Airport.icao.ilike(search_term)) |
                (Airport.iata.ilike(search_term)) |
                (Airport.name.ilike(search_term))
            )
        
        result = await db.execute(stmt)
        rows = result.fetchall()
        
        return [
            {
                'icao': row.icao,
                'iata': row.iata,
                'name': row.name,
                'elevation_ft': row.elevation_ft,
                'location': row.location,
                'created_at': row.created_at,
                'updated_at': row.updated_at,
                'runways': []  # For list view, we don't include runways by default
            }
            for row in rows
        ]

    @staticmethod
    async def get_airport_summary(db: AsyncSession, icao: str) -> Optional[dict]:
        """Get airport summary with runway count."""
        stmt = (
            select(
                Airport.icao,
                Airport.iata,
                Airport.name,
                Airport.elevation_ft,
                ST_AsGeoJSON(Airport.location).label('location'),
                func.count(Runway.name).label('runway_count')
            )
            .outerjoin(Runway, Airport.icao == Runway.airport_icao)
            .where(Airport.icao == icao.upper())
            .group_by(
                Airport.icao,
                Airport.iata,
                Airport.name,
                Airport.elevation_ft,
                Airport.location
            )
        )
        
        result = await db.execute(stmt)
        row = result.first()
        
        if row:
            return {
                'icao': row.icao,
                'iata': row.iata,
                'name': row.name,
                'elevation_ft': row.elevation_ft,
                'location': row.location,
                'runway_count': row.runway_count
            }
        return None

    @staticmethod
    async def create_airport(db: AsyncSession, airport_data: AirportCreate) -> Airport:
        """Create a new airport."""
        db_airport = Airport(**airport_data.dict(exclude={'location'}))
        
        # Handle location if provided
        if airport_data.location:
            # In a real implementation, you'd parse the GeoJSON and create a proper Geography object
            # For now, we'll store it as-is (you'll need to implement proper GeoJSON parsing)
            pass
        
        db.add(db_airport)
        await db.commit()
        await db.refresh(db_airport)
        return db_airport

    @staticmethod
    async def update_airport(
        db: AsyncSession,
        icao: str,
        airport_data: AirportUpdate
    ) -> Optional[Airport]:
        """Update an existing airport."""
        airport = await AirportService.get_airport_by_icao(db, icao)
        
        if not airport:
            return None
        
        update_data = airport_data.dict(exclude_unset=True, exclude={'location'})
        
        for field, value in update_data.items():
            setattr(airport, field, value)
        
        # Handle location update if provided
        if airport_data.location is not None:
            # In a real implementation, you'd parse the GeoJSON and create a proper Geography object
            pass
        
        await db.commit()
        await db.refresh(airport)
        return airport

    @staticmethod
    async def delete_airport(db: AsyncSession, icao: str) -> bool:
        """Delete an airport and its runways."""
        stmt = select(Airport).where(Airport.icao == icao.upper())
        result = await db.execute(stmt)
        airport = result.scalar_one_or_none()
        
        if not airport:
            return False
        
        await db.delete(airport)
        await db.commit()
        return True

    @staticmethod
    async def airport_exists(db: AsyncSession, icao: str) -> bool:
        """Check if airport exists."""
        stmt = select(Airport.icao).where(Airport.icao == icao.upper())
        result = await db.execute(stmt)
        return result.scalar_one_or_none() is not None

    @staticmethod
    async def get_runway_events(
        db: AsyncSession,
        icao: str,
        start_time: datetime,
        end_time: datetime
    ) -> List[RunwayEventResponse]:
        """
        Get takeoff and landing events for an airport within a time range.
        
        Uses H3 spatial filtering around runway endpoints to efficiently find
        aircraft positions, then calculates runway scores based on distance
        and heading to determine the most likely runway for each event.
        
        Args:
            db: Database session
            icao: Airport ICAO code
            start_time: Start of time range
            end_time: End of time range
            
        Returns:
            List of runway events with calculated runway assignments
        """
        
        # SQL query with parameters for ICAO and time range
        sql_query = """
        WITH runway_lines AS (
            SELECT
                airport_icao,
                name as runway_name,
                centerline
            FROM runways
            WHERE airport_icao = :icao
              AND centerline IS NOT NULL
        ),
        runway_endpoints AS (
            SELECT
                airport_icao,
                runway_name,
                centerline,
                ST_StartPoint(centerline::geometry) as start_point,
                ST_EndPoint(centerline::geometry) as end_point
            FROM runway_lines
        ),
        distinct_endpoint_h3_cells AS (
            -- First, get the distinct H3 cells for the start points
            SELECT h3_lat_lng_to_cell(start_point, 7) as h3_cell
            FROM runway_endpoints
            WHERE start_point IS NOT NULL
            UNION
            -- Union with the distinct H3 cells for the end points
            SELECT h3_lat_lng_to_cell(end_point, 7) as h3_cell
            FROM runway_endpoints
            WHERE end_point IS NOT NULL
        ),
        disk_cells AS (
            -- Get all cells within a grid disk of distance 1
            SELECT h3_grid_disk(h3_cell, 1) as h3_res7_cell
            FROM distinct_endpoint_h3_cells
        ),
        airport_h3_res4_cells AS (
            SELECT DISTINCT h3_cell_to_parent(h3_res7_cell, 4) as h3_res4_parent
            FROM disk_cells
        ),
        takeoff_landing_events AS (
            -- Get the takeoff/landing events
            SELECT 
                ap.time,
                a.registration,
                a.category,
                ap.hex,
                ap.flight_phase as event_type,
                ap.flight,
                ap.alt_baro,
                ap.track,
                ap.h3_index,
                h3_cell_to_lat_lng(ap.h3_index) as aircraft_position
            FROM aircraft_pos ap
            INNER JOIN aircraft a ON ap.hex = a.hex
            INNER JOIN airport_h3_res4_cells ah ON ap.h3_res4 = ah.h3_res4_parent
            WHERE ap.time >= :start_time
              AND ap.time <= :end_time
              AND ap.flight_phase IN ('takeoff', 'landing')
              AND ap.h3_index IS NOT NULL
              AND ap.track IS NOT NULL
        ),
        runway_scores AS (
            -- Calculate score for each event against each runway
            SELECT 
                tle.*,
                rl.runway_name,
                rl.centerline,
                -- Calculate perpendicular distance in meters
                ST_Distance(
                    ST_GeogFromText('POINT(' || (aircraft_position)[0] || ' ' || (aircraft_position)[1] || ')'),
                    rl.centerline
                ) as distance_meters,
                -- Calculate runway bearing (azimuth from start to end)
                degrees(ST_Azimuth(
                    ST_StartPoint(rl.centerline::geometry),
                    ST_EndPoint(rl.centerline::geometry)
                )) as runway_bearing,
                -- Calculate heading difference (accounting for circular nature)
                LEAST(
                    ABS(tle.track - degrees(ST_Azimuth(
                        ST_StartPoint(rl.centerline::geometry),
                        ST_EndPoint(rl.centerline::geometry)
                    ))),
                    360 - ABS(tle.track - degrees(ST_Azimuth(
                        ST_StartPoint(rl.centerline::geometry),
                        ST_EndPoint(rl.centerline::geometry)
                    )))
                ) as heading_difference,
                -- Total score = distance + heading difference
                ST_Distance(
                    ST_GeogFromText('POINT(' || (aircraft_position)[0] || ' ' || (aircraft_position)[1] || ')'),
                    rl.centerline
                ) + LEAST(
                    ABS(tle.track - degrees(ST_Azimuth(
                        ST_StartPoint(rl.centerline::geometry),
                        ST_EndPoint(rl.centerline::geometry)
                    ))),
                    360 - ABS(tle.track - degrees(ST_Azimuth(
                        ST_StartPoint(rl.centerline::geometry),
                        ST_EndPoint(rl.centerline::geometry)
                    )))
                ) as total_score
            FROM takeoff_landing_events tle
            CROSS JOIN runway_lines rl
        ),
        best_runway_per_event AS (
            -- Find the best runway for each event
            SELECT 
                rs.*,
                ROW_NUMBER() OVER (
                    PARTITION BY rs.time, rs.hex 
                    ORDER BY rs.total_score ASC
                ) as score_rank
            FROM runway_scores rs
        )
        -- Final result: events with their best matching runway
        SELECT 
            time,
            registration,
            category,
            hex,
            event_type,
            flight,
            alt_baro,
            track,
            runway_name as runway
        FROM best_runway_per_event
        WHERE score_rank = 1  -- Only the best matching runway
          AND total_score <= 30  -- Filter out poor matches
        ORDER BY time DESC;
        """
        
        # Execute query with parameters
        result = await db.execute(text(sql_query), {
            'icao': icao.upper(),
            'start_time': start_time,
            'end_time': end_time
        })
        
        rows = result.fetchall()
        
        # Convert to response objects
        events = []
        for row in rows:
            event = RunwayEventResponse(
                time=row.time,
                event_type=row.event_type,
                registration=row.registration,
                category=row.category,
                hex=row.hex,
                runway=row.runway,
                flight=row.flight,
                alt_baro=row.alt_baro,
                track=row.track
            )
            events.append(event)
        
        return events 