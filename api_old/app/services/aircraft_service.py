from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc, or_, text, asc
from sqlalchemy.orm import joinedload, selectinload
import logging
import time
import h3

from app.models.aircraft import Aircraft, AircraftPos
from app.schemas.aircraft import AircraftPositionResponse, AircraftTracePoint, OptimizedAircraftPosition, OccurrencesSearchRequest, AircraftOccurrence, FlightTraceResponse, FlightTracePoint

logger = logging.getLogger(__name__)


class AircraftService:
    """Service class for aircraft-related operations."""

    @staticmethod
    def _build_registration_filter(registrations: Optional[List[str]]) -> str:
        """Build registration filter for the final WHERE clause."""
        if not registrations:
            return ""
        
        reg_placeholders = ', '.join([f':reg_{i}' for i in range(len(registrations))])
        return f"WHERE a.registration IN ({reg_placeholders})"

    @staticmethod
    async def search_occurrences(
        db: AsyncSession,
        search_request: OccurrencesSearchRequest
    ) -> <PERSON><PERSON>[List[AircraftOccurrence], float]:
        """
        Search for aircraft occurrences (flight segments) using an optimized single SQL query.
        
        Groups aircraft positions by aircraft and time gaps to identify continuous flight 
        segments (occurrences). Uses PostgreSQL H3 functions for efficient spatial filtering.
        
        Performance strategy:
        1. Uses h3_res4 index for coarse geographic filtering  
        2. Applies h3_cell_to_parent() for precise geographic matching
        3. Groups positions by time gaps ≤ occurrencePeriod seconds
        4. Joins aircraft metadata only for final results
        5. Dynamic early filtering on indexed columns (hex, flight_phase)
        
        Args:
            db: Database session
            search_request: Search parameters including H3 area, time range, filters
            
        Returns:
            Tuple of (list of aircraft occurrences, execution time in milliseconds)
        """
        start_time = time.time()
        
        # Extract search H3 cells
        search_h3_cells = search_request.area if search_request.area else []
        
        # Check if we have H3 cells to search
        if not search_h3_cells:
            logger.warning("No H3 cells provided for search")
            return [], 0.0
        
        # Initialize query parameters
        query_params = {}
        
        # Pre-fetch Aircraft hex Codes (if registrations provided)
        hex_filter = ""
        if search_request.registration:
            hex_query = "SELECT hex FROM aircraft WHERE registration = ANY(:registrations)"
            hex_result = await db.execute(text(hex_query), {'registrations': [r.strip().upper() for r in search_request.registration]})
            hex_codes = [row[0] for row in hex_result.fetchall()]
            
            # Implement Early Exit Optimization
            if not hex_codes:
                logger.info("No matching aircraft found for the provided registrations. Returning early.")
                return [], (time.time() - start_time) * 1000
            
            # Build hex filter for early application
            hex_placeholders = ', '.join([f":hex_{i}" for i in range(len(hex_codes))])
            hex_filter = f" AND ap.hex IN ({hex_placeholders})"
            for i, h in enumerate(hex_codes):
                query_params[f'hex_{i}'] = h
        
        # Build time filter
        time_conditions = []
        if search_request.startTime:
            time_conditions.append("ap.time >= :start_time")
        if search_request.endTime:
            time_conditions.append("ap.time <= :end_time")
        time_filter = " AND " + " AND ".join(time_conditions) if time_conditions else ""
        
        # Build flight phase filter (using = ANY() for better performance)
        flight_phase_filter = ""
        if search_request.flightPhase:
            flight_phase_filter = " AND ap.flight_phase = ANY(:flight_phases)"
            query_params['flight_phases'] = [phase.value for phase in search_request.flightPhase]
        
        # Build altitude filter
        altitude_filter = ""
        if search_request.altMin is not None:
            altitude_filter += " AND ap.alt_baro >= :alt_min"
            query_params['alt_min'] = search_request.altMin
        if search_request.altMax is not None:
            altitude_filter += " AND ap.alt_baro <= :alt_max"
            query_params['alt_max'] = search_request.altMax
        
        # Build heading filter
        heading_filter = ""
        if search_request.heading:
            heading_placeholders = ', '.join([str(h) for h in search_request.heading])
            heading_filter = f" AND ap.track IN ({heading_placeholders})"
        
        # Build squawk filter  
        squawk_filter = ""
        if search_request.squawk:
            squawk_placeholders = ', '.join([str(int(s)) for s in search_request.squawk if s.isdigit()])
            if squawk_placeholders:
                squawk_filter = f" AND ap.squawk IN ({squawk_placeholders})"
        
        # Set occurrence period
        occurrence_period = search_request.occurrencePeriod or 11
        query_params['occurrence_period'] = occurrence_period
        
        # Add time parameters if provided
        if search_request.startTime:
            query_params['start_time'] = search_request.startTime
        if search_request.endTime:
            query_params['end_time'] = search_request.endTime
        
        # SQL query based on the working query structure
        sql_query = f"""
        WITH search_area_h3 AS (
            SELECT UNNEST(ARRAY[{', '.join([f"'{cell}'::h3index" for cell in search_h3_cells])}]) AS h3_index
        ),
        search_area_parents AS (
            -- CORRECT: Get all unique parent cells for the entire search area
            SELECT DISTINCT h3_cell_to_parent(h3_index, 4) as h3_res4_parent
            FROM search_area_h3
        ),
        search_params AS (
            -- This just gets the resolution, assuming all input cells have the same resolution.
            SELECT h3_get_resolution((SELECT h3_index FROM search_area_h3 LIMIT 1)) AS search_resolution
        ),
        filtered_positions AS (
            SELECT ap.*
            FROM aircraft_pos ap
            CROSS JOIN search_params sp
            -- CORRECT: Use IN to filter by all relevant parent cells, leveraging the index.
            WHERE ap.h3_res4 IN (SELECT h3_res4_parent FROM search_area_parents)
              -- DYNAMIC FILTERS ARE APPLIED HERE FOR MAXIMUM EFFICIENCY
              {hex_filter}
              {flight_phase_filter}
              {time_filter}
              {altitude_filter}
              {heading_filter}
              {squawk_filter}
              AND ap.h3_index IS NOT NULL
              AND h3_cell_to_parent(ap.h3_index, sp.search_resolution) IN (
                SELECT h3_index FROM search_area_h3
              )
        ),
        with_time_gaps AS (
            SELECT *,
                   EXTRACT(EPOCH FROM (time - LAG(time) OVER (PARTITION BY hex ORDER BY time))) AS time_gap
            FROM filtered_positions
        ),
        with_group_boundaries AS (
            SELECT *,
                   CASE WHEN time_gap > :occurrence_period OR time_gap IS NULL THEN 1 ELSE 0 END AS is_new_group
            FROM with_time_gaps
        ),
        position_groups AS (
            SELECT *,
                   SUM(is_new_group) OVER (PARTITION BY hex ORDER BY time ROWS UNBOUNDED PRECEDING) AS group_id
            FROM with_group_boundaries
        ),
        occurrence_groups AS (
            SELECT 
                hex,
                group_id,
                MIN(time) AS firstseen,
                MAX(time) AS lastseen,
                (ARRAY_AGG(flight ORDER BY time))[1] AS flight,
                (ARRAY_AGG(flight_phase ORDER BY time))[1] AS category,
                COUNT(*) AS position_count
            FROM position_groups
            GROUP BY hex, group_id
            HAVING COUNT(*) > 0
        )
        SELECT 
            og.hex,
            og.firstseen,
            og.lastseen,
            og.flight,
            og.category,
            a.registration,
            a.category as aircraft_category,
            og.position_count
        FROM occurrence_groups og
        LEFT JOIN aircraft a ON og.hex = a.hex
        ORDER BY og.firstseen, og.hex;
        """
        
        # Log query for debugging
        logger.info(f"🔍 Executing aircraft occurrences search")
        logger.info(f"📊 Query structure: Multi-cell H3 spatial filtering → early indexed filters → time gaps → flight segments → metadata join")
        logger.info(f"   - H3 cells: {len(search_h3_cells)} input cells")
        logger.info(f"   - Performance: h3_res4 index (comprehensive) → early hex/flight_phase filtering → precise H3 filtering → grouping")
        if hex_filter:
            logger.info(f"   - Dynamic optimization: Early hex filtering applied ({len(search_request.registration)} registrations)")
        if flight_phase_filter:
            logger.info(f"   - Dynamic optimization: Early flight_phase filtering applied ({len(search_request.flightPhase)} phases)")
        logger.debug(f"SQL Query: {sql_query}")
        logger.debug(f"Parameters: {query_params}")
        
        # Execute the query
        result = await db.execute(text(sql_query), query_params)
        rows = result.fetchall()
        
        # Convert results to response objects
        occurrences = []
        for row in rows:
            occurrence = AircraftOccurrence(
                hex=row.hex,
                firstseen=row.firstseen,
                lastseen=row.lastseen,
                flight=row.flight,
                category=row.category,
                registration=row.registration,
                aircraft_category=row.aircraft_category,
                position_count=row.position_count
            )
            occurrences.append(occurrence)
        
        # Calculate execution time
        execution_time = (time.time() - start_time) * 1000
        
        logger.info(f"✅ Found {len(occurrences)} occurrences in {execution_time:.1f}ms")
        
        return occurrences, execution_time

    @staticmethod
    async def get_positions_at_time(
        db: AsyncSession,
        target_time: datetime,
        lookback_seconds: int = 40,
        max_trace_points: int = 50,
        north: Optional[float] = None,
        east: Optional[float] = None,
        south: Optional[float] = None,
        west: Optional[float] = None
    ) -> List[AircraftPositionResponse]:
        """
        Get all aircraft positions at a given time with trace data using a SINGLE optimized SQL query.
        
        This method performs ALL operations in a single database query:
        1. First gets H3 cells within the bounding box (if provided)
        2. Gets all positions in those cells for the lookback time
        3. Per hex, returns the latest position and trace history
        
        Args:
            db: Database session
            target_time: The target timestamp to query positions for
            lookback_seconds: How many seconds to look back (default: 40)
            max_trace_points: Maximum number of trace points per aircraft (default: 50)
            north: North boundary latitude for spatial filtering
            east: East boundary longitude for spatial filtering
            south: South boundary latitude for spatial filtering
            west: West boundary longitude for spatial filtering
            
        Returns:
            List of aircraft positions with trace data and aircraft details
        """
        # Calculate the time window
        earliest_time = target_time - timedelta(seconds=lookback_seconds)
        
        # Build H3 cells filter condition if bounding box is provided
        h3_cells_condition = ""
        h3_cells_params = {}
        
        if all(param is not None for param in [north, east, south, west]):
            # Get H3 cells within the bounding box
            h3_cells = AircraftService.get_h3_cells_in_bounds(
                north=north,
                east=east, 
                south=south,
                west=west,
                resolution=4  # Using resolution 4 to match h3_res4 column
            )
            
            if h3_cells:
                # Build IN clause for H3 cells - no casting needed since we're using proper h3index type
                h3_placeholders = ', '.join([f':h3_cell_{i}' for i in range(len(h3_cells))])
                h3_cells_condition = f"AND ap.h3_res4 IN ({h3_placeholders})"
                
                # Add H3 cells to parameters
                for i, cell in enumerate(h3_cells):
                    h3_cells_params[f'h3_cell_{i}'] = cell
        
        # Single comprehensive SQL query that does everything at once
        sql_query = f"""
        WITH 
        -- Step 1: Get latest positions per aircraft within time window and spatial bounds
        latest_positions AS (
            SELECT 
                ap.time,
                ap.hex,
                ap.alt_baro,
                ap.alt_geom,
                ap.gs,
                ap.track,
                ap.baro_rate,
                ap.squawk,
                ap.flight,
                ap.h3_index,
                ap.flight_phase,
                ap.h3_res4,
                a.category,
                a.registration,
                ROW_NUMBER() OVER (
                    PARTITION BY ap.hex 
                    ORDER BY ap.time DESC
                ) as position_rank
            FROM aircraft_pos ap
            INNER JOIN aircraft a ON ap.hex = a.hex
            WHERE ap.time >= :earliest_time 
              AND ap.time <= :target_time
              {h3_cells_condition}
        ),
        
        -- Step 2: Filter to only the most recent position per aircraft
        current_positions AS (
            SELECT *
            FROM latest_positions
            WHERE position_rank = 1
        ),
        
        -- Step 3: Get trace data for all aircraft (excluding current position times)
        trace_data AS (
            SELECT 
                ap.hex,
                ap.h3_index,
                ap.time as trace_time,
                cp.time as current_time,
                ROW_NUMBER() OVER (
                    PARTITION BY ap.hex 
                    ORDER BY ap.time DESC
                ) as trace_rank
            FROM aircraft_pos ap
            INNER JOIN current_positions cp ON ap.hex = cp.hex
            WHERE ap.time >= :earliest_time
              AND ap.time <= :target_time
              AND ap.time != cp.time  -- Exclude current position
              AND ap.h3_index IS NOT NULL
              {h3_cells_condition}
        ),
        
        -- Step 4: Limit trace points per aircraft
        limited_trace AS (
            SELECT 
                hex,
                h3_index,
                trace_time
            FROM trace_data
            WHERE trace_rank <= :max_trace_points
        ),
        
        -- Step 5: Aggregate trace data per aircraft
        aggregated_traces AS (
            SELECT 
                hex,
                ARRAY_AGG(
                    h3_index ORDER BY trace_time ASC  -- Chronological order (oldest first)
                ) as trace_h3_indices
            FROM limited_trace
            GROUP BY hex
        )
        
        -- Final result: Join current positions with their trace data
        SELECT 
            cp.time,
            cp.hex,
            cp.alt_baro,
            cp.alt_geom,
            cp.gs,
            cp.track,
            cp.baro_rate,
            cp.squawk,
            cp.flight,
            cp.h3_index,
            cp.flight_phase,
            cp.h3_res4,
            cp.category,
            cp.registration,
            COALESCE(at.trace_h3_indices, ARRAY[]::h3index[]) as trace_h3_indices
        FROM current_positions cp
        LEFT JOIN aggregated_traces at ON cp.hex = at.hex
        ORDER BY cp.hex;
        """
        
        # Prepare query parameters
        query_params = {
            'earliest_time': earliest_time,
            'target_time': target_time,
            'max_trace_points': max_trace_points if max_trace_points > 0 else 1
        }
        
        # Add H3 cells parameters if filtering by spatial bounds
        query_params.update(h3_cells_params)
        
        # Log the actual SQL query for debugging
        logger.info(f"🚀 Executing single optimized SQL query for aircraft positions")
        logger.debug(f"SQL Query: {sql_query}")
        logger.debug(f"Parameters: {query_params}")
        
        # Execute the single comprehensive query
        result = await db.execute(text(sql_query), query_params)
        rows = result.fetchall()
        
        # Convert results to response objects
        positions = []
        for row in rows:
            # Convert trace h3_indices array to AircraftTracePoint objects
            trace_data = []
            if max_trace_points > 0 and row.trace_h3_indices:
                for h3_index in row.trace_h3_indices:
                    if h3_index:  # Ensure h3_index is not None
                        # Convert h3index to string for API response
                        trace_point = AircraftTracePoint(h3_index=str(h3_index))
                        trace_data.append(trace_point)
            
            position = AircraftPositionResponse(
                time=row.time,
                hex=row.hex,
                alt_baro=row.alt_baro,
                alt_geom=row.alt_geom,
                gs=row.gs,
                track=row.track,
                baro_rate=row.baro_rate,
                squawk=row.squawk,
                flight=row.flight,
                h3_index=str(row.h3_index) if row.h3_index else None,
                flight_phase=row.flight_phase,
                h3_res4=str(row.h3_res4) if row.h3_res4 else None,
                category=row.category,
                registration=row.registration,
                trace=trace_data
            )
            positions.append(position)
        
        logger.info(f"✅ Retrieved {len(positions)} aircraft positions with trace data in SINGLE query (max_trace_points: {max_trace_points})")
        return positions

    @staticmethod
    async def get_optimized_positions_at_time(
        db: AsyncSession,
        target_time: datetime,
        lookback_seconds: int = 40,
        max_trace_points: int = 50,
        north: Optional[float] = None,
        east: Optional[float] = None,
        south: Optional[float] = None,
        west: Optional[float] = None
    ) -> Tuple[List[OptimizedAircraftPosition], datetime]:
        """
        Get all aircraft positions at a given time in optimized format (without individual timestamps).
        
        This method uses the same SQL query as get_positions_at_time but returns data in an optimized
        format where the timestamp is not repeated for each aircraft position.
        
        Args:
            db: Database session
            target_time: The target timestamp to query positions for
            lookback_seconds: How many seconds to look back (default: 40)
            max_trace_points: Maximum number of trace points per aircraft (default: 50)
            north: North boundary latitude for spatial filtering
            east: East boundary longitude for spatial filtering
            south: South boundary latitude for spatial filtering
            west: West boundary longitude for spatial filtering
            
        Returns:
            Tuple of (List of optimized aircraft positions, actual timestamp of the data)
        """
        # Calculate the time window
        earliest_time = target_time - timedelta(seconds=lookback_seconds)
        
        # Build H3 cells filter condition if bounding box is provided
        h3_cells_condition = ""
        h3_cells_params = {}
        
        if all(param is not None for param in [north, east, south, west]):
            # Get H3 cells within the bounding box
            h3_cells = AircraftService.get_h3_cells_in_bounds(
                north=north,
                east=east, 
                south=south,
                west=west,
                resolution=4  # Using resolution 4 to match h3_res4 column
            )
            
            if h3_cells:
                # Build IN clause for H3 cells - no casting needed since we're using proper h3index type
                h3_placeholders = ', '.join([f':h3_cell_{i}' for i in range(len(h3_cells))])
                h3_cells_condition = f"AND ap.h3_res4 IN ({h3_placeholders})"
                
                # Add H3 cells to parameters
                for i, cell in enumerate(h3_cells):
                    h3_cells_params[f'h3_cell_{i}'] = cell
        
        # Single comprehensive SQL query that does everything at once
        sql_query = f"""
        WITH 
        -- Step 1: Get latest positions per aircraft within time window and spatial bounds
        latest_positions AS (
            SELECT 
                ap.time,
                ap.hex,
                ap.alt_baro,
                ap.alt_geom,
                ap.gs,
                ap.track,
                ap.baro_rate,
                ap.squawk,
                ap.flight,
                ap.h3_index,
                ap.flight_phase,
                ap.h3_res4,
                a.category,
                a.registration,
                ROW_NUMBER() OVER (
                    PARTITION BY ap.hex 
                    ORDER BY ap.time DESC
                ) as position_rank
            FROM aircraft_pos ap
            INNER JOIN aircraft a ON ap.hex = a.hex
            WHERE ap.time >= :earliest_time 
              AND ap.time <= :target_time
              {h3_cells_condition}
        ),
        
        -- Step 2: Filter to only the most recent position per aircraft
        current_positions AS (
            SELECT *
            FROM latest_positions
            WHERE position_rank = 1
        ),
        
        -- Step 3: Get trace data for all aircraft (excluding current position times)
        trace_data AS (
            SELECT 
                ap.hex,
                ap.h3_index,
                ap.time as trace_time,
                cp.time as current_time,
                ROW_NUMBER() OVER (
                    PARTITION BY ap.hex 
                    ORDER BY ap.time DESC
                ) as trace_rank
            FROM aircraft_pos ap
            INNER JOIN current_positions cp ON ap.hex = cp.hex
            WHERE ap.time >= :earliest_time
              AND ap.time <= :target_time
              AND ap.time != cp.time  -- Exclude current position
              AND ap.h3_index IS NOT NULL
              {h3_cells_condition}
        ),
        
        -- Step 4: Limit trace points per aircraft
        limited_trace AS (
            SELECT 
                hex,
                h3_index,
                trace_time
            FROM trace_data
            WHERE trace_rank <= :max_trace_points
        ),
        
        -- Step 5: Aggregate trace data per aircraft
        aggregated_traces AS (
            SELECT 
                hex,
                ARRAY_AGG(
                    h3_index ORDER BY trace_time ASC  -- Chronological order (oldest first)
                ) as trace_h3_indices
            FROM limited_trace
            GROUP BY hex
        )
        
        -- Final result: Join current positions with their trace data
        SELECT 
            cp.time,
            cp.hex,
            cp.alt_baro,
            cp.alt_geom,
            cp.gs,
            cp.track,
            cp.baro_rate,
            cp.squawk,
            cp.flight,
            cp.h3_index,
            cp.flight_phase,
            cp.h3_res4,
            cp.category,
            cp.registration,
            COALESCE(at.trace_h3_indices, ARRAY[]::h3index[]) as trace_h3_indices
        FROM current_positions cp
        LEFT JOIN aggregated_traces at ON cp.hex = at.hex
        ORDER BY cp.hex;
        """
        
        # Prepare query parameters
        query_params = {
            'earliest_time': earliest_time,
            'target_time': target_time,
            'max_trace_points': max_trace_points if max_trace_points > 0 else 1
        }
        
        # Add H3 cells parameters if filtering by spatial bounds
        query_params.update(h3_cells_params)
        
        # Log the actual SQL query for debugging
        logger.info(f"🚀 Executing single optimized SQL query for aircraft positions (optimized format)")
        logger.debug(f"SQL Query: {sql_query}")
        logger.debug(f"Parameters: {query_params}")
        
        # Execute the single comprehensive query
        result = await db.execute(text(sql_query), query_params)
        rows = result.fetchall()
        
        # Convert results to optimized response objects
        positions = []
        actual_time = None
        
        for row in rows:
            # Track the most recent actual time from the data
            if actual_time is None or row.time > actual_time:
                actual_time = row.time
                
            # Convert trace h3_indices array to AircraftTracePoint objects
            trace_data = []
            if max_trace_points > 0 and row.trace_h3_indices:
                for h3_index in row.trace_h3_indices:
                    if h3_index:  # Ensure h3_index is not None
                        # Convert h3index to string for API response
                        trace_point = AircraftTracePoint(h3_index=str(h3_index))
                        trace_data.append(trace_point)
            
            # Create optimized position without individual time field
            position = OptimizedAircraftPosition(
                hex=row.hex,
                alt_baro=row.alt_baro,
                alt_geom=row.alt_geom,
                gs=row.gs,
                track=row.track,
                baro_rate=row.baro_rate,
                squawk=row.squawk,
                flight=row.flight,
                h3_index=str(row.h3_index) if row.h3_index else None,
                flight_phase=row.flight_phase,
                h3_res4=str(row.h3_res4) if row.h3_res4 else None,
                category=row.category,
                registration=row.registration,
                trace=trace_data
            )
            positions.append(position)
        
        # If no positions found, use target_time as actual_time
        if actual_time is None:
            actual_time = target_time
            
        logger.info(f"✅ Retrieved {len(positions)} aircraft positions in optimized format (max_trace_points: {max_trace_points})")
        return positions, actual_time

    @staticmethod
    async def get_flight_trace(
        db: AsyncSession,
        hex: str,
        search_time: datetime
    ) -> FlightTraceResponse:
        """
        Get full flight trace for a specific aircraft around a given time.
        
        Looks up to 2 hours ahead and 2 hours back from the provided time
        to get the complete flight trace for the specified aircraft.
        
        Args:
            db: Database session
            hex: Aircraft hex identifier
            search_time: Reference timestamp to search around
            
        Returns:
            FlightTraceResponse with full flight trace and aircraft info
        """
        logger.info(f"Getting flight trace for hex={hex} at time={search_time}")
        
        # Calculate time range: 2 hours before and after
        time_range_start = search_time - timedelta(hours=2)
        time_range_end = search_time + timedelta(hours=2)
        
        # Use the exact SQL query provided by the user, adapted for our service pattern
        sql_query = text("""
            SELECT 
                ap.time,
                ap.hex,
                ap.alt_baro,
                ap.gs,
                ap.flight_phase,
                ap.flight,
                ap.track,
                ap.h3_index,
                ap.squawk,
                a.registration,
                a.category
            FROM aircraft_pos ap
            LEFT JOIN aircraft a ON ap.hex = a.hex
            WHERE ap.hex = :hex
              AND ap.time >= :time_start
              AND ap.time <= :time_end
            ORDER BY ap.time
        """)
        
        # Execute the query
        result = await db.execute(sql_query, {
            'hex': hex,
            'time_start': time_range_start,
            'time_end': time_range_end
        })
        
        rows = result.fetchall()
        logger.info(f"Found {len(rows)} trace points for aircraft {hex}")
        
        # Extract aircraft info from first row (all rows will have same aircraft info)
        registration = None
        category = None
        if rows:
            registration = rows[0][9]  # a.registration
            category = rows[0][10]     # a.category
        
        # Convert rows to FlightTracePoint objects
        trace_points = []
        for row in rows:
            trace_point = FlightTracePoint(
                time=row[0],           # ap.time
                alt_baro=row[2],       # ap.alt_baro
                gs=row[3],            # ap.gs
                flight_phase=row[4],   # ap.flight_phase
                flight=row[5],         # ap.flight
                track=row[6],          # ap.track
                h3_index=row[7],       # ap.h3_index
                squawk=row[8]          # ap.squawk
            )
            trace_points.append(trace_point)
        
        return FlightTraceResponse(
            hex=hex,
            search_time=search_time,
            time_range_start=time_range_start,
            time_range_end=time_range_end,
            trace_points=trace_points,
            count=len(trace_points),
            registration=registration,
            category=category
        )

    @staticmethod
    def get_h3_cells_in_bounds(
        north: float,
        east: float, 
        south: float,
        west: float,
        resolution: int = 4
    ) -> List[str]:
        """
        Get all H3 cells at the given resolution within the specified bounding box.
        
        Args:
            north: North boundary latitude
            east: East boundary longitude  
            south: South boundary latitude
            west: West boundary longitude
            resolution: H3 resolution level (default: 4)
            
        Returns:
            List of H3 cell IDs as strings
        """
        # Create a polygon from bounding box coordinates using LatLngPoly
        # h3-py expects coordinates in (lat, lng) format
        outer_ring = [
            (north, west),  # NW corner
            (north, east),  # NE corner
            (south, east),  # SE corner
            (south, west),  # SW corner
        ]
        
        # Create H3 LatLngPoly object
        polygon = h3.LatLngPoly(outer_ring)
        
        # Convert polygon to H3 cells using the correct h3-py v4.2.2 API
        h3_cells = h3.h3shape_to_cells(polygon, resolution)
        
        # Convert to sorted list of strings
        return sorted(list(h3_cells)) 