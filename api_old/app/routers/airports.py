from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timezone, timedelta

from app.db.database import get_db
from app.schemas.airport import AirportResponse, AirportEventsResponse, ErrorResponse
from app.services.airport_service import AirportService

router = APIRouter(prefix="/airports", tags=["airports"])


def validate_icao_code(icao: str) -> None:
    """Validate ICAO code format - 4 alphanumeric characters."""
    if len(icao) != 4 or not icao.isalnum():
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="ICAO code must be exactly 4 alphanumeric characters"
        )


@router.get(
    "/{icao}",
    response_model=AirportResponse,
    responses={
        404: {"model": ErrorResponse, "description": "Airport not found"},
        422: {"model": ErrorResponse, "description": "Invalid ICAO code format"}
    },
    summary="Get airport by ICAO code",
    description="Retrieve airport information including all runways for the specified ICAO code."
)
async def get_airport(
    icao: str,
    db: AsyncSession = Depends(get_db)
):
    """Get airport information by ICAO code."""
    
    validate_icao_code(icao)
    
    airport = await AirportService.get_airport_by_icao(db, icao.upper())
    
    if not airport:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Airport with ICAO code '{icao.upper()}' not found"
        )
    
    return airport


@router.get(
    "/{icao}/events",
    response_model=AirportEventsResponse,
    responses={
        404: {"model": ErrorResponse, "description": "Airport not found"},
        422: {"model": ErrorResponse, "description": "Invalid ICAO code format"},
        400: {"model": ErrorResponse, "description": "Invalid time range"}
    },
    summary="Get runway events at airport",
    description="""
    Retrieve takeoff and landing events at an airport within a specified time range.
    
    This endpoint uses advanced spatial filtering with H3 hexagonal indexing to efficiently
    find aircraft movements near runway endpoints. For each event, it calculates a score
    based on perpendicular distance to runway centerlines and heading difference to
    determine the most likely runway used.
    
    **Scoring Algorithm:**
    - Score = Distance (meters) + Heading Difference (degrees)
    - Events with scores > 30 are filtered out as poor matches
    - Only the best matching runway per event is returned
    
    **Spatial Filtering:**
    - Uses H3 resolution 7 cells around runway start/end points
    - Includes neighboring cells (grid disk distance 1)
    - Converts to H3 resolution 4 parents for efficient database filtering
    
    Default time range is the last hour if not specified.
    """
)
async def get_airport_events(
    icao: str,
    start_time: Optional[datetime] = Query(
        None,
        description="Start time for events (ISO 8601 format). Defaults to 1 hour ago.",
        example="2024-01-01T11:00:00Z"
    ),
    end_time: Optional[datetime] = Query(
        None,
        description="End time for events (ISO 8601 format). Defaults to current time.",
        example="2024-01-01T12:00:00Z"
    ),
    db: AsyncSession = Depends(get_db)
):
    """Get takeoff and landing events at an airport."""
    
    try:
        validate_icao_code(icao)
        
        # Set default time range if not provided
        if end_time is None:
            end_time = datetime.now(timezone.utc)
        
        if start_time is None:
            start_time = end_time - timedelta(hours=1)
        
        # Ensure times are timezone-aware (assume UTC if not specified)
        if start_time.tzinfo is None:
            start_time = start_time.replace(tzinfo=timezone.utc)
        
        if end_time.tzinfo is None:
            end_time = end_time.replace(tzinfo=timezone.utc)
        
        # Validate time range
        if start_time >= end_time:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start time must be before end time"
            )
        
        # Check if time range is reasonable (not more than 24 hours)
        if (end_time - start_time).total_seconds() > 24 * 3600:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Time range cannot exceed 24 hours"
            )
        
        # Check if airport exists (by trying to get it)
        airport = await AirportService.get_airport_by_icao(db, icao.upper())
        if not airport:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Airport with ICAO code '{icao.upper()}' not found"
            )
        
        # Get runway events
        events = await AirportService.get_runway_events(db, icao.upper(), start_time, end_time)
        
        return AirportEventsResponse(
            icao=icao.upper(),
            start_time=start_time,
            end_time=end_time,
            events=events,
            count=len(events)
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid datetime format: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving airport events: {str(e)}"
        ) 