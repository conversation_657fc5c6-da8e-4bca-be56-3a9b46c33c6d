from datetime import datetime, timezone
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_db
from app.schemas.aircraft import (
    AircraftPositionsAtTimeResponse,
    AircraftPositionResponse,
    OptimizedAircraftPositionsAtTimeResponse,
    H3CellsResponse,
    ErrorResponse,
    OccurrencesSearchRequest,
    OccurrencesSearchResponse,
    FlightTraceResponse,
    AircraftEventResponse
)
from app.services.aircraft_service import AircraftService

router = APIRouter(prefix="/aircraft", tags=["aircraft"])


@router.get(
    "/positions/at-time",
    response_model=AircraftPositionsAtTimeResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid timestamp format"},
        500: {"model": ErrorResponse, "description": "Database error"}
    },
    summary="Get aircraft positions at specific time with optional trace data",
    description="""
    Retrieve all aircraft positions at a specific timestamp with optional historical trace data.
    
    For each aircraft, the system looks back up to 40 seconds from the specified time
    and returns the most recent position within that window. This includes aircraft
    category and registration information from the aircraft table.
    
    **Trace Data:** Each aircraft position now includes an optional 'trace' array containing
    all historical H3 cell positions for that aircraft within the lookback window, ordered
    chronologically (oldest first). The trace excludes the current position and only includes
    H3 cell identifiers to minimize payload size.
    
    **Spatial Filtering:** Optionally filter results by geographic bounding box using
    north, east, south, west parameters. This uses H3 spatial indexing for efficient
    geospatial queries on the h3_res4 indexed column.
    
    If no timestamp is provided, defaults to the current time (NOW()).
    The timestamp should be provided in ISO 8601 format (e.g., 2024-01-01T12:00:00Z).
    If no timezone is specified, UTC is assumed.
    
    **Performance Note:** Trace data now contains only H3 cell identifiers for optimal
    payload size while maintaining full geographic trail information.
    """
)
async def get_positions_at_time(
    timestamp: Optional[datetime] = Query(
        None,
        description="The target timestamp in ISO 8601 format (e.g., 2024-01-01T12:00:00Z). Defaults to current time if not provided.",
        example="2024-01-01T12:00:00Z"
    ),
    lookback_seconds: int = Query(
        40,
        ge=1,
        le=300,
        description="Number of seconds to look back for positions and trace data (1-300 seconds)"
    ),
    max_trace_points: int = Query(
        50,
        ge=0,
        le=200,
        description="Maximum number of trace points per aircraft (0-200). Set to 0 to disable trace data."
    ),
    north: Optional[float] = Query(None, ge=-90, le=90, description="North boundary latitude for spatial filtering"),
    east: Optional[float] = Query(None, ge=-180, le=180, description="East boundary longitude for spatial filtering"),
    south: Optional[float] = Query(None, ge=-90, le=90, description="South boundary latitude for spatial filtering"),
    west: Optional[float] = Query(None, ge=-180, le=180, description="West boundary longitude for spatial filtering"),
    db: AsyncSession = Depends(get_db)
):
    """Get aircraft positions at a specific time."""
    # TODO: Update implementation to use new aircraft_positions table
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="This endpoint is being updated to use the new schema. Please check back later."
    )


@router.get(
    "/positions/at-time/optimized",
    response_model=OptimizedAircraftPositionsAtTimeResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid timestamp format"},
        500: {"model": ErrorResponse, "description": "Database error"}
    },
    summary="Get aircraft positions at specific time with optimized response format",
    description="""
    Retrieve all aircraft positions at a specific timestamp with an optimized response format.
    
    **Optimized Format:** This endpoint returns the same data as the standard positions endpoint,
    but with improved efficiency by listing the timestamp only once at the response level instead
    of repeating it for each aircraft position. This reduces payload size significantly when
    dealing with many aircraft.
    
    **Response Structure:**
    - `timestamp`: The requested timestamp
    - `actual_time`: The actual timestamp of the position data (may differ due to lookback)
    - `positions`: Array of aircraft positions WITHOUT individual time fields
    - `count`: Number of positions returned
    
    For each aircraft, the system looks back up to 40 seconds from the specified time
    and returns the most recent position within that window. This includes aircraft
    category and registration information from the aircraft table.
    
    **Trace Data:** Each aircraft position includes an optional 'trace' array containing
    all historical H3 cell positions for that aircraft within the lookback window, ordered
    chronologically (oldest first).
    
    **Spatial Filtering:** Optionally filter results by geographic bounding box using
    north, east, south, west parameters.
    
    If no timestamp is provided, defaults to the current time (NOW()).
    The timestamp should be provided in ISO 8601 format (e.g., 2024-01-01T12:00:00Z).
    If no timezone is specified, UTC is assumed.
    """
)
async def get_optimized_positions_at_time(
    timestamp: Optional[datetime] = Query(
        None,
        description="The target timestamp in ISO 8601 format (e.g., 2024-01-01T12:00:00Z). Defaults to current time if not provided.",
        example="2024-01-01T12:00:00Z"
    ),
    lookback_seconds: int = Query(
        40,
        ge=1,
        le=300,
        description="Number of seconds to look back for positions and trace data (1-300 seconds)"
    ),
    max_trace_points: int = Query(
        50,
        ge=0,
        le=200,
        description="Maximum number of trace points per aircraft (0-200). Set to 0 to disable trace data."
    ),
    north: Optional[float] = Query(None, ge=-90, le=90, description="North boundary latitude for spatial filtering"),
    east: Optional[float] = Query(None, ge=-180, le=180, description="East boundary longitude for spatial filtering"),
    south: Optional[float] = Query(None, ge=-90, le=90, description="South boundary latitude for spatial filtering"),
    west: Optional[float] = Query(None, ge=-180, le=180, description="West boundary longitude for spatial filtering"),
    db: AsyncSession = Depends(get_db)
):
    """Get aircraft positions at a specific time with optimized response format."""
    # TODO: Update implementation to use new aircraft_positions table
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="This endpoint is being updated to use the new schema. Please check back later."
    )


@router.get(
    "/h3-cells",
    response_model=H3CellsResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid parameters"},
        500: {"model": ErrorResponse, "description": "Error generating H3 cells"}
    },
    summary="Get H3 cells within bounding box",
    description="""
    Get all H3 cells at resolution 4 within the specified bounding box.
    
    The bounding box is defined by North, East, South, West coordinates.
    Returns all H3 cells whose centroids fall within the polygon formed by these bounds.
    """
)
async def get_h3_cells_in_bounds(
    north: float = Query(..., ge=-90, le=90, description="North boundary latitude"),
    east: float = Query(..., ge=-180, le=180, description="East boundary longitude"),
    south: float = Query(..., ge=-90, le=90, description="South boundary latitude"), 
    west: float = Query(..., ge=-180, le=180, description="West boundary longitude"),
    resolution: int = Query(4, ge=0, le=15, description="H3 resolution level (default: 4)")
):
    """Get H3 cells within the specified bounding box."""
    # This endpoint doesn't need schema changes as it's just a utility function
    try:
        cells = await AircraftService.get_h3_cells_in_bounds(
            north=north,
            east=east,
            south=south,
            west=west,
            resolution=resolution
        )
        return H3CellsResponse(cells=cells)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating H3 cells: {str(e)}"
        )


@router.get(
    "/trace",
    response_model=FlightTraceResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid parameters"},
        404: {"model": ErrorResponse, "description": "No data found for aircraft"},
        500: {"model": ErrorResponse, "description": "Database error"}
    },
    summary="Get full flight trace for an aircraft around a specific time",
    description="""
    Retrieve the full flight trace for a specific aircraft around a given timestamp.
    
    The system looks up to 2 hours ahead and 2 hours back from the provided time
    to capture the complete flight trace for the specified aircraft hex identifier.
    
    **Included Data:**
    - time: Position timestamp
    - alt_baro: Barometric altitude in feet
    - gs: Ground speed in knots
    - flight_phase: Current flight phase (ground, takeoff, landing, airborne)
    - flight: Flight number/callsign
    - track: Track angle in degrees
    - h3_index: H3 geographic index for position
    - squawk: Squawk code
    - registration: Aircraft registration (from aircraft table)
    - category: Aircraft category (from aircraft table)
    
    **Example Usage:**
    - hex: acbe3e
    - timestamp: 2025-06-10T16:08:50.001+0800
    
    The timestamp should be provided in ISO 8601 format with timezone information.
    All trace points are returned in chronological order (oldest first).
    """
)
async def get_flight_trace(
    hex: str = Query(
        ...,
        description="Aircraft hex identifier (e.g., 'acbe3e')",
        example="acbe3e",
        min_length=6,
        max_length=6
    ),
    timestamp: datetime = Query(
        ...,
        description="Reference timestamp in ISO 8601 format with timezone (e.g., '2025-06-10T16:08:50.001+0800')",
        example="2025-06-10T16:08:50.001+0800"
    ),
    db: AsyncSession = Depends(get_db)
):
    """Get full flight trace for an aircraft around a specific time."""
    # TODO: Update implementation to use new aircraft_positions table
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="This endpoint is being updated to use the new schema. Please check back later."
    )


@router.post(
    "/occurrences/search",
    response_model=OccurrencesSearchResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid search parameters"},
        500: {"model": ErrorResponse, "description": "Database error"}
    },
    summary="Search for aircraft occurrences (flight segments)",
    description="""
    Search for aircraft occurrences within specified criteria using a single optimized SQL query.
    
    **Occurrences** are continuous flight segments grouped by aircraft and time gaps. The system
    groups positions by aircraft where time gaps ≤ `occurrencePeriod` seconds represent 
    continuous flight segments.
    
    **Key Features:**
    - **Single SQL Query**: Maximum performance using optimized H3 spatial indexing
    - **Flight Segment Grouping**: Groups positions by aircraft with configurable time gaps
    - **H3 Spatial Filtering**: Efficient geographic filtering using H3 cell indexes
    - **Multiple Filters**: Time, altitude, heading, flight phase, aircraft type, etc.
    
    **Search Area**: The `area` parameter should contain H3 cell indexes at various resolutions.
    The system automatically calculates resolution 4 parents for optimal indexing performance.
    
    **Time Grouping**: The `occurrencePeriod` parameter (default: 11 seconds) determines
    the maximum time gap between positions to consider them part of the same flight segment.
    
    **Performance**: Uses the optimized query pattern from the aircraft position API context,
    filtering by `h3_res4` first for maximum efficiency on large datasets.
    
    **Example Request Body:**
    ```json
    {
        "startTime": "2024-01-01T12:00:00Z",
        "endTime": "2024-01-01T13:00:00Z", 
        "area": ["85264003fffffff", "85264017fffffff"],
        "occurrencePeriod": 11,
        "flightPhase": ["airborne", "landing"],
        "altMin": 1000,
        "altMax": 10000
    }
    ```
    """
)
async def search_occurrences(
    search_request: OccurrencesSearchRequest,
    db: AsyncSession = Depends(get_db)
):
    """Search for aircraft occurrences (flight segments)."""
    # TODO: Update implementation to use new aircraft_positions and aircraft_events tables
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="This endpoint is being updated to use the new schema. Please check back later."
    )


# New endpoints for aircraft events

@router.get(
    "/events/at-time",
    response_model=List[AircraftEventResponse],
    responses={
        400: {"model": ErrorResponse, "description": "Invalid timestamp format"},
        500: {"model": ErrorResponse, "description": "Database error"}
    },
    summary="Get aircraft events at specific time",
    description="""
    Retrieve all aircraft events at a specific timestamp.
    
    Events include takeoffs, landings, and other flight phase changes with associated
    airport and runway information when available.
    
    **Spatial Filtering:** Optionally filter results by geographic bounding box using
    north, east, south, west parameters. This uses H3 spatial indexing for efficient
    geospatial queries on the h3_res4 indexed column.
    
    If no timestamp is provided, defaults to the current time (NOW()).
    The timestamp should be provided in ISO 8601 format (e.g., 2024-01-01T12:00:00Z).
    If no timezone is specified, UTC is assumed.
    """
)
async def get_events_at_time(
    timestamp: Optional[datetime] = Query(
        None,
        description="The target timestamp in ISO 8601 format (e.g., 2024-01-01T12:00:00Z). Defaults to current time if not provided.",
        example="2024-01-01T12:00:00Z"
    ),
    lookback_seconds: int = Query(
        40,
        ge=1,
        le=300,
        description="Number of seconds to look back for events (1-300 seconds)"
    ),
    north: Optional[float] = Query(None, ge=-90, le=90, description="North boundary latitude for spatial filtering"),
    east: Optional[float] = Query(None, ge=-180, le=180, description="East boundary longitude for spatial filtering"),
    south: Optional[float] = Query(None, ge=-90, le=90, description="South boundary latitude for spatial filtering"),
    west: Optional[float] = Query(None, ge=-180, le=180, description="West boundary longitude for spatial filtering"),
    db: AsyncSession = Depends(get_db)
):
    """Get aircraft events at a specific time."""
    # TODO: Implement using new aircraft_events table
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="This endpoint is being implemented. Please check back later."
    )


@router.get(
    "/events/by-airport",
    response_model=List[AircraftEventResponse],
    responses={
        400: {"model": ErrorResponse, "description": "Invalid parameters"},
        404: {"model": ErrorResponse, "description": "Airport not found"},
        500: {"model": ErrorResponse, "description": "Database error"}
    },
    summary="Get aircraft events for a specific airport",
    description="""
    Retrieve all aircraft events for a specific airport within a time range.
    
    Events include takeoffs, landings, and other flight phase changes with associated
    runway information when available.
    
    The timestamp range should be provided in ISO 8601 format (e.g., 2024-01-01T12:00:00Z).
    If no timezone is specified, UTC is assumed.
    """
)
async def get_events_by_airport(
    airport_icao: str = Query(
        ...,
        description="Airport ICAO code (e.g., 'KLAX')",
        example="KLAX",
        min_length=4,
        max_length=4
    ),
    start_time: datetime = Query(
        ...,
        description="Start timestamp in ISO 8601 format (e.g., '2024-01-01T12:00:00Z')",
        example="2024-01-01T12:00:00Z"
    ),
    end_time: datetime = Query(
        ...,
        description="End timestamp in ISO 8601 format (e.g., '2024-01-01T13:00:00Z')",
        example="2024-01-01T13:00:00Z"
    ),
    db: AsyncSession = Depends(get_db)
):
    """Get aircraft events for a specific airport."""
    # TODO: Implement using new aircraft_events table
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="This endpoint is being implemented. Please check back later."
    ) 