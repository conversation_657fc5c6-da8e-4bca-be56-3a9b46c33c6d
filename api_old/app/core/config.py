from typing import List, Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "TarmacTrack API"
    PROJECT_DESCRIPTION: str = "Streamlined aircraft tracking and airport information API with native H3 support"
    VERSION: str = "2.1.0"
    
    # Database
    DATABASE_URL: str = "postgresql+asyncpg://tsdbadmin:<EMAIL>:34087/tsdb?ssl=require"
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-this-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "https://yourdomain.com"]
    
    # Environment
    ENVIRONMENT: str = "development"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings() 