from datetime import datetime
from enum import Enum
from typing import Optional
from sqlalchemy import <PERSON>umn, String, Integer, SmallInteger, DateTime, ForeignKey, Enum as SQLEnum, text
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import TIMESTAMP
from sqlalchemy.types import UserDefinedType

from app.db.database import Base


class H3Index(UserDefinedType):
    """Custom SQLAlchemy type for PostgreSQL h3index."""
    
    def get_col_spec(self):
        return "h3index"
    
    def bind_processor(self, dialect):
        def process(value):
            if value is not None:
                return str(value)
            return value
        return process
    
    def result_processor(self, dialect, coltype):
        def process(value):
            if value is not None:
                return str(value)  # Return as string for now, can be converted to h3 objects in service layer
            return value
        return process


class AircraftCategoryEnum(str, Enum):
    """Aircraft category types - matches database enum exactly."""
    A0 = "A0"
    A1 = "A1"
    A2 = "A2"
    A3 = "A3"
    A4 = "A4"
    A5 = "A5"
    A6 = "A6"
    A7 = "A7"
    B0 = "B0"
    B1 = "B1"
    B2 = "B2"
    B3 = "B3"
    B4 = "B4"
    B5 = "B5"
    B6 = "B6"
    B7 = "B7"
    C0 = "C0"
    C1 = "C1"
    C2 = "C2"
    C3 = "C3"
    C4 = "C4"
    C5 = "C5"
    C6 = "C6"
    C7 = "C7"
    D0 = "D0"
    D1 = "D1"
    D2 = "D2"
    D3 = "D3"
    D4 = "D4"
    D5 = "D5"
    D6 = "D6"
    D7 = "D7"
    UNKNOWN = "UNKNOWN"


class FlightPhaseEnum(str, Enum):
    """Flight phase types - matches database enum exactly."""
    ground = "ground"
    takeoff = "takeoff"
    landing = "landing"
    airborne = "airborne"


class Aircraft(Base):
    """Aircraft model - matches database table exactly."""
    __tablename__ = "aircraft"

    hex = Column(String, primary_key=True, index=True)
    category = Column(SQLEnum(AircraftCategoryEnum), nullable=True)
    registration = Column(String, nullable=True, unique=True)

    # Relationships
    positions = relationship("AircraftPosition", back_populates="aircraft")
    events = relationship("AircraftEvent", back_populates="aircraft")


class AircraftPosition(Base):
    """Aircraft position model - matches database table exactly."""
    __tablename__ = "aircraft_positions"

    time = Column(TIMESTAMP(timezone=True), primary_key=True, nullable=False)
    hex = Column(String, ForeignKey("aircraft.hex"), primary_key=True, nullable=False)
    alt_baro = Column(Integer, nullable=True)
    alt_geom = Column(Integer, nullable=True)
    gs = Column(SmallInteger, nullable=True)
    track = Column(SmallInteger, nullable=True)
    baro_rate = Column(SmallInteger, nullable=True)
    squawk = Column(SmallInteger, nullable=True)
    flight = Column(String, nullable=True)
    h3_index = Column(H3Index, nullable=True)  # PostgreSQL h3index type
    h3_res4 = Column(H3Index, nullable=True)  # Generated h3index column

    # Relationship to aircraft
    aircraft = relationship("Aircraft", back_populates="positions")

    __table_args__ = (
        {"postgresql_partition_by": "RANGE (time)"},  # TimescaleDB partitioning hint
    )


class AircraftEvent(Base):
    """Aircraft event model - matches database table exactly."""
    __tablename__ = "aircraft_events"

    time = Column(TIMESTAMP(timezone=True), primary_key=True, nullable=False)
    hex = Column(String, ForeignKey("aircraft.hex"), primary_key=True, nullable=False)
    alt_baro = Column(Integer, nullable=True)
    alt_geom = Column(Integer, nullable=True)
    gs = Column(SmallInteger, nullable=True)
    track = Column(SmallInteger, nullable=True)
    baro_rate = Column(SmallInteger, nullable=True)
    squawk = Column(SmallInteger, nullable=True)
    flight = Column(String, nullable=True)
    h3_index = Column(H3Index, nullable=True)  # PostgreSQL h3index type
    h3_res4 = Column(H3Index, nullable=True)  # Generated h3index column
    flight_phase = Column(SQLEnum(FlightPhaseEnum), nullable=False)
    airport_icao = Column(String(4), ForeignKey("airports.icao"), nullable=True)
    runway_name = Column(String(10), nullable=True)

    # Relationships
    aircraft = relationship("Aircraft", back_populates="events")
    airport = relationship("Airport", foreign_keys=[airport_icao])
    runway = relationship("Runway", foreign_keys=[airport_icao, runway_name])

    __table_args__ = (
        ForeignKeyConstraint(
            ['airport_icao', 'runway_name'],
            ['runways.airport_icao', 'runways.name'],
            ondelete='SET NULL'
        ),
    ) 