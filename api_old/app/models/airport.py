from datetime import datetime
from enum import Enum
from typing import Optional, List
from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import VARCHAR, TIMESTAMP
from geoalchemy2 import Geography
from app.db.database import Base


class RunwaySurfaceEnum(str, Enum):
    """Runway surface types - matches database enum exactly."""
    ASPHALT = "ASPHALT"
    CONCRETE = "CONCRETE"
    GRASS = "GRASS"
    GRAVEL = "GRAVEL"
    DIRT = "DIRT"
    WATER = "WATER"
    UNKNOWN = "UNKNOWN"
    OTHER = "OTHER"


class Airport(Base):
    """Airport model - matches database table exactly."""
    __tablename__ = "airports"

    icao = Column(VARCHAR(4), primary_key=True, index=True)
    iata = Column(VARCHAR(3), nullable=True)
    name = Column(VARCHAR(255), nullable=True)
    location = Column(Geography("POINT", srid=4326), nullable=True)
    elevation_ft = Column(Integer, nullable=True)
    created_at = Column(TIMESTAMP(timezone=True), default=datetime.utcnow, nullable=True)
    updated_at = Column(TIMESTAMP(timezone=True), default=datetime.utcnow, onupdate=datetime.utcnow, nullable=True)

    # Relationship to runways
    runways = relationship("Runway", back_populates="airport", cascade="all, delete-orphan")


class Runway(Base):
    """Runway model - matches database table exactly."""
    __tablename__ = "runways"

    airport_icao = Column(VARCHAR(4), ForeignKey("airports.icao", ondelete="CASCADE", onupdate="CASCADE"), primary_key=True)
    name = Column(VARCHAR(10), primary_key=True)
    length_ft = Column(Integer, nullable=True)
    width_ft = Column(Integer, nullable=True)
    surface = Column(SQLEnum(RunwaySurfaceEnum), nullable=False)
    centerline = Column(Geography("LINESTRING", srid=4326), nullable=True)
    created_at = Column(TIMESTAMP(timezone=True), default=datetime.utcnow, nullable=False)
    updated_at = Column(TIMESTAMP(timezone=True), default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationship to airport
    airport = relationship("Airport", back_populates="runways") 