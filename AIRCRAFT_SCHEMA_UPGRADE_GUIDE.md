# Aircraft Schema Upgrade Guide

This guide walks you through upgrading your aircraft table schema to include all the TAR1090 database fields for enhanced aircraft information.

## Overview

The upgrade adds the following capabilities to your aircraft table:
- **Aircraft Type Information**: ICAO type designators, descriptions, wake turbulence categories
- **Operational Data**: Operator, year built, country of registration
- **Classification Flags**: Military, interesting, privacy flags
- **Enhanced Metadata**: Database source tracking, timestamps

## Files Created/Modified

### 1. Database Migration
- **`aircraft_schema_migration.sql`** - Complete database migration script

### 2. Backend Updates (API)
- **`api/app/shared/enums.py`** - Added `WakeTurbulenceCategory` enum
- **`api/app/models/aircraft.py`** - Updated `Aircraft` model with new fields
- **`api/app/schemas/aircraft.py`** - Updated Pydantic schemas

### 3. Frontend Updates (Webapp)
- **`webapp/src/types/api.ts`** - Added TypeScript interfaces and enums

### 4. Data Population
- **`populate_aircraft_database.py`** - Script to populate database from TAR1090 data

## Step-by-Step Implementation

### Step 1: Run Database Migration

```bash
# Connect to your PostgreSQL database
psql -h localhost -U postgres -d tarmactrack

# Run the migration script
\i aircraft_schema_migration.sql
```

This will:
- Create the `wake_turbulence_category_enum` type
- Add all new columns to the `aircraft` table
- Create indexes for performance
- Add helpful functions and views

### Step 2: Update Database Connection (if needed)

Update the database configuration in `populate_aircraft_database.py`:

```python
DB_CONFIG = {
    'host': 'your_host',
    'port': 5432,
    'database': 'tarmactrack',
    'user': 'your_user',
    'password': 'your_password'
}
```

### Step 3: Install Python Dependencies

```bash
pip install psycopg2-binary
```

### Step 4: Populate Aircraft Database

```bash
# Run the population script
python populate_aircraft_database.py
```

This will:
- Load the ICAO aircraft types cache
- Process all TAR1090 database files
- Insert/update aircraft records with enhanced information
- Handle conflicts gracefully (upsert logic)

### Step 5: Restart API Services

After the database changes, restart your API to pick up the new schema:

```bash
# If using Docker
docker-compose restart api

# If running locally
# Restart your FastAPI server
```

### Step 6: Update Frontend (if needed)

The TypeScript types have been updated. If you're using the new aircraft fields in your frontend:

```bash
cd webapp
npm run build  # Rebuild to check for type errors
```

## New Database Schema

### Added Columns

| Column | Type | Description |
|--------|------|-------------|
| `icao_type_designator` | VARCHAR(4) | ICAO aircraft type (e.g., "A320", "B738") |
| `type_description` | VARCHAR(10) | Type description code (e.g., "L2J") |
| `wake_turbulence_category` | ENUM | L/M/H/J wake category |
| `aircraft_description` | TEXT | Full aircraft name/description |
| `operator` | TEXT | Owner/operator name |
| `year_built` | INTEGER | Year manufactured |
| `is_military` | BOOLEAN | Military aircraft flag |
| `is_interesting` | BOOLEAN | Special/interesting aircraft flag |
| `is_pia` | BOOLEAN | Privacy ICAO Address flag |
| `is_ladd` | BOOLEAN | Limiting Aircraft Data Displayed flag |
| `country_code` | VARCHAR(2) | Country derived from ICAO hex |
| `database_source` | VARCHAR(50) | Source of the data |
| `last_updated` | TIMESTAMPTZ | Last update timestamp |
| `created_at` | TIMESTAMPTZ | Creation timestamp |

### New Indexes

- `idx_aircraft_icao_type` - For type-based queries
- `idx_aircraft_operator` - For operator searches
- `idx_aircraft_military` - For military aircraft filtering
- `idx_aircraft_country` - For country-based queries
- `idx_aircraft_wake_category` - For wake turbulence filtering

## Usage Examples

### Query Enhanced Aircraft Data

```sql
-- Get all military aircraft
SELECT hex, registration, icao_type_designator, operator 
FROM aircraft 
WHERE is_military = true;

-- Get all Airbus A320 family aircraft
SELECT hex, registration, operator, year_built
FROM aircraft 
WHERE icao_type_designator LIKE 'A32%';

-- Get aircraft by wake turbulence category
SELECT hex, registration, icao_type_designator
FROM aircraft 
WHERE wake_turbulence_category = 'H';  -- Heavy aircraft
```

### Use the Enhanced View

```sql
-- Query the enhanced view with derived fields
SELECT hex, registration, aircraft_class, suggested_icon
FROM aircraft_enhanced
WHERE country_code = 'US' AND is_military = false;
```

### API Usage

The enhanced aircraft data will be available through your existing API endpoints:

```javascript
// Example API response
{
  "hex": "A12345",
  "category": "A3",
  "registration": "N123AA",
  "icao_type_designator": "A320",
  "type_description": "L2J",
  "wake_turbulence_category": "M",
  "aircraft_description": "Airbus A320-200",
  "operator": "American Airlines",
  "year_built": 2015,
  "is_military": false,
  "is_interesting": false,
  "country_code": "US",
  "database_source": "tar1090"
}
```

## Icon Selection Logic

The migration includes a view with suggested icons based on TAR1090 logic:

```sql
SELECT hex, registration, suggested_icon 
FROM aircraft_enhanced 
WHERE hex = 'A12345';
```

Possible icon values:
- `airliner` - Commercial airliners
- `helicopter` - Helicopters
- `cessna` - Light aircraft
- `military` - Military aircraft
- `light` - Light aircraft (A1, A2 category)
- `heavy` - Heavy aircraft (A4, A5 category)
- `unknown` - Unknown/unclassified

## Maintenance

### Regular Updates

To keep the aircraft database current, you can:

1. **Re-run the population script** periodically with updated TAR1090 data
2. **Use the upsert function** for individual aircraft updates:

```sql
SELECT upsert_aircraft_from_tar1090('A12345', 'N123AA', 'A320', '00', 'Airbus A320-200');
```

### Monitoring

Check database statistics:

```sql
-- Count aircraft by source
SELECT database_source, COUNT(*) 
FROM aircraft 
GROUP BY database_source;

-- Count by country
SELECT country_code, COUNT(*) 
FROM aircraft 
WHERE country_code IS NOT NULL
GROUP BY country_code 
ORDER BY COUNT(*) DESC;
```

## Troubleshooting

### Common Issues

1. **Migration fails**: Check PostgreSQL version and permissions
2. **Population script fails**: Verify database connection and file paths
3. **API errors**: Restart API services after schema changes
4. **Type errors**: Rebuild frontend after TypeScript updates

### Rollback (if needed)

To rollback the schema changes:

```sql
-- Remove added columns (CAUTION: This will lose data!)
ALTER TABLE aircraft 
DROP COLUMN icao_type_designator,
DROP COLUMN type_description,
-- ... (list all added columns)

-- Drop the enum
DROP TYPE wake_turbulence_category_enum;
```

## Performance Notes

- All new columns are indexed appropriately for common queries
- The enhanced view provides derived fields without storage overhead
- Batch inserts are used for efficient data population
- Upsert logic prevents duplicate entries while allowing updates

This upgrade significantly enhances your aircraft database with comprehensive type information, enabling better aircraft identification, classification, and visualization in your application.
