# 🚀 TarmacTrack Development Workflow

## 🎯 Overview

This guide provides a complete development workflow for TarmacTrack, from rapid local development to production deployment on Coolify.

## 🏃‍♂️ Daily Development Cycle

### 1. Docker Development with Hot Reload (Recommended)

**Start Everything with Docker (Fastest & Most Consistent):**
```bash
# Start everything with hot reload
make dev-up

# Both services auto-reload on file changes
# API: http://localhost:8001/docs (FastAPI auto-reload)
# Webapp: http://localhost:3000 (Next.js hot reload)

# View logs for debugging
make logs
```

**Benefits:**
- ⚡ Instant feedback with hot reload
- 🐛 Full debugging capabilities
- 📊 Direct API access
- 🔍 Easy database inspection
- 🌐 Consistent environment
- 📦 No local dependency management needed

### 2. Alternative: Local Development (Optional)

**If you prefer running services directly:**
```bash
# Terminal 1: API with hot reload
cd api
uvicorn app.main:app --reload --port 8001

# Terminal 2: Webapp with hot reload
cd webapp
npm run dev
```

**When to use:**
- Need to debug at the process level
- Want faster startup times
- Working on one service only

### 3. Integration Testing

**Test in Docker (Before Committing):**
```bash
# Test containerized environment
docker-compose up -d

# Verify everything works together
# Check logs for issues
docker-compose logs -f

# Clean up
docker-compose down
```

**Benefits:**
- 🔄 Environment consistency
- 🌐 Service communication
- 📦 Dependency validation

## 🧪 Pre-Deployment Testing

### 4. Quality Checks

```bash
# Run all tests
cd api && python -m pytest
cd webapp && npm run lint

# Test production build locally
docker-compose -f docker-compose.yml build
docker-compose -f docker-compose.yml up -d

# Verify health endpoints
curl http://localhost:8001/health
curl http://localhost:3000
```

### 5. Staging Deployment

**Use Coolify as Staging:**
```bash
# Create staging branch
git checkout -b staging

# Deploy to staging environment
git push origin staging

# Test in staging before production
```

## 🚢 Production Deployment

### 6. Release Process

```bash
# Prepare release
git checkout main
git pull origin main

# Tag release
git tag -a v1.2.0 -m "Release v1.2.0"
git push origin v1.2.0

# Deploy to production
git push origin main
# Coolify auto-deploys
```

### 7. Post-Deployment

```bash
# Verify deployment
curl https://your-api-domain.sslip.io/health

# Test webapp functionality
# Monitor Coolify logs
# Verify user workflows
```

## 🔧 Configuration Management

### Environment Variables

**Development:**
- API connects to TimescaleDB Cloud
- Webapp connects to `localhost:8001`
- Hot reload enabled

**Production:**
- API uses production database
- Webapp uses Coolify API domain
- Build-time environment variables
- Health checks enabled

### Key Files

| File | Purpose |
|------|---------|
| `docker-compose.yml` | Development environment |
| `docker-compose.prod.yml` | Production deployment |
| `api/Dockerfile` | API container definition |
| `webapp/Dockerfile` | Webapp container with build args |

## 🚨 Emergency Procedures

### Quick Rollback

```bash
# Find last working version
git tag -l

# Emergency rollback
git checkout v1.1.0
git push origin main --force

# Monitor deployment
```

### Debug Production

```bash
# Access Coolify dashboard
# Check container logs
# Access container shells if needed
docker exec -it container-name bash

# Test health endpoints
curl http://localhost:8000/health
```

## 📋 Daily Commands

```bash
# Development
make dev-up         # Start Docker development
make logs           # View all logs
make test           # Run tests
make clean          # Clean Docker

# Deployment
git push origin main      # Deploy production
git push origin staging   # Deploy staging

# Debugging
make api-shell      # Access API container
make webapp-shell   # Access webapp container
```

## 🎯 Best Practices

### Code Quality
1. Always test locally before committing
2. Keep TypeScript types in sync
3. Test error scenarios
4. Monitor performance

### Deployment Safety
1. Use staging for testing
2. Tag all releases
3. Monitor after deployment
4. Have rollback plan ready

### Security
1. Keep secrets in Coolify environment
2. Regular dependency updates
3. Monitor security advisories
4. Use non-root containers

## 🔄 Typical Day Workflow

```bash
# Morning: Start development
git pull origin main
make dev-up                   # Start Docker with hot reload

# Development: Make changes
# Edit code - both API and webapp auto-reload in Docker
# View logs: make logs
# Test endpoints: http://localhost:8001/docs
# Test webapp: http://localhost:3000

# Pre-commit: Quality checks
make test                     # Run tests in containers
# Everything already tested in consistent Docker environment

# Commit: Push changes
git add .
git commit -m "Add new feature"
git push origin feature-branch

# Merge: Deploy to staging
git checkout staging
git merge feature-branch
git push origin staging
# Test in staging

# Production: Deploy when ready
git checkout main
git merge staging
git tag -a v1.x.x -m "Release notes"
git push origin main
git push origin v1.x.x
# Monitor deployment
```

This workflow ensures rapid development with production reliability! 🚀 