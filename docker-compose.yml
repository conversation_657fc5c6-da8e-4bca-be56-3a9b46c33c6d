services:
  # FastAPI Backend
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    ports:
      - "8001:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://tsdbadmin:<EMAIL>:34087/tsdb?ssl=require
      - ENVIRONMENT=development
      - ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:3001"]
    volumes:
      - ./api:/app
    networks:
      - tarmactrack-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Next.js Frontend
  webapp:
    build:
      context: ./webapp
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://api:8000
    depends_on:
      - api
    networks:
      - tarmactrack-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  tarmactrack-network:
    driver: bridge

volumes:
  api_data: 