# TarmacTrack

A full-stack application for managing airport and runway data, built with FastAPI backend and Next.js frontend.

## Architecture

```
tarmactrack/
├── api/               # FastAPI backend with PostGIS integration
├── webapp/            # Next.js frontend application
│   └── src/
│       ├── types/     # TypeScript API types
│       ├── components/
│       ├── lib/
│       └── app/
├── context/           # Additional project documentation
└── docker-compose.yml # Development environment setup
```

## Features

### Backend (FastAPI)
- **RESTful API** with automatic OpenAPI documentation
- **Type-safe** request/response validation with Pydantic
- **Async database operations** with SQLAlchemy and PostgreSQL/TimescaleDB
- **Geographic data support** with PostGIS for airport locations and runway centerlines
- **Docker containerization** for easy deployment
- **CORS enabled** for frontend integration
- **Comprehensive error handling** and logging

### Frontend (Next.js)
- **Type-safe API client** with webapp TypeScript types
- **Modern React** with Next.js 14+
- **Ready for integration** with the FastAPI backend

### Shared
- **Type definitions** synchronized between API and frontend
- **Unified development environment** with Docker Compose

## Quick Start

### Prerequisites

- Docker and Docker Compose
- Node.js 18+ (for local development only)
- Python 3.11+ (for local development only)

### Using Docker (Recommended)

```bash
# Clone the repository
git clone <your-repo-url>
cd tarmactrack

# Start all services (API + Webapp)
make setup

# Or manually:
docker-compose up -d
```

### Services

After starting, the following services will be available:

- **API**: http://localhost:8001
- **API Documentation**: http://localhost:8001/docs
- **Web App**: http://localhost:3000
- **Health Check**: http://localhost:8001/health

### Development vs Production

#### Development Mode (with hot reload)
```bash
# Uses docker-compose.override.yml automatically
make dev-up
# or
docker-compose up -d
```

#### Production Mode (optimized builds)
```bash
make prod-up
# or
docker-compose -f docker-compose.yml up -d
```

### Local Development (without Docker)

#### API Only
```bash
cd api
pip install -r requirements.txt
uvicorn app.main:app --reload
```

#### Web App Only
```bash
cd webapp
npm install
npm run dev
```

#### Both with Make
```bash
# API
make api-dev

# Web App  
make webapp-dev
```

## Docker Architecture

Both services are fully containerized and production-ready:

- **API Container**: FastAPI with PostgreSQL/TimescaleDB connection
- **Webapp Container**: Next.js with optimized production build
- **Networking**: Services communicate via Docker internal network
- **Health Checks**: Both services include health monitoring
- **Development**: Hot reload support with volume mounting
- **Production**: Multi-stage builds for optimized images

## Database Configuration

The application connects to a TimescaleDB/PostgreSQL database. Update the connection string in:

- **Docker**: `docker-compose.yml` environment variables
- **Local**: `.env` file in the `api/` directory

Current schema:
- `airports` table with ICAO, IATA, name, location (PostGIS point), elevation
- `runways` table with airport reference, dimensions, surface type, centerline (PostGIS linestring)

## API Endpoints

### Airport Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/v1/airports/{icao}` | Get airport details with runways |
| GET | `/api/v1/airports/{icao}/summary` | Get airport summary |
| GET | `/api/v1/airports/` | List airports (with search & pagination) |
| POST | `/api/v1/airports/` | Create new airport |
| PUT | `/api/v1/airports/{icao}` | Update airport |
| DELETE | `/api/v1/airports/{icao}` | Delete airport |

### Example Usage

```bash
# Get airport by ICAO
curl http://localhost:8001/api/v1/airports/KJFK

# Search airports
curl "http://localhost:8001/api/v1/airports/?search=kennedy&limit=10"

# Create airport
curl -X POST http://localhost:8001/api/v1/airports/ \
  -H "Content-Type: application/json" \
  -d '{"icao": "TEST", "name": "Test Airport", "elevation_ft": 100}'
```

## Type Safety Between Frontend and Backend

The project maintains type safety through shared TypeScript definitions:

1. **Pydantic schemas** in the API define request/response structures
2. **TypeScript interfaces** in `webapp/src/types/api.ts` mirror these schemas
3. **API client** in the webapp uses these types for compile-time safety

### Example Type Flow

```python
# api/app/schemas/airport.py
class AirportResponse(BaseModel):
    icao: str
    name: Optional[str]
    runways: List[RunwayResponse]
```

```typescript
// webapp/src/types/api.ts
export interface AirportResponse {
  icao: string;
  name?: string;
  runways: RunwayResponse[];
}
```

```typescript
// webapp/src/lib/api-client.ts
async getAirport(icao: string): Promise<ApiResponse<AirportResponse>> {
  // Fully typed API call
}
```

## Development Commands

### Using Makefile

```bash
make help          # Show all available commands
make build         # Build Docker containers
make up            # Start all services
make down          # Stop all services
make logs          # View logs
make test          # Run API tests
make clean         # Clean up Docker resources
```

### Docker Compose

```bash
docker-compose up api          # Start only API
docker-compose up webapp       # Start only webapp
docker-compose logs -f api     # Follow API logs
docker-compose exec api bash   # Shell into API container
```

## Security Features

- **Input validation** at API boundaries
- **SQL injection protection** via SQLAlchemy ORM
- **Non-root Docker containers**
- **CORS configuration** for controlled frontend access
- **Structured error responses** that don't leak sensitive information

## Benefits of Streamlined Architecture

1. **Type Safety**: Ensures API and frontend stay in sync with webapp types
2. **Unified Development**: Single Docker Compose setup
3. **Version Consistency**: API and frontend versions are linked
4. **Simplified Deployment**: Single repository to deploy
5. **Code Clarity**: Clear separation of concerns, no unnecessary shared abstractions

## Future Integration Ideas

1. **Generate TypeScript types** from Pydantic schemas automatically
2. **Shared validation logic** between frontend and backend
3. **OpenAPI client generation** for the frontend
4. **Shared constants and enums**
5. **Integration testing** across both services

## Environment Variables

### API (.env)
```env
DATABASE_URL=postgresql+asyncpg://user:pass@host:port/db
SECRET_KEY=your-secret-key
ENVIRONMENT=development
ALLOWED_ORIGINS=["http://localhost:3000"]
```

### Webapp (.env.local)
```env
# For local development
NEXT_PUBLIC_API_URL=http://localhost:8001

# For Docker development (set automatically)
NEXT_PUBLIC_API_URL=http://api:8000
```

### Docker Environment
The Docker setup automatically configures:
- API accessible at `http://localhost:8001`
- Webapp accessible at `http://localhost:3000`
- Internal service communication via Docker network
- Environment-specific API URLs

## Production Deployment

The application is fully containerized and production-ready:

### Quick Production Setup
```bash
# Clone and build
git clone <your-repo-url>
cd tarmactrack

# Production deployment
make prod-up
```

### Coolify Deployment (Recommended for VPS)

For easy testing deployment on a VPS with Coolify:

1. **Prerequisites:**
   - Coolify instance running on your VPS
   - [TimescaleDB Cloud](https://cloud.timescale.com) account (free tier available)

2. **Quick Setup:**
   - Set up TimescaleDB Cloud and get your connection string
   - Deploy to Coolify (initial deployment will generate domains)
   - Update `docker-compose.prod.yml` with the generated API domain in both build args and environment
   - Redeploy for the webapp to use the correct API URL

3. **Key Configuration:**
   ```yaml
   webapp:
     build:
       args:
         - NEXT_PUBLIC_API_URL=http://your-api-domain.sslip.io
     environment:
       - NEXT_PUBLIC_API_URL=http://your-api-domain.sslip.io
   ```

4. **What you get:**
   - Free Coolify-generated domains for testing
   - Automatic SSL certificates
   - Container health monitoring  
   - Auto-deployment from Git
   - Built-in database management (TimescaleDB Cloud)
   - Easy scaling and management

5. **Detailed Guide:**
   See [`COOLIFY-DEPLOYMENT.md`](./COOLIFY-DEPLOYMENT.md) for complete step-by-step instructions.

### Manual Production Setup
```bash
# Build optimized containers
docker-compose build

# Start in production mode
docker-compose -f docker-compose.yml up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

### Scaling
```bash
# Scale webapp instances
docker-compose up -d --scale webapp=3

# Or use Docker Swarm/Kubernetes for advanced orchestration
```

## Contributing

1. Keep webapp types in sync with API schemas
2. Follow the established project structure
3. Add tests for new API endpoints
4. Update documentation for new features
5. Use conventional commit messages

## License

This project is part of the TarmacTrack application.

## 🚀 Development Workflow

### 🏃‍♂️ Rapid Development Cycle

#### 1. Local Development (Fastest Iteration)

**For API Development:**
```bash
# Terminal 1 - Start database (if needed)
cd api
# Use local PostgreSQL or connect to TimescaleDB Cloud

# Terminal 2 - Start API with hot reload
cd api
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8001
# API available at http://localhost:8001
```

**For Frontend Development:**
```bash
# Terminal 3 - Start webapp with hot reload
cd webapp
npm install
npm run dev
# Webapp available at http://localhost:3000
# Automatically connects to API at localhost:8001
```

**Benefits:**
- ⚡ Instant reload on file changes
- 🐛 Full debugging capabilities
- 🔍 Easy to inspect database queries
- 📊 Direct access to API docs at `/docs`

#### 2. Docker Development Testing (Integration Testing)

Test the full stack with Docker to catch environment issues:

```bash
# Start both services in Docker
make dev-up
# or
docker-compose up -d

# Services available at:
# API: http://localhost:8001
# Webapp: http://localhost:3000

# View logs
docker-compose logs -f api
docker-compose logs -f webapp

# Stop when done
make down
```

**Benefits:**
- 🔄 Tests containerized environment
- 🌐 Validates service communication
- 📦 Catches dependency issues
- 🔍 Environment variable testing

### 🧪 Testing & Quality Assurance

#### 3. Pre-Deployment Testing

Before deploying to production, run comprehensive tests:

```bash
# Run API tests
make test
# or
cd api && python -m pytest

# Check for linting issues
cd webapp && npm run lint

# Build production containers locally
docker-compose -f docker-compose.yml build

# Test production build locally
docker-compose -f docker-compose.yml up -d
```

#### 4. Mock Deployment Testing (Coolify Staging)

Use your Coolify instance as a staging environment:

**Option A: Separate Staging Project**
1. Create a separate project in Coolify called `tarmactrack-staging`
2. Use a separate git branch (e.g., `staging`)
3. Test with staging database

**Option B: Same Project, Different Branch**
1. Create a `staging` branch
2. Update `docker-compose.prod.yml` with staging configurations
3. Deploy staging branch to your existing Coolify project
4. Test thoroughly before merging to `main`

```bash
# Create staging branch
git checkout -b staging

# Make any staging-specific changes
# (different database, debug settings, etc.)

# Deploy to Coolify staging
git push origin staging
```

### 🚢 Production Deployment

#### 5. Production Release Process

**Step 1: Prepare Release**
```bash
# Ensure you're on main branch
git checkout main
git pull origin main

# Update version numbers if needed
# Update CHANGELOG.md with new features

# Final local testing
make test
docker-compose -f docker-compose.yml build
```

**Step 2: Deploy to Production**
```bash
# Tag the release
git tag -a v1.2.0 -m "Release v1.2.0: Add new airport features"
git push origin v1.2.0

# Deploy to production
git push origin main
# Coolify automatically deploys
```

**Step 3: Post-Deployment Verification**
```bash
# Check health endpoints
curl https://your-api-domain.sslip.io/health

# Verify webapp is working
# Check browser network tab for proper API calls
# Test key user workflows

# Monitor logs in Coolify dashboard
```

### 🔄 Quick Commands Reference

```bash
# Development
make setup          # First-time setup
make dev            # Start local development
make dev-up         # Start Docker development
make logs           # View all logs
make test           # Run tests

# Docker Management
make build          # Build all containers
make up             # Start production containers
make down           # Stop all containers
make clean          # Clean up Docker resources

# Database
make api-shell      # Access API container shell
make webapp-shell   # Access webapp container shell

# Deployment
git push origin main     # Deploy to production
git push origin staging  # Deploy to staging
```

### 📋 Best Practices

#### Development Workflow
1. **Branch Strategy**: Use `main` for production, `staging` for testing, feature branches for development
2. **Environment Variables**: Keep secrets in Coolify environment settings, not in code
3. **Database**: Use TimescaleDB Cloud for both staging and production
4. **Testing**: Always test in Docker before deploying

#### Code Quality
1. **Type Safety**: Keep TypeScript types in sync between API and frontend
2. **Error Handling**: Test error scenarios in both API and frontend
3. **Performance**: Monitor API response times and frontend bundle size
4. **Security**: Regular dependency updates and security scanning

#### Deployment Safety
1. **Health Checks**: Always verify health endpoints after deployment
2. **Rollback Plan**: Keep previous git tags for quick rollbacks
3. **Monitoring**: Watch Coolify logs during and after deployment
4. **User Testing**: Test key user workflows after each deployment

### 🆘 Emergency Procedures

#### Quick Rollback
```bash
# Find last working tag
git tag -l

# Rollback to previous version
git checkout v1.1.0
git push origin main --force

# Or create hotfix
git checkout -b hotfix/critical-fix
# Make minimal fix
git commit -m "Critical hotfix"
git push origin hotfix/critical-fix
# Merge to main
```

#### Debug Production Issues
```bash
# Access container logs
# In Coolify dashboard or via SSH:
docker logs api-container-name
docker logs webapp-container-name

# Access container shell
docker exec -it api-container-name bash
docker exec -it webapp-container-name sh

# Check health endpoints
curl http://localhost:8000/health  # Inside API container
curl http://localhost:3000         # Inside webapp container
```

This workflow ensures rapid development while maintaining production reliability! 